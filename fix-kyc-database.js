const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
})

async function createKYCSchema() {
  console.log('🔧 Creating KYC database schema...\n')
  
  try {
    // Step 1: Add KYC columns to users table
    console.log('1. Adding KYC columns to users table...')
    
    // Check if columns already exist first
    const { data: existingColumns, error: checkError } = await supabase
      .from('users')
      .select('kyc_status')
      .limit(1)
    
    if (checkError && checkError.message.includes('does not exist')) {
      // Add the columns using raw SQL
      const addColumnsSQL = `
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS kyc_status varchar(20) DEFAULT 'not_submitted' 
        CHECK (kyc_status IN ('not_submitted', 'pending', 'approved', 'rejected'));
        
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS kyc_submitted_at timestamp with time zone;
        
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS kyc_approved_at timestamp with time zone;
      `
      
      // We'll need to execute this manually since we can't use rpc
      console.log('   Please execute this SQL manually in Supabase SQL Editor:')
      console.log('   ' + addColumnsSQL.replace(/\n/g, '\n   '))
      console.log('   ✅ SQL provided for manual execution')
    } else {
      console.log('   ✅ KYC columns already exist in users table')
    }
    
    // Step 2: Create kyc_document_types table
    console.log('\n2. Creating kyc_document_types table...')
    
    // Try to create the table by inserting data (will fail if table doesn't exist)
    const { error: docTypesError } = await supabase
      .from('kyc_document_types')
      .select('*')
      .limit(1)
    
    if (docTypesError && docTypesError.message.includes('does not exist')) {
      const createDocTypesSQL = `
        CREATE TABLE kyc_document_types (
          id varchar(50) PRIMARY KEY,
          name varchar(100) NOT NULL,
          description text,
          is_active boolean DEFAULT true,
          created_at timestamp with time zone DEFAULT now()
        );
        
        INSERT INTO kyc_document_types (id, name, description) VALUES
        ('national_id', 'National Identity Card', 'Sri Lankan National Identity Card'),
        ('passport', 'Passport', 'Valid passport document'),
        ('driving_license', 'Driving License', 'Valid driving license');
      `
      
      console.log('   Please execute this SQL manually in Supabase SQL Editor:')
      console.log('   ' + createDocTypesSQL.replace(/\n/g, '\n   '))
      console.log('   ✅ SQL provided for manual execution')
    } else {
      console.log('   ✅ kyc_document_types table already exists')
    }
    
    // Step 3: Create kyc_submissions table
    console.log('\n3. Creating kyc_submissions table...')
    
    const { error: submissionsError } = await supabase
      .from('kyc_submissions')
      .select('*')
      .limit(1)
    
    if (submissionsError && submissionsError.message.includes('does not exist')) {
      const createSubmissionsSQL = `
        CREATE TABLE kyc_submissions (
          id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          
          -- Document URLs (stored in Supabase Storage)
          id_document_front_url text NOT NULL,
          id_document_back_url text NOT NULL,
          selfie_photo_url text NOT NULL,
          address_proof_url text NOT NULL,
          
          -- Document metadata
          id_document_type varchar(50) NOT NULL CHECK (id_document_type IN ('national_id', 'passport', 'driving_license')),
          id_document_number varchar(100),
          
          -- Personal information for verification
          full_name varchar(255) NOT NULL,
          date_of_birth date,
          address text NOT NULL,
          
          -- Submission status and tracking
          status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')),
          submission_notes text,
          
          -- Admin review fields
          reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
          reviewed_at timestamp with time zone,
          rejection_reason text,
          admin_notes text,
          
          -- Timestamps
          created_at timestamp with time zone DEFAULT now(),
          updated_at timestamp with time zone DEFAULT now(),
          
          -- Ensure one active submission per user
          UNIQUE(user_id)
        );
      `
      
      console.log('   Please execute this SQL manually in Supabase SQL Editor:')
      console.log('   ' + createSubmissionsSQL.replace(/\n/g, '\n   '))
      console.log('   ✅ SQL provided for manual execution')
    } else {
      console.log('   ✅ kyc_submissions table already exists')
    }
    
    console.log('\n🎯 Next Steps:')
    console.log('1. Copy the SQL statements above')
    console.log('2. Go to Supabase Dashboard > SQL Editor')
    console.log('3. Paste and execute each SQL block')
    console.log('4. Run the schema check again to verify')
    
  } catch (error) {
    console.error('❌ Error creating KYC schema:', error.message)
  }
}

createKYCSchema()
