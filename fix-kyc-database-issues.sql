-- Fix KYC Database Issues
-- 1. Fix foreign key references to use public.users instead of auth.users
-- 2. Add proper RLS policies
-- 3. Ensure all tables are properly configured

-- =====================================================
-- STEP 1: FIX FOREIGN KEY REFERENCES
-- =====================================================

-- Drop existing foreign key constraints that reference auth.users
ALTER TABLE kyc_submissions DROP CONSTRAINT IF EXISTS kyc_submissions_user_id_fkey;
ALTER TABLE kyc_submissions DROP CONSTRAINT IF EXISTS kyc_submissions_reviewed_by_fkey;
ALTER TABLE kyc_status_history DROP CONSTRAINT IF EXISTS kyc_status_history_user_id_fkey;
ALTER TABLE kyc_status_history DROP CONSTRAINT IF EXISTS kyc_status_history_changed_by_fkey;

-- Add correct foreign key constraints referencing public.users
ALTER TABLE kyc_submissions
ADD CONSTRAINT kyc_submissions_user_id_fkey
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE kyc_submissions
ADD CONSTRAINT kyc_submissions_reviewed_by_fkey
FOREIGN KEY (reviewed_by) REFERENCES public.users(id) ON DELETE SET NULL;

ALTER TABLE kyc_status_history
ADD CONSTRAINT kyc_status_history_user_id_fkey
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE kyc_status_history
ADD CONSTRAINT kyc_status_history_changed_by_fkey
FOREIGN KEY (changed_by) REFERENCES public.users(id) ON DELETE SET NULL;

-- =====================================================
-- STEP 2: ENABLE RLS ON ALL KYC TABLES
-- =====================================================

-- Enable RLS on all KYC tables
ALTER TABLE kyc_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_document_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_status_history ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 3: DROP EXISTING POLICIES (IF ANY)
-- =====================================================

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can insert own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can update own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Admins can view all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Admins can update all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Everyone can read KYC document types" ON kyc_document_types;
DROP POLICY IF EXISTS "Admins can modify KYC document types" ON kyc_document_types;
DROP POLICY IF EXISTS "Users can view own KYC status history" ON kyc_status_history;
DROP POLICY IF EXISTS "Admins can view all KYC status history" ON kyc_status_history;
DROP POLICY IF EXISTS "System can insert KYC status history" ON kyc_status_history;

-- =====================================================
-- STEP 4: CREATE RLS POLICIES
-- =====================================================

-- KYC Submissions Policies
-- Users can view their own submissions
CREATE POLICY "Users can view own KYC submissions" ON kyc_submissions
    FOR SELECT USING (auth.uid()::text = user_id::text);

-- Users can insert their own submissions
CREATE POLICY "Users can insert own KYC submissions" ON kyc_submissions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- Users can update their own submissions (for resubmission)
CREATE POLICY "Users can update own KYC submissions" ON kyc_submissions
    FOR UPDATE USING (auth.uid()::text = user_id::text);

-- Admins can view all submissions
CREATE POLICY "Admins can view all KYC submissions" ON kyc_submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id::text = auth.uid()::text
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- Admins can update all submissions (for review)
CREATE POLICY "Admins can update all KYC submissions" ON kyc_submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id::text = auth.uid()::text
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- KYC Document Types Policies
-- Everyone can read document types
CREATE POLICY "Everyone can read KYC document types" ON kyc_document_types
    FOR SELECT USING (true);

-- Only admins can modify document types
CREATE POLICY "Admins can modify KYC document types" ON kyc_document_types
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- KYC Status History Policies
-- Users can view their own status history
CREATE POLICY "Users can view own KYC status history" ON kyc_status_history
    FOR SELECT USING (auth.uid()::text = user_id::text);

-- Admins can view all status history
CREATE POLICY "Admins can view all KYC status history" ON kyc_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- System can insert status history (for triggers)
CREATE POLICY "System can insert KYC status history" ON kyc_status_history
    FOR INSERT WITH CHECK (true);

-- =====================================================
-- 4. CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to check if user is KYC verified
CREATE OR REPLACE FUNCTION is_kyc_verified(user_id_param uuid)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = user_id_param 
        AND kyc_status = 'approved'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's KYC status
CREATE OR REPLACE FUNCTION get_user_kyc_status(user_id_param uuid)
RETURNS TABLE(
    kyc_status varchar(20),
    kyc_submitted_at timestamp with time zone,
    kyc_approved_at timestamp with time zone
) AS $$
BEGIN
    RETURN QUERY
    SELECT u.kyc_status, u.kyc_submitted_at, u.kyc_approved_at
    FROM public.users u
    WHERE u.id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 5: CREATE ADMIN VIEW FOR KYC MANAGEMENT
-- =====================================================

-- Drop existing view if it exists
DROP VIEW IF EXISTS admin_kyc_submissions;

-- Create a view that joins KYC submissions with user data for admin panel
CREATE VIEW admin_kyc_submissions AS
SELECT
    ks.*,
    u.full_name as user_full_name,
    u.email as user_email,
    u.phone as user_phone,
    u.created_at as user_created_at,
    reviewer.full_name as reviewer_name
FROM kyc_submissions ks
JOIN public.users u ON ks.user_id = u.id
LEFT JOIN public.users reviewer ON ks.reviewed_by = reviewer.id;

-- Grant access to admin view
GRANT SELECT ON admin_kyc_submissions TO authenticated;
GRANT SELECT ON admin_kyc_submissions TO service_role;

-- =====================================================
-- 6. UPDATE EXISTING DATA (IF ANY)
-- =====================================================

-- Update any existing submissions to ensure data consistency
UPDATE kyc_submissions 
SET updated_at = now() 
WHERE updated_at IS NULL;

-- =====================================================
-- 7. GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON kyc_submissions TO authenticated;
GRANT SELECT ON kyc_document_types TO authenticated;
GRANT SELECT, INSERT ON kyc_status_history TO authenticated;

-- Grant permissions to service role (for admin operations)
GRANT ALL ON kyc_submissions TO service_role;
GRANT ALL ON kyc_document_types TO service_role;
GRANT ALL ON kyc_status_history TO service_role;
GRANT ALL ON admin_kyc_submissions TO service_role;

-- =====================================================
-- 8. ADD INDEXES FOR PERFORMANCE
-- =====================================================

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_kyc_submissions_user_id ON kyc_submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_submissions_status ON kyc_submissions(status);
CREATE INDEX IF NOT EXISTS idx_kyc_submissions_created_at ON kyc_submissions(created_at);
CREATE INDEX IF NOT EXISTS idx_kyc_status_history_user_id ON kyc_status_history(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_status_history_submission_id ON kyc_status_history(kyc_submission_id);

-- =====================================================
-- 9. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE kyc_submissions IS 'KYC verification submissions with proper public.users references';
COMMENT ON TABLE kyc_status_history IS 'Audit trail for KYC status changes';
COMMENT ON VIEW admin_kyc_submissions IS 'Admin view for KYC management with user details';
COMMENT ON FUNCTION is_kyc_verified(uuid) IS 'Check if user has completed KYC verification';
COMMENT ON FUNCTION get_user_kyc_status(uuid) IS 'Get user KYC status details';

-- Verify the changes
SELECT 'KYC database fixes applied successfully!' as status;
