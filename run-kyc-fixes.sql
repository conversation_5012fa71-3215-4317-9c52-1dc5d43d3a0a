-- STEP 1: Fix Foreign Key References (Run this first)
-- Drop existing foreign key constraints that reference auth.users
ALTER TABLE kyc_submissions DROP CONSTRAINT IF EXISTS kyc_submissions_user_id_fkey;
ALTER TABLE kyc_submissions DROP CONSTRAINT IF EXISTS kyc_submissions_reviewed_by_fkey;
ALTER TABLE kyc_status_history DROP CONSTRAINT IF EXISTS kyc_status_history_user_id_fkey;
ALTER TABLE kyc_status_history DROP CONSTRAINT IF EXISTS kyc_status_history_changed_by_fkey;

-- Add correct foreign key constraints referencing public.users
ALTER TABLE kyc_submissions 
ADD CONSTRAINT kyc_submissions_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE kyc_submissions 
ADD CONSTRAINT kyc_submissions_reviewed_by_fkey 
FOREIGN KEY (reviewed_by) REFERENCES public.users(id) ON DELETE SET NULL;

ALTER TABLE kyc_status_history 
ADD CONSTRAINT kyc_status_history_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE kyc_status_history 
ADD CONSTRAINT kyc_status_history_changed_by_fkey 
FOREIGN KEY (changed_by) REFERENCES public.users(id) ON DELETE SET NULL;
