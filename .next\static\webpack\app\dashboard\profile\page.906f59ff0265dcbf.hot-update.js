"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx":
/*!****************************************************!*\
  !*** ./src/components/kyc/KYCVerificationForm.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KYCVerificationForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/kyc */ \"(app-pages-browser)/./src/lib/services/kyc.ts\");\n/* harmony import */ var _KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./KYCStatusBadge */ \"(app-pages-browser)/./src/components/kyc/KYCStatusBadge.tsx\");\n/* harmony import */ var _KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./KYCDocumentUpload */ \"(app-pages-browser)/./src/components/kyc/KYCDocumentUpload.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_premium__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/premium */ \"(app-pages-browser)/./src/components/ui/premium/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCVerificationForm(param) {\n    let { onSubmissionComplete } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // KYC Status\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [kycSubmission, setKycSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Form Data\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id_document_type: \"national_id\",\n        id_document_number: \"\",\n        full_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        submission_notes: \"\"\n    });\n    // Document Files\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [documentErrors, setDocumentErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Document Types\n    const [documentTypes, setDocumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchKYCData();\n            fetchDocumentTypes();\n        }\n    }, [\n        user\n    ]);\n    const fetchKYCData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [status, submission] = await Promise.all([\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCStatus(user.id),\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCSubmission(user.id)\n            ]);\n            setKycStatus(status);\n            setKycSubmission(submission);\n            // Pre-fill form with existing submission data\n            if (submission) {\n                setFormData({\n                    id_document_type: submission.id_document_type,\n                    id_document_number: submission.id_document_number || \"\",\n                    full_name: submission.full_name,\n                    date_of_birth: submission.date_of_birth || \"\",\n                    address: submission.address,\n                    submission_notes: submission.submission_notes || \"\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching KYC data:\", error);\n            setError(\"Failed to load KYC information\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        user\n    ]);\n    const fetchDocumentTypes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const types = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getKYCDocumentTypes();\n            setDocumentTypes(types);\n        } catch (error) {\n            console.error(\"Error fetching document types:\", error);\n        }\n    }, []);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setError(\"\");\n    }, []);\n    const handleDocumentSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((documentType, file)=>{\n        if (file) {\n            setDocuments((prev)=>({\n                    ...prev,\n                    [documentType]: file\n                }));\n            setDocumentErrors((prev)=>({\n                    ...prev,\n                    [documentType]: \"\"\n                }));\n        } else {\n            setDocuments((prev)=>{\n                const newDocs = {\n                    ...prev\n                };\n                delete newDocs[documentType];\n                return newDocs;\n            });\n        }\n    }, []);\n    const validateForm = ()=>{\n        const errors = {};\n        let isValid = true;\n        // Validate required fields\n        if (!formData.full_name.trim()) {\n            setError(\"Full name is required\");\n            isValid = false;\n        }\n        if (!formData.address.trim()) {\n            setError(\"Address is required\");\n            isValid = false;\n        }\n        // Validate required documents\n        const requiredDocs = [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ];\n        for (const docType of requiredDocs){\n            if (!documents[docType]) {\n                errors[docType] = \"This document is required\";\n                isValid = false;\n            }\n        }\n        setDocumentErrors(errors);\n        if (!isValid && !error) {\n            setError(\"Please fill in all required fields and upload all required documents\");\n        }\n        return isValid;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!user || !validateForm()) return;\n        try {\n            setSubmitting(true);\n            setError(\"\");\n            const kycDocuments = {\n                id_front: documents.id_front,\n                id_back: documents.id_back,\n                selfie: documents.selfie,\n                address_proof: documents.address_proof\n            };\n            await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.submitKYCApplication(user.id, formData, kycDocuments);\n            setSuccess(\"KYC application submitted successfully! We will review your documents and notify you of the result.\");\n            // Refresh KYC data\n            await fetchKYCData();\n            if (onSubmissionComplete) {\n                onSubmissionComplete();\n            }\n            // Clear form\n            setDocuments({});\n            setDocumentErrors({});\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to submit KYC application\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this);\n    }\n    const canSubmit = !kycSubmission || kycSubmission.status === \"rejected\";\n    const isVerified = (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) === \"approved\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCStatusCard, {\n                    status: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\",\n                    submittedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_submitted_at,\n                    approvedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_approved_at,\n                    rejectionReason: kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.rejection_reason\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                delay: 0.1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Verification Progress\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCProgress, {\n                            currentStatus: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            canSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.SlideInUp, {\n                delay: 0.2,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-primary-blue/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-primary-blue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit KYC Documents\" : \"Submit KYC Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Complete your identity verification to unlock all features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Full Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    value: formData.full_name,\n                                                    onChange: (e)=>handleInputChange(\"full_name\", e.target.value),\n                                                    placeholder: \"Enter your full name as per ID\",\n                                                    required: true,\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Date of Birth\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"date\",\n                                                    value: formData.date_of_birth,\n                                                    onChange: (e)=>handleInputChange(\"date_of_birth\", e.target.value),\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Type *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.id_document_type,\n                                                    onChange: (e)=>handleInputChange(\"id_document_type\", e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                    required: true,\n                                                    children: documentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type.id,\n                                                            children: type.name\n                                                        }, type.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    value: formData.id_document_number,\n                                                    onChange: (e)=>handleInputChange(\"id_document_number\", e.target.value),\n                                                    placeholder: \"Enter ID number\",\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Address *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.address,\n                                            onChange: (e)=>handleInputChange(\"address\", e.target.value),\n                                            placeholder: \"Enter your full address\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Required Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_front\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_front\", file),\n                                                    selectedFile: documents.id_front,\n                                                    error: documentErrors.id_front,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_back\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_back\", file),\n                                                    selectedFile: documents.id_back,\n                                                    error: documentErrors.id_back,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"selfie\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"selfie\", file),\n                                                    selectedFile: documents.selfie,\n                                                    error: documentErrors.selfie,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"address_proof\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"address_proof\", file),\n                                                    selectedFile: documents.address_proof,\n                                                    error: documentErrors.address_proof,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.submission_notes,\n                                            onChange: (e)=>handleInputChange(\"submission_notes\", e.target.value),\n                                            placeholder: \"Any additional information you'd like to provide\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                    children: [\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, this),\n                                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-green-600 bg-green-50 border border-green-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: success\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumButton, {\n                                        type: \"submit\",\n                                        disabled: submitting,\n                                        className: \"px-8 py-3\",\n                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 23\n                                                }, this),\n                                                (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit Documents\" : \"Submit for Verification\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCVerificationForm, \"ybfD82Qchy81H6vDVLYV20jKRic=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = KYCVerificationForm;\nvar _c;\n$RefreshReg$(_c, \"KYCVerificationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx\n"));

/***/ })

});