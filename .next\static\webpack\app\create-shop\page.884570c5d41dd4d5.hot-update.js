"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-shop/page",{

/***/ "(app-pages-browser)/./src/lib/services/kyc.ts":
/*!*********************************!*\
  !*** ./src/lib/services/kyc.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCService: function() { return /* binding */ KYCService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _kycStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kycStorage */ \"(app-pages-browser)/./src/lib/services/kycStorage.ts\");\n\n\nclass KYCService {\n    /**\n   * Get user's KYC status from users table\n   */ static async getUserKYCStatus(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"kyc_status, kyc_submitted_at, kyc_approved_at\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user KYC status:\", error);\n                // Return default status if columns don't exist yet\n                if (error.message.includes(\"column\") && error.message.includes(\"does not exist\")) {\n                    return {\n                        kyc_status: \"not_submitted\"\n                    };\n                }\n                throw new Error(\"Failed to fetch KYC status: \".concat(error.message));\n            }\n            return data || {\n                kyc_status: \"not_submitted\"\n            };\n        } catch (error) {\n            console.error(\"Error getting user KYC status:\", error);\n            // Return default status for any database errors\n            return {\n                kyc_status: \"not_submitted\"\n            };\n        }\n    }\n    /**\n   * Get user's KYC submission details\n   */ static async getUserKYCSubmission(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"*\").eq(\"user_id\", userId).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    // No submission found\n                    return null;\n                }\n                // Handle table not found error\n                if (error.message.includes(\"Could not find the table\") || error.message.includes(\"schema cache\")) {\n                    console.warn(\"KYC submissions table not found - database migration may be needed\");\n                    return null;\n                }\n                console.error(\"Error fetching KYC submission:\", error);\n                throw new Error(\"Failed to fetch KYC submission: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting user KYC submission:\", error);\n            // Return null for table not found errors instead of throwing\n            if (error instanceof Error && (error.message.includes(\"Could not find the table\") || error.message.includes(\"schema cache\"))) {\n                return null;\n            }\n            throw error;\n        }\n    }\n    /**\n   * Submit KYC application with documents\n   */ static async submitKYCApplication(userId, submissionData, documents) {\n        try {\n            // Check if user already has a submission\n            const existingSubmission = await this.getUserKYCSubmission(userId);\n            if (existingSubmission && existingSubmission.status !== \"rejected\") {\n                throw new Error(\"You already have a pending or approved KYC submission\");\n            }\n            // Prepare document uploads\n            const documentUploads = [\n                {\n                    file: documents.id_front,\n                    type: \"id_front\"\n                },\n                {\n                    file: documents.id_back,\n                    type: \"id_back\"\n                },\n                {\n                    file: documents.selfie,\n                    type: \"selfie\"\n                },\n                {\n                    file: documents.address_proof,\n                    type: \"address_proof\"\n                }\n            ];\n            // Upload all documents\n            console.log(\"Uploading KYC documents...\");\n            const uploadedDocuments = await _kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.uploadKYCDocuments(documentUploads, userId);\n            // If there's an existing rejected submission, delete it first\n            if (existingSubmission) {\n                await this.deleteKYCSubmission(existingSubmission.id);\n            }\n            // Create KYC submission record\n            const submissionRecord = {\n                user_id: userId,\n                id_document_front_url: uploadedDocuments.id_front,\n                id_document_back_url: uploadedDocuments.id_back,\n                selfie_photo_url: uploadedDocuments.selfie,\n                address_proof_url: uploadedDocuments.address_proof,\n                id_document_type: submissionData.id_document_type,\n                id_document_number: submissionData.id_document_number,\n                full_name: submissionData.full_name,\n                date_of_birth: submissionData.date_of_birth,\n                address: submissionData.address,\n                submission_notes: submissionData.submission_notes,\n                status: \"pending\"\n            };\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).insert(submissionRecord).select().single();\n            if (error) {\n                console.error(\"Error creating KYC submission:\", error);\n                throw new Error(\"Failed to submit KYC application: \".concat(error.message));\n            }\n            console.log(\"KYC application submitted successfully\");\n            return data;\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all KYC submissions for admin review\n   */ static async getAllKYCSubmissions() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, status = arguments.length > 2 ? arguments[2] : void 0, searchTerm = arguments.length > 3 ? arguments[3] : void 0;\n        try {\n            // First, try the direct join approach\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"\\n          *,\\n          user:users(full_name, email)\\n        \", {\n                count: \"exact\"\n            });\n            // Apply filters\n            if (status) {\n                query = query.eq(\"status\", status);\n            }\n            if (searchTerm) {\n                query = query.or(\"full_name.ilike.%\".concat(searchTerm, \"%,id_document_number.ilike.%\").concat(searchTerm, \"%\"));\n            }\n            // Apply pagination\n            const offset = (page - 1) * limit;\n            query = query.order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            const { data, error, count } = await query;\n            if (error) {\n                console.error(\"Error fetching KYC submissions with join:\", error);\n                // Fallback: Get submissions first, then get user data separately\n                console.log(\"Trying fallback approach...\");\n                let fallbackQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"*\", {\n                    count: \"exact\"\n                });\n                if (status) {\n                    fallbackQuery = fallbackQuery.eq(\"status\", status);\n                }\n                if (searchTerm) {\n                    fallbackQuery = fallbackQuery.or(\"full_name.ilike.%\".concat(searchTerm, \"%,id_document_number.ilike.%\").concat(searchTerm, \"%\"));\n                }\n                const fallbackOffset = (page - 1) * limit;\n                fallbackQuery = fallbackQuery.order(\"created_at\", {\n                    ascending: false\n                }).range(fallbackOffset, fallbackOffset + limit - 1);\n                const { data: submissions, error: submissionsError, count: submissionsCount } = await fallbackQuery;\n                if (submissionsError) {\n                    throw new Error(\"Failed to fetch KYC submissions: \".concat(submissionsError.message));\n                }\n                // Get user data for each submission\n                const submissionsWithUsers = await Promise.all((submissions || []).map(async (submission)=>{\n                    const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"full_name, email\").eq(\"id\", submission.user_id).single();\n                    if (userError) {\n                        console.warn(\"Could not fetch user data for submission \".concat(submission.id, \":\"), userError);\n                        return {\n                            ...submission,\n                            user: {\n                                full_name: \"Unknown User\",\n                                email: \"<EMAIL>\"\n                            }\n                        };\n                    }\n                    return {\n                        ...submission,\n                        user: userData\n                    };\n                }));\n                return {\n                    submissions: submissionsWithUsers,\n                    total: submissionsCount || 0\n                };\n            }\n            return {\n                submissions: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting all KYC submissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Review KYC submission (admin only)\n   */ static async reviewKYCSubmission(submissionId, reviewData, reviewerId) {\n        try {\n            const updateData = {\n                status: reviewData.status,\n                reviewed_by: reviewerId,\n                reviewed_at: new Date().toISOString(),\n                rejection_reason: reviewData.rejection_reason,\n                admin_notes: reviewData.admin_notes\n            };\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).update(updateData).eq(\"id\", submissionId).select().single();\n            if (error) {\n                console.error(\"Error reviewing KYC submission:\", error);\n                throw new Error(\"Failed to review KYC submission: \".concat(error.message));\n            }\n            console.log(\"KYC submission \".concat(submissionId, \" reviewed as \").concat(reviewData.status));\n            return data;\n        } catch (error) {\n            console.error(\"Error reviewing KYC submission:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get KYC status history for a user\n   */ static async getKYCStatusHistory(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_STATUS_HISTORY).select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching KYC status history:\", error);\n                throw new Error(\"Failed to fetch KYC status history: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC status history:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Check if user is KYC verified\n   */ static async isUserKYCVerified(userId) {\n        try {\n            const status = await this.getUserKYCStatus(userId);\n            return (status === null || status === void 0 ? void 0 : status.kyc_status) === \"approved\";\n        } catch (error) {\n            console.error(\"Error checking KYC verification status:\", error);\n            return false;\n        }\n    }\n    /**\n   * Get KYC document signed URLs for admin review\n   */ static async getKYCDocumentUrls(submission) {\n        try {\n            const filePaths = [\n                submission.id_document_front_url,\n                submission.id_document_back_url,\n                submission.selfie_photo_url,\n                submission.address_proof_url\n            ];\n            const signedUrls = await _kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.getKYCDocumentSignedUrls(filePaths, 3600) // 1 hour expiry\n            ;\n            return {\n                id_front_url: signedUrls[submission.id_document_front_url],\n                id_back_url: signedUrls[submission.id_document_back_url],\n                selfie_url: signedUrls[submission.selfie_photo_url],\n                address_proof_url: signedUrls[submission.address_proof_url]\n            };\n        } catch (error) {\n            console.error(\"Error getting KYC document URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC submission (for resubmission)\n   */ static async deleteKYCSubmission(submissionId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).delete().eq(\"id\", submissionId);\n            if (error) {\n                console.error(\"Error deleting KYC submission:\", error);\n                throw new Error(\"Failed to delete KYC submission: \".concat(error.message));\n            }\n        } catch (error) {\n            console.error(\"Error deleting KYC submission:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get KYC document types\n   */ static async getKYCDocumentTypes() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_DOCUMENT_TYPES).select(\"*\").eq(\"is_active\", true).order(\"name\");\n            if (error) {\n                console.error(\"Error fetching KYC document types:\", error);\n                throw new Error(\"Failed to fetch document types: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC document types:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kyc.ts\n"));

/***/ })

});