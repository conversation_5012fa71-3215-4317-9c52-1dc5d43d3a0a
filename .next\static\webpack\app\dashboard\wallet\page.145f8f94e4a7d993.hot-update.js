"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/wallet/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/wallet/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WalletPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_wallet_CommissionBreakdownCards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/wallet/CommissionBreakdownCards */ \"(app-pages-browser)/./src/components/wallet/CommissionBreakdownCards.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_services_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/storage */ \"(app-pages-browser)/./src/lib/services/storage.ts\");\n/* harmony import */ var _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/commissionSystem */ \"(app-pages-browser)/./src/lib/services/commissionSystem.ts\");\n/* harmony import */ var _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useKYCVerification */ \"(app-pages-browser)/./src/hooks/useKYCVerification.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/AlertContext */ \"(app-pages-browser)/./src/contexts/AlertContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { showAlert } = (0,_contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__.useAlert)();\n    const { requireKYCForAction } = (0,_hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__.useKYCCheck)();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transfers, setTransfers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [depositRequests, setDepositRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [withdrawalRequests, setWithdrawalRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalBalance: 0,\n        totalDeposits: 0,\n        transfersSent: 0,\n        transfersReceived: 0,\n        totalWithdrawals: 0,\n        pendingDeposits: 0\n    });\n    const [commissionBreakdown, setCommissionBreakdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        directCommission: 0,\n        levelCommission: 0,\n        rsmBonus: 0,\n        zmBonus: 0,\n        okdoiHeadCommission: 0,\n        totalCommissions: 0\n    });\n    const [extendedCommissionData, setExtendedCommissionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        directCommission: 0,\n        levelCommission: 0,\n        voucherCommission: 0,\n        festivalBonus: 0,\n        savingCommission: 0,\n        giftCenterCommission: 0,\n        entertainmentCommission: 0,\n        medicalCommission: 0,\n        educationCommission: 0,\n        creditCommission: 0,\n        // ZM specific\n        zmBonuses: 0,\n        petralAllowanceZM: 0,\n        leasingFacilityZM: 0,\n        phoneBillZM: 0,\n        // RSM specific\n        rsmBonuses: 0,\n        petralAllowanceRSM: 0,\n        leasingFacilityRSM: 0,\n        phoneBillRSM: 0,\n        // OKDOI Head specific\n        okdoiHeadCommission: 0,\n        totalCommissions: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_transactions\");\n    // Modal states\n    const [showTransferModal, setShowTransferModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDepositModal, setShowDepositModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWithdrawalModal, setShowWithdrawalModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Transfer form\n    const [transferForm, setTransferForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        receiverEmail: \"\",\n        amount: \"\",\n        description: \"\"\n    });\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Deposit form\n    const [depositForm, setDepositForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        depositor_name: \"\",\n        notes: \"\",\n        terms_accepted: false\n    });\n    const [depositProof, setDepositProof] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [depositLoading, setDepositLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Withdrawal form\n    const [withdrawalForm, setWithdrawalForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        bank_name: \"\",\n        account_number: \"\",\n        account_holder_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        branch: \"\",\n        notes: \"\"\n    });\n    const [withdrawalLoading, setWithdrawalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchWalletData();\n        }\n    }, [\n        user\n    ]);\n    const fetchWalletData = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            setError(null);\n            console.log(\"Fetching wallet data for user:\", user.id);\n            // Fetch wallet with error handling\n            const walletData = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserWallet(user.id);\n            setWallet(walletData);\n            console.log(\"Wallet data fetched:\", walletData);\n            // Fetch recent transactions with error handling\n            const { transactions: transactionsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getWalletTransactions(user.id, 1, 10);\n            setTransactions(transactionsData || []);\n            console.log(\"Transactions fetched:\", (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.length) || 0);\n            // Fetch recent transfers with error handling\n            const { transfers: transfersData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserTransfers(user.id, 1, 10);\n            setTransfers(transfersData || []);\n            console.log(\"Transfers fetched:\", (transfersData === null || transfersData === void 0 ? void 0 : transfersData.length) || 0);\n            // Fetch deposit requests with error handling\n            const { requests: depositsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserDepositRequests(user.id, 1, 10);\n            setDepositRequests(depositsData || []);\n            console.log(\"Deposit requests fetched:\", (depositsData === null || depositsData === void 0 ? void 0 : depositsData.length) || 0);\n            // Fetch withdrawal requests with error handling\n            const { requests: withdrawalsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserWithdrawalRequests(user.id, 1, 10);\n            setWithdrawalRequests(withdrawalsData || []);\n            console.log(\"Withdrawal requests fetched:\", (withdrawalsData === null || withdrawalsData === void 0 ? void 0 : withdrawalsData.length) || 0);\n            // Fetch commission breakdown\n            try {\n                const commissionData = await _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__.CommissionSystemService.getCommissionBreakdown(user.id);\n                setCommissionBreakdown(commissionData);\n                // Get real extended commission data from the database\n                const extendedData = await _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__.CommissionSystemService.getExtendedCommissionBreakdown(user.id);\n                setExtendedCommissionData(extendedData);\n                console.log(\"Commission breakdown fetched:\", commissionData);\n            } catch (commissionError) {\n                console.error(\"Error fetching commission breakdown:\", commissionError);\n            // Don't fail the entire load if commission data fails\n            }\n            // Calculate stats with null safety\n            const totalDeposits = (transactionsData || []).filter((t)=>t.transaction_type === \"deposit\" && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const transfersSent = (transfersData || []).filter((t)=>t.sender_id === user.id && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const transfersReceived = (transfersData || []).filter((t)=>t.receiver_id === user.id && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const totalWithdrawals = (transactionsData || []).filter((t)=>t.transaction_type === \"withdrawal\" && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const pendingDeposits = (depositsData || []).filter((d)=>d.status === \"pending\").reduce((sum, d)=>sum + (d.amount || 0), 0);\n            setStats({\n                totalBalance: (walletData === null || walletData === void 0 ? void 0 : walletData.balance) || 0,\n                totalDeposits,\n                transfersSent,\n                transfersReceived,\n                totalWithdrawals,\n                pendingDeposits\n            });\n            console.log(\"Wallet data fetch completed successfully\");\n        } catch (err) {\n            console.error(\"Error fetching wallet data:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load wallet data\");\n            // Reset data on error\n            setWallet(null);\n            setTransactions([]);\n            setTransfers([]);\n            setDepositRequests([]);\n            setWithdrawalRequests([]);\n            setStats({\n                totalBalance: 0,\n                totalDeposits: 0,\n                transfersSent: 0,\n                transfersReceived: 0,\n                totalWithdrawals: 0,\n                pendingDeposits: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTransfer = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Check KYC verification status\n        const canTransfer = await requireKYCForAction(\"p2p_transfer\");\n        if (!canTransfer) {\n            await showAlert({\n                title: \"KYC Verification Required\",\n                message: \"You must complete KYC verification before making P2P transfers. Please go to Profile > Identity Verification to submit your documents.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setTransferLoading(true);\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createP2PTransfer(user.id, transferForm.receiverEmail, parseFloat(transferForm.amount), transferForm.description || undefined);\n            setShowTransferModal(false);\n            setTransferForm({\n                receiverEmail: \"\",\n                amount: \"\",\n                description: \"\"\n            });\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Transfer completed successfully!\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Transfer failed\",\n                variant: \"danger\"\n            });\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    const handleDeposit = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Validation\n        if (!depositForm.terms_accepted) {\n            await showAlert({\n                title: \"Terms Required\",\n                message: \"Please accept the terms and conditions to proceed.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        if (!depositProof) {\n            await showAlert({\n                title: \"Proof Required\",\n                message: \"Please upload a deposit receipt or proof of payment.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setDepositLoading(true);\n            // Upload proof file first\n            let proofUrl = \"\";\n            if (depositProof) {\n                proofUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_6__.StorageService.uploadImage(depositProof, user.id);\n            }\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createDepositRequest(user.id, {\n                amount: parseFloat(depositForm.amount),\n                depositor_name: depositForm.depositor_name,\n                notes: depositForm.notes || undefined,\n                deposit_slip_url: proofUrl\n            });\n            setShowDepositModal(false);\n            setDepositForm({\n                amount: \"\",\n                depositor_name: \"\",\n                notes: \"\",\n                terms_accepted: false\n            });\n            setDepositProof(null);\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Deposit request submitted successfully! We will review it shortly.\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Failed to submit deposit request\",\n                variant: \"danger\"\n            });\n        } finally{\n            setDepositLoading(false);\n        }\n    };\n    const handleWithdrawal = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Check KYC verification status\n        const canWithdraw = await requireKYCForAction(\"wallet_withdrawal\");\n        if (!canWithdraw) {\n            await showAlert({\n                title: \"KYC Verification Required\",\n                message: \"You must complete KYC verification before making withdrawals. Please go to Profile > Identity Verification to submit your documents.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setWithdrawalLoading(true);\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createWithdrawalRequest(user.id, {\n                amount: parseFloat(withdrawalForm.amount),\n                bank_name: withdrawalForm.bank_name,\n                account_number: withdrawalForm.account_number,\n                account_holder_name: withdrawalForm.account_holder_name,\n                branch: withdrawalForm.branch || undefined,\n                notes: withdrawalForm.notes || undefined\n            });\n            setShowWithdrawalModal(false);\n            setWithdrawalForm({\n                amount: \"\",\n                bank_name: \"\",\n                account_number: \"\",\n                account_holder_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n                branch: \"\",\n                notes: \"\"\n            });\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Withdrawal request submitted successfully! We will process it shortly.\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Failed to submit withdrawal request\",\n                variant: \"danger\"\n            });\n        } finally{\n            setWithdrawalLoading(false);\n        }\n    };\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 16\n                }, this);\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 16\n                }, this);\n            case \"commission\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTransactionColor = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n            case \"commission\":\n                return \"text-green-600\";\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return \"text-red-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 456,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 455,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Error Loading Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 466,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 465,\n            columnNumber: 7\n        }, this);\n    }\n    if (!wallet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Wallet Not Found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Your wallet is being set up. Please try again in a moment.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 485,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 484,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"My Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage your funds and transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowDepositModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Funds\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTransferModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Send Money\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowWithdrawalModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Withdraw\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-blue to-secondary-blue rounded-xl p-8 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-100 mb-2\",\n                                            children: \"Available Balance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.totalDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Transfers Sent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.transfersSent)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Transfers Received\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.transfersReceived)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-6 w-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Withdrawals\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.totalWithdrawals)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Pending Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.pendingDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_CommissionBreakdownCards__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        data: extendedCommissionData,\n                        userType: user.user_type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 612,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                {\n                                    id: \"all_transactions\",\n                                    name: \"All Transactions\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"]\n                                },\n                                {\n                                    id: \"fund_transfers\",\n                                    name: \"Fund Transfers\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                                },\n                                {\n                                    id: \"deposits\",\n                                    name: \"Deposits\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                                },\n                                {\n                                    id: \"withdrawals\",\n                                    name: \"Withdrawals\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n                                },\n                                {\n                                    id: \"commissions\",\n                                    name: \"Commissions\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        tab.name\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            activeTab === \"all_transactions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"All Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.map((transaction)=>{\n                                                var _transaction_p2p_transfer_receiver, _transaction_p2p_transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                                                                            children: transaction.transaction_type.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 665,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.reference_type === \"p2p_transfer\" && transaction.p2p_transfer ? transaction.transaction_type === \"transfer_out\" ? \"Transfer to \".concat(((_transaction_p2p_transfer_receiver = transaction.p2p_transfer.receiver) === null || _transaction_p2p_transfer_receiver === void 0 ? void 0 : _transaction_p2p_transfer_receiver.full_name) || \"Unknown\") : \"Transfer from \".concat(((_transaction_p2p_transfer_sender = transaction.p2p_transfer.sender) === null || _transaction_p2p_transfer_sender === void 0 ? void 0 : _transaction_p2p_transfer_sender.full_name) || \"Unknown\") : transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transaction.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transaction.reference_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transaction.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium mr-2 \".concat(getTransactionColor(transaction.transaction_type)),\n                                                                            children: [\n                                                                                transaction.transaction_type.includes(\"in\") || transaction.transaction_type === \"deposit\" || transaction.transaction_type === \"refund\" ? \"+\" : \"-\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.amount)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(transaction.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Balance: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.balance_after)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transactions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transactions found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"fund_transfers\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"P2P Transfers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transfers.map((transfer)=>{\n                                                var _transfer_receiver, _transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                    children: transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: [\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"Sent to\" : \"Received from\",\n                                                                                \" \",\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? (_transfer_receiver = transfer.receiver) === null || _transfer_receiver === void 0 ? void 0 : _transfer_receiver.full_name : (_transfer_sender = transfer.sender) === null || _transfer_sender === void 0 ? void 0 : _transfer_sender.full_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transfer.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transfer.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transfer.reference_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 733,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transfer.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 737,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium mr-2 \".concat(transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"text-red-600\" : \"text-green-600\"),\n                                                                        children: [\n                                                                            transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"-\" : \"+\",\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transfer.amount)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    getStatusIcon(transfer.status)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transfer.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transfers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transfers found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"deposits\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            depositRequests.map((deposit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 773,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                                    children: [\n                                                                                        \"Bank Deposit - \",\n                                                                                        deposit.bank_name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 776,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        deposit.account_holder_name,\n                                                                                        \" (\",\n                                                                                        deposit.account_number,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 779,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 771,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-lg font-bold text-gray-900\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(deposit.amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                getStatusIcon(deposit.status),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-1 text-xs font-medium capitalize \".concat(deposit.status === \"approved\" ? \"text-green-600\" : deposit.status === \"rejected\" ? \"text-red-600\" : \"text-yellow-600\"),\n                                                                                    children: deposit.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 790,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 788,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        new Date(deposit.created_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                deposit.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-mono\",\n                                                                    children: [\n                                                                        \"Ref: \",\n                                                                        deposit.reference_number\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.transaction_reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Bank Reference: \",\n                                                                        deposit.transaction_reference\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Notes: \",\n                                                                        deposit.notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.admin_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Admin Notes: \",\n                                                                        deposit.admin_notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 811,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, deposit.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            depositRequests.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No deposit requests found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 765,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"withdrawals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Withdrawal Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            withdrawalRequests.map((withdrawal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: [\n                                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(withdrawal.amount),\n                                                                                    \" to \",\n                                                                                    withdrawal.bank_name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 834,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Account: \",\n                                                                                    withdrawal.account_number,\n                                                                                    \" (\",\n                                                                                    withdrawal.account_holder_name,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 837,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            withdrawal.branch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Branch: \",\n                                                                                    withdrawal.branch\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            withdrawal.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400 font-mono\",\n                                                                                children: [\n                                                                                    \"Ref: \",\n                                                                                    withdrawal.reference_number\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 844,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            withdrawal.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Notes: \",\n                                                                                    withdrawal.notes\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(withdrawal.status === \"processed\" ? \"bg-green-100 text-green-800\" : withdrawal.status === \"approved\" ? \"bg-blue-100 text-blue-800\" : withdrawal.status === \"rejected\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                            children: withdrawal.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 853,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 831,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1 ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        new Date(withdrawal.created_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                withdrawal.approved_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Approved: \",\n                                                                        new Date(withdrawal.approved_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                withdrawal.processed_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Processed: \",\n                                                                        new Date(withdrawal.processed_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                withdrawal.admin_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Admin Notes: \",\n                                                                        withdrawal.admin_notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, withdrawal.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            withdrawalRequests.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No withdrawal requests found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 879,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"commissions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Commission Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.filter((t)=>t.transaction_type === \"commission\").map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: \"Commission Earned\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 896,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 899,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transaction.reference_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                transaction.reference_id.slice(-8)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 903,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transaction.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 907,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium mr-2 text-green-600\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.amount)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 914,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(transaction.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 913,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Balance: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.balance_after)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 919,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            transactions.filter((t)=>t.transaction_type === \"commission\").length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"No commission transactions found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Commissions will appear here when you earn them from referrals or other activities.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this),\n            showTransferModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Send Money\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleTransfer,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Receiver Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 945,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            required: true,\n                                            value: transferForm.receiverEmail,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        receiverEmail: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Enter receiver's email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            max: (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0,\n                                            step: \"0.01\",\n                                            value: transferForm.amount,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Available: \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 972,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Description (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: transferForm.description,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"What's this for?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 976,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowTransferModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: transferLoading,\n                                            className: \"flex-1 px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 disabled:opacity-50\",\n                                            children: transferLoading ? \"Sending...\" : \"Send Money\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 988,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 943,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 941,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 940,\n                columnNumber: 9\n            }, this),\n            showDepositModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Request Cash Deposit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1013,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleDeposit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            step: \"0.01\",\n                                            value: depositForm.amount,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1015,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Depositor Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: depositForm.depositor_name,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        depositor_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Name of person who made the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Deposit Receipt/Proof *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            required: true,\n                                            accept: \"image/*,.pdf\",\n                                            onChange: (e)=>{\n                                                var _e_target_files;\n                                                const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                if (file) {\n                                                    // Validate file size (5MB max)\n                                                    if (file.size > 5 * 1024 * 1024) {\n                                                        showAlert({\n                                                            title: \"File Too Large\",\n                                                            message: \"Please select a file smaller than 5MB.\",\n                                                            variant: \"warning\"\n                                                        });\n                                                        e.target.value = \"\";\n                                                        return;\n                                                    }\n                                                    setDepositProof(file);\n                                                }\n                                            },\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Upload bank deposit slip, receipt, or proof of payment (Max 5MB)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1074,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: depositForm.notes,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            rows: 3,\n                                            placeholder: \"Additional information about the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1077,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1073,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"terms\",\n                                            required: true,\n                                            checked: depositForm.terms_accepted,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        terms_accepted: e.target.checked\n                                                    })),\n                                            className: \"mt-1 h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"terms\",\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    target: \"_blank\",\n                                                    className: \"text-primary-blue hover:underline\",\n                                                    children: \"Terms and Conditions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"and confirm that the deposit information provided is accurate. *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1094,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowDepositModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1103,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: depositLoading,\n                                            className: \"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                            children: depositLoading ? \"Submitting...\" : \"Submit Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1014,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 1012,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 1011,\n                columnNumber: 9\n            }, this),\n            showWithdrawalModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Request Withdrawal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1127,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleWithdrawal,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            min: \"1\",\n                                            max: (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0,\n                                            required: true,\n                                            value: withdrawalForm.amount,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter amount\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Available balance: \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1144,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1129,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Bank Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1150,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.bank_name,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        bank_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter bank name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1153,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Account Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.account_number,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        account_number: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter account number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Account Holder Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.account_holder_name,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        account_holder_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter account holder name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Branch (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: withdrawalForm.branch,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        branch: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter branch name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1195,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: withdrawalForm.notes,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            rows: 3,\n                                            placeholder: \"Any additional notes...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowWithdrawalModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1218,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: withdrawalLoading,\n                                            className: \"flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50\",\n                                            children: withdrawalLoading ? \"Submitting...\" : \"Submit Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1225,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1217,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1128,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 1126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 1125,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n        lineNumber: 502,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"IpoN2hFwEo051gKZFYjH7z8llE0=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__.useAlert,\n        _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__.useKYCCheck\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/wallet/page.tsx\n"));

/***/ })

});