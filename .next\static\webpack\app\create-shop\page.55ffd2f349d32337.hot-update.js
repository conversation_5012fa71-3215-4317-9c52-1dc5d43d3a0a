"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-shop/page",{

/***/ "(app-pages-browser)/./src/lib/services/kycStorage.ts":
/*!****************************************!*\
  !*** ./src/lib/services/kycStorage.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCStorageService: function() { return /* binding */ KYCStorageService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass KYCStorageService {\n    /**\n   * Ensure KYC bucket exists before operations\n   */ static async ensureBucketExists() {\n        try {\n            // Check if bucket exists\n            const { data: buckets, error: listError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.listBuckets();\n            if (listError) {\n                console.warn(\"Cannot list buckets (likely due to RLS policies):\", listError.message);\n                console.log(\"Assuming KYC bucket exists and continuing...\");\n                return;\n            }\n            const bucketExists = buckets === null || buckets === void 0 ? void 0 : buckets.some((bucket)=>bucket.name === this.BUCKET_NAME);\n            if (!bucketExists) {\n                console.log(\"KYC bucket '\".concat(this.BUCKET_NAME, \"' not found in bucket list\"));\n                console.log(\"This is expected if RLS policies restrict bucket listing\");\n                console.log(\"Assuming bucket exists and continuing with upload...\");\n                return;\n            }\n            console.log(\"KYC bucket '\".concat(this.BUCKET_NAME, \"' confirmed to exist\"));\n        } catch (error) {\n            console.warn(\"Error checking KYC bucket existence:\", error);\n            console.log(\"Assuming bucket exists and continuing with operations...\");\n        }\n    }\n    /**\n   * Validate KYC document file\n   */ static validateFile(file, documentType) {\n        // Check file size\n        if (file.size > this.MAX_FILE_SIZE) {\n            throw new Error(\"File size must be less than \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB\"));\n        }\n        // Check file type\n        if (!this.ALLOWED_TYPES.includes(file.type)) {\n            throw new Error(\"File type \".concat(file.type, \" is not supported. Allowed types: \").concat(this.ALLOWED_TYPES.join(\", \")));\n        }\n        // Additional validation for specific document types\n        if (documentType === \"selfie\" && file.type === \"application/pdf\") {\n            throw new Error(\"Selfie photos must be image files, not PDF\");\n        }\n        // Check if file name is reasonable\n        if (file.name.length > 255) {\n            throw new Error(\"File name is too long\");\n        }\n    }\n    /**\n   * Generate secure filename for KYC document\n   */ static generateSecureFileName(userId, documentType, originalName) {\n        var _originalName_split_pop;\n        const timestamp = Date.now();\n        const randomId = Math.random().toString(36).substring(2);\n        const fileExt = ((_originalName_split_pop = originalName.split(\".\").pop()) === null || _originalName_split_pop === void 0 ? void 0 : _originalName_split_pop.toLowerCase()) || \"jpg\";\n        // Create folder structure: userId/documentType/timestamp-randomId.ext\n        return \"\".concat(userId, \"/\").concat(documentType, \"/\").concat(timestamp, \"-\").concat(randomId, \".\").concat(fileExt);\n    }\n    /**\n   * Upload a single KYC document\n   */ static async uploadKYCDocument(file, userId, documentType) {\n        try {\n            // Ensure bucket exists\n            await this.ensureBucketExists();\n            // Validate file\n            this.validateFile(file, documentType);\n            // Generate secure filename\n            const fileName = this.generateSecureFileName(userId, documentType, file.name);\n            console.log(\"Uploading KYC document: \".concat(fileName, \" to bucket: \").concat(this.BUCKET_NAME));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).upload(fileName, file, {\n                cacheControl: \"3600\",\n                upsert: false // Don't overwrite existing files\n            });\n            if (error) {\n                console.error(\"KYC document upload error:\", error);\n                throw new Error(\"Upload failed: \".concat(error.message));\n            }\n            console.log(\"KYC document upload successful:\", data);\n            // For private buckets, we need to create signed URLs for access\n            // Return the file path for now, signed URL will be generated when needed\n            return fileName;\n        } catch (error) {\n            console.error(\"Error uploading KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upload multiple KYC documents\n   */ static async uploadKYCDocuments(documents, userId) {\n        const results = {};\n        try {\n            // Upload all documents concurrently\n            const uploadPromises = documents.map(async (doc)=>{\n                const filePath = await this.uploadKYCDocument(doc.file, userId, doc.type);\n                return {\n                    type: doc.type,\n                    filePath\n                };\n            });\n            const uploadResults = await Promise.all(uploadPromises);\n            // Build results object\n            uploadResults.forEach((result)=>{\n                results[result.type] = result.filePath;\n            });\n            return results;\n        } catch (error) {\n            console.error(\"Error uploading multiple KYC documents:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get signed URL for KYC document (for admin review)\n   */ static async getKYCDocumentSignedUrl(filePath) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).createSignedUrl(filePath, expiresIn);\n            if (error) {\n                console.error(\"Error creating signed URL:\", error);\n                throw new Error(\"Failed to create signed URL: \".concat(error.message));\n            }\n            return data.signedUrl;\n        } catch (error) {\n            console.error(\"Error getting KYC document signed URL:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC document (for cleanup or resubmission)\n   */ static async deleteKYCDocument(filePath) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).remove([\n                filePath\n            ]);\n            if (error) {\n                console.error(\"Error deleting KYC document:\", error);\n                throw new Error(\"Failed to delete document: \".concat(error.message));\n            }\n            console.log(\"KYC document deleted successfully:\", filePath);\n        } catch (error) {\n            console.error(\"Error deleting KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get multiple signed URLs for KYC documents\n   */ static async getKYCDocumentSignedUrls(filePaths) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const urlPromises = filePaths.map(async (filePath)=>{\n                const signedUrl = await this.getKYCDocumentSignedUrl(filePath, expiresIn);\n                return {\n                    filePath,\n                    signedUrl\n                };\n            });\n            const results = await Promise.all(urlPromises);\n            const urlMap = {};\n            results.forEach((result)=>{\n                urlMap[result.filePath] = result.signedUrl;\n            });\n            return urlMap;\n        } catch (error) {\n            console.error(\"Error getting multiple KYC document signed URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Validate document type enum\n   */ static isValidDocumentType(type) {\n        return [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ].includes(type);\n    }\n    /**\n   * Get human-readable document type name\n   */ static getDocumentTypeName(type) {\n        const names = {\n            id_front: \"ID Document (Front)\",\n            id_back: \"ID Document (Back)\",\n            selfie: \"Selfie Photo\",\n            address_proof: \"Address Proof\"\n        };\n        return names[type];\n    }\n    /**\n   * Get document type requirements\n   */ static getDocumentTypeRequirements(type) {\n        const requirements = {\n            id_front: \"Clear photo of the front side of your government-issued ID (National ID, Passport, or Driving License)\",\n            id_back: \"Clear photo of the back side of your government-issued ID\",\n            selfie: \"Clear selfie photo holding your ID document next to your face\",\n            address_proof: \"Recent utility bill, bank statement, or official document showing your address (PDF or image)\"\n        };\n        return requirements[type];\n    }\n}\nKYCStorageService.BUCKET_NAME = \"kyc-documents\";\nKYCStorageService.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for KYC documents\n;\nKYCStorageService.ALLOWED_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/webp\",\n    \"application/pdf\" // Allow PDF for address proof documents\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kycStorage.ts\n"));

/***/ })

});