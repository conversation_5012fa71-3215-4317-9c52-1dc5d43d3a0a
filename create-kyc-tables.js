const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
})

async function createKYCTables() {
  console.log('🔧 Creating KYC database tables...\n')
  
  try {
    // Step 1: Add KYC columns to users table
    console.log('1. Adding KYC columns to users table...')
    
    const addKYCColumnsSQL = `
      -- Add KYC status column to users table
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS kyc_status varchar(20) DEFAULT 'not_submitted' 
      CHECK (kyc_status IN ('not_submitted', 'pending', 'approved', 'rejected'));

      -- Add KYC timestamp columns
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS kyc_submitted_at timestamp with time zone;

      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS kyc_approved_at timestamp with time zone;
    `
    
    console.log('   Executing SQL for users table...')
    console.log('   SQL:', addKYCColumnsSQL)
    
    // Step 2: Create kyc_document_types table
    console.log('\n2. Creating kyc_document_types table...')
    
    const createDocTypesSQL = `
      CREATE TABLE IF NOT EXISTS kyc_document_types (
          id varchar(50) PRIMARY KEY,
          name varchar(100) NOT NULL,
          description text,
          is_active boolean DEFAULT true,
          created_at timestamp with time zone DEFAULT now()
      );

      -- Insert default document types
      INSERT INTO kyc_document_types (id, name, description) VALUES
      ('national_id', 'National Identity Card', 'Sri Lankan National Identity Card'),
      ('passport', 'Passport', 'Valid passport document'),
      ('driving_license', 'Driving License', 'Valid driving license')
      ON CONFLICT (id) DO NOTHING;
    `
    
    console.log('   Executing SQL for kyc_document_types table...')
    console.log('   SQL:', createDocTypesSQL)
    
    // Step 3: Create kyc_submissions table
    console.log('\n3. Creating kyc_submissions table...')
    
    const createSubmissionsSQL = `
      CREATE TABLE IF NOT EXISTS kyc_submissions (
          id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          
          -- Document URLs (stored in Supabase Storage)
          id_document_front_url text NOT NULL,
          id_document_back_url text NOT NULL,
          selfie_photo_url text NOT NULL,
          address_proof_url text NOT NULL,
          
          -- Document metadata
          id_document_type varchar(50) NOT NULL CHECK (id_document_type IN ('national_id', 'passport', 'driving_license')),
          id_document_number varchar(100),
          
          -- Personal information for verification
          full_name varchar(255) NOT NULL,
          date_of_birth date,
          address text NOT NULL,
          
          -- Submission status and tracking
          status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')),
          submission_notes text,
          
          -- Admin review fields
          reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
          reviewed_at timestamp with time zone,
          rejection_reason text,
          admin_notes text,
          
          -- Timestamps
          created_at timestamp with time zone DEFAULT now(),
          updated_at timestamp with time zone DEFAULT now(),
          
          -- Ensure one active submission per user
          UNIQUE(user_id)
      );
    `
    
    console.log('   Executing SQL for kyc_submissions table...')
    console.log('   SQL:', createSubmissionsSQL)
    
    // Step 4: Create kyc_status_history table
    console.log('\n4. Creating kyc_status_history table...')
    
    const createHistorySQL = `
      CREATE TABLE IF NOT EXISTS kyc_status_history (
          id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
          kyc_submission_id uuid REFERENCES kyc_submissions(id) ON DELETE CASCADE NOT NULL,
          user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          
          -- Status change tracking
          previous_status varchar(20),
          new_status varchar(20) NOT NULL,
          changed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
          change_reason text,
          admin_notes text,
          
          -- Timestamps
          created_at timestamp with time zone DEFAULT now()
      );
    `
    
    console.log('   Executing SQL for kyc_status_history table...')
    console.log('   SQL:', createHistorySQL)
    
    // Step 5: Create indexes
    console.log('\n5. Creating indexes...')
    
    const createIndexesSQL = `
      CREATE INDEX IF NOT EXISTS idx_kyc_submissions_user_id ON kyc_submissions(user_id);
      CREATE INDEX IF NOT EXISTS idx_kyc_submissions_status ON kyc_submissions(status);
      CREATE INDEX IF NOT EXISTS idx_kyc_submissions_created_at ON kyc_submissions(created_at);
      CREATE INDEX IF NOT EXISTS idx_kyc_status_history_submission_id ON kyc_status_history(kyc_submission_id);
      CREATE INDEX IF NOT EXISTS idx_kyc_status_history_user_id ON kyc_status_history(user_id);
      CREATE INDEX IF NOT EXISTS idx_users_kyc_status ON users(kyc_status);
    `
    
    console.log('   Executing SQL for indexes...')
    console.log('   SQL:', createIndexesSQL)
    
    // Combine all SQL into one script
    const fullSQL = `
      ${addKYCColumnsSQL}
      
      ${createDocTypesSQL}
      
      ${createSubmissionsSQL}
      
      ${createHistorySQL}
      
      ${createIndexesSQL}
    `
    
    console.log('\n📋 COMPLETE SQL SCRIPT TO EXECUTE IN SUPABASE SQL EDITOR:')
    console.log('=' .repeat(80))
    console.log(fullSQL)
    console.log('=' .repeat(80))
    
    console.log('\n🎯 INSTRUCTIONS:')
    console.log('1. Copy the SQL script above')
    console.log('2. Go to Supabase Dashboard > SQL Editor')
    console.log('3. Paste the entire script')
    console.log('4. Click "Run" to execute')
    console.log('5. Run the schema check again to verify tables were created')
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

createKYCTables()
