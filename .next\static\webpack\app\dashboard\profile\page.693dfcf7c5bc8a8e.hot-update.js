"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/credit-card.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreditCard; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst CreditCard = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CreditCard\", [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"14\",\n            x: \"2\",\n            y: \"5\",\n            rx: \"2\",\n            key: \"ynyp8z\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"2\",\n            x2: \"22\",\n            y1: \"10\",\n            y2: \"10\",\n            key: \"1b3vmo\"\n        }\n    ]\n]);\n //# sourceMappingURL=credit-card.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY3JlZGl0LWNhcmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxNQUFBQSxhQUFhQyxnRUFBZ0JBLENBQUMsY0FBYztJQUNoRDtRQUNFO1FBQ0E7WUFBRUMsT0FBTztZQUFNQyxRQUFRO1lBQU1DLEdBQUc7WUFBS0MsR0FBRztZQUFLQyxJQUFJO1lBQUtDLEtBQUs7UUFBUztLQUN0RTtJQUNBO1FBQUM7UUFBUTtZQUFFQyxJQUFJO1lBQUtDLElBQUk7WUFBTUMsSUFBSTtZQUFNQyxJQUFJO1lBQU1KLEtBQUs7UUFBQTtLQUFVO0NBQ2xFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvY3JlZGl0LWNhcmQudHM/NmVmMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENyZWRpdENhcmRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNtVmpkQ0IzYVdSMGFEMGlNakFpSUdobGFXZG9kRDBpTVRRaUlIZzlJaklpSUhrOUlqVWlJSEo0UFNJeUlpQXZQZ29nSUR4c2FXNWxJSGd4UFNJeUlpQjRNajBpTWpJaUlIa3hQU0l4TUNJZ2VUSTlJakV3SWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY3JlZGl0LWNhcmRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDcmVkaXRDYXJkID0gY3JlYXRlTHVjaWRlSWNvbignQ3JlZGl0Q2FyZCcsIFtcbiAgW1xuICAgICdyZWN0JyxcbiAgICB7IHdpZHRoOiAnMjAnLCBoZWlnaHQ6ICcxNCcsIHg6ICcyJywgeTogJzUnLCByeDogJzInLCBrZXk6ICd5bnlwOHonIH0sXG4gIF0sXG4gIFsnbGluZScsIHsgeDE6ICcyJywgeDI6ICcyMicsIHkxOiAnMTAnLCB5MjogJzEwJywga2V5OiAnMWIzdm1vJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBDcmVkaXRDYXJkO1xuIl0sIm5hbWVzIjpbIkNyZWRpdENhcmQiLCJjcmVhdGVMdWNpZGVJY29uIiwid2lkdGgiLCJoZWlnaHQiLCJ4IiwieSIsInJ4Iiwia2V5IiwieDEiLCJ4MiIsInkxIiwieTIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/upload.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Upload; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Upload = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Upload\", [\n    [\n        \"path\",\n        {\n            d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n            key: \"ih7n3h\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"17 8 12 3 7 8\",\n            key: \"t8dd8p\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"3\",\n            y2: \"15\",\n            key: \"widbto\"\n        }\n    ]\n]);\n //# sourceMappingURL=upload.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/profile/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/profile/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Calendar,Camera,CheckCircle,Edit2,Mail,MapPin,Phone,Save,Shield,Sparkles,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_ui_premium__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/premium */ \"(app-pages-browser)/./src/components/ui/premium/index.ts\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/storage */ \"(app-pages-browser)/./src/lib/services/storage.ts\");\n/* harmony import */ var _components_kyc_NewKYCVerificationPage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/kyc/NewKYCVerificationPage */ \"(app-pages-browser)/./src/components/kyc/NewKYCVerificationPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ProfilePage() {\n    var _user_full_name, _user_email;\n    _s();\n    const { user, updateProfile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadingAvatar, setUploadingAvatar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"profile\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        full_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        phone: (user === null || user === void 0 ? void 0 : user.phone) || \"\",\n        location: (user === null || user === void 0 ? void 0 : user.location) || \"\"\n    });\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    const handleSave = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            await updateProfile(formData);\n            setIsEditing(false);\n            setSuccess(\"Profile updated successfully!\");\n            setTimeout(()=>setSuccess(\"\"), 3000);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to update profile\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setFormData({\n            full_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n            phone: (user === null || user === void 0 ? void 0 : user.phone) || \"\",\n            location: (user === null || user === void 0 ? void 0 : user.location) || \"\"\n        });\n        setIsEditing(false);\n        setError(\"\");\n    };\n    const handleAvatarClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleAvatarChange = async (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file || !user) return;\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            setError(\"Please select a valid image file\");\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            setError(\"Image size must be less than 5MB\");\n            return;\n        }\n        try {\n            setUploadingAvatar(true);\n            setError(\"\");\n            // Upload the image\n            const avatarUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_6__.StorageService.uploadImage(file, user.id);\n            // Update user profile with new avatar URL\n            await updateProfile({\n                avatar_url: avatarUrl\n            });\n            setSuccess(\"Profile picture updated successfully!\");\n            setTimeout(()=>setSuccess(\"\"), 3000);\n        } catch (error) {\n            console.error(\"Error uploading avatar:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to upload profile picture\");\n        } finally{\n            setUploadingAvatar(false);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_3__.FadeIn, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_3__.PremiumCard, {\n                        variant: \"premium\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            className: \"p-3 bg-gradient-to-br from-primary-blue to-secondary-blue text-white\",\n                                            whileHover: {\n                                                scale: 1.1,\n                                                rotate: 5\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h1, {\n                                                    className: \"text-3xl font-bold font-heading text-gray-900\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.2\n                                                    },\n                                                    children: \"Profile Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                                    className: \"text-gray-600 flex items-center gap-2\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.3\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 text-primary-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Manage your personal information and preferences\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: !isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_3__.PremiumButton, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setIsEditing(true),\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 29\n                                            }, void 0),\n                                            children: \"Edit Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, \"edit\", false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        className: \"flex space-x-3\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_3__.PremiumButton, {\n                                                variant: \"outline\",\n                                                onClick: handleCancel,\n                                                disabled: loading,\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 29\n                                                }, void 0),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_3__.PremiumButton, {\n                                                variant: \"primary\",\n                                                onClick: handleSave,\n                                                loading: loading,\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 29\n                                                }, void 0),\n                                                children: \"Save Changes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, \"actions\", true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                    children: [\n                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            className: \"bg-gradient-to-r from-green-50 to-green-100 border border-green-200 text-green-800 px-6 py-4 shadow-lg\",\n                            initial: {\n                                opacity: 0,\n                                y: -20,\n                                scale: 0.95\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0,\n                                scale: 1\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20,\n                                scale: 0.95\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: 0.2,\n                                            type: \"spring\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            className: \"bg-gradient-to-r from-red-50 to-red-100 border border-red-200 text-red-800 px-6 py-4 shadow-lg\",\n                            initial: {\n                                opacity: 0,\n                                y: -20,\n                                scale: 0.95\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0,\n                                scale: 1\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20,\n                                scale: 0.95\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: 0.2,\n                                            type: \"spring\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_3__.FadeIn, {\n                    delay: 0.3,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"profile\"),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === \"profile\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Profile Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"verification\"),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === \"verification\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Identity Verification\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: [\n                        activeTab === \"profile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_3__.SlideInUp, {\n                                        delay: 0.2,\n                                        className: \"lg:col-span-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_3__.PremiumCard, {\n                                            variant: \"premium\",\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative inline-block mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                            className: \"relative\",\n                                                            whileHover: {\n                                                                scale: 1.05\n                                                            },\n                                                            transition: {\n                                                                duration: 0.2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-32 h-32 rounded-full overflow-hidden cursor-pointer group relative\",\n                                                                    onClick: handleAvatarClick,\n                                                                    children: user.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.img, {\n                                                                        src: user.avatar_url,\n                                                                        alt: \"Profile picture\",\n                                                                        className: \"w-full h-full object-cover object-center group-hover:opacity-80 transition-opacity\",\n                                                                        whileHover: {\n                                                                            scale: 1.1\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.3\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 23\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                        className: \"w-full h-full bg-gradient-to-br from-primary-blue to-secondary-blue flex items-center justify-center text-white text-3xl font-bold group-hover:opacity-80 transition-opacity\",\n                                                                        whileHover: {\n                                                                            scale: 1.1\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.3\n                                                                        },\n                                                                        children: ((_user_full_name = user.full_name) === null || _user_full_name === void 0 ? void 0 : _user_full_name.charAt(0)) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.charAt(0)) || \"U\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                                    onClick: handleAvatarClick,\n                                                                    disabled: uploadingAvatar,\n                                                                    className: \"absolute bottom-0 right-0 w-10 h-10 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                                                                    whileHover: {\n                                                                        scale: 1.1\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                                                                        mode: \"wait\",\n                                                                        children: uploadingAvatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                            className: \"w-5 h-5 border-2 border-primary-blue border-t-transparent\",\n                                                                            animate: {\n                                                                                rotate: 360\n                                                                            },\n                                                                            transition: {\n                                                                                duration: 1,\n                                                                                repeat: Infinity,\n                                                                                ease: \"linear\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                            initial: {\n                                                                                scale: 0\n                                                                            },\n                                                                            animate: {\n                                                                                scale: 1\n                                                                            },\n                                                                            exit: {\n                                                                                scale: 0\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                    className: \"absolute -top-2 -right-2 bg-green-500 text-white p-2 shadow-lg\",\n                                                                    initial: {\n                                                                        scale: 0\n                                                                    },\n                                                                    animate: {\n                                                                        scale: 1\n                                                                    },\n                                                                    transition: {\n                                                                        delay: 0.5,\n                                                                        type: \"spring\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            ref: fileInputRef,\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            onChange: handleAvatarChange,\n                                                            className: \"hidden\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h3, {\n                                                    className: \"text-2xl font-bold font-heading text-gray-900 mb-2\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 10\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.3\n                                                    },\n                                                    children: user.full_name || \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                                    className: \"text-gray-600 mb-6\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 10\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.4\n                                                    },\n                                                    children: user.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                            className: \"flex items-center justify-center text-sm text-gray-600 bg-gray-50 px-4 py-2\",\n                                                            initial: {\n                                                                opacity: 0,\n                                                                x: -20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                x: 0\n                                                            },\n                                                            transition: {\n                                                                delay: 0.5\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 text-primary-blue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                \"Member since \",\n                                                                user.created_at ? new Date(user.created_at).toLocaleDateString() : \"N/A\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        (user.role === \"admin\" || user.is_super_admin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                            className: \"inline-flex items-center px-3 py-2 text-sm font-bold bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800\",\n                                                            initial: {\n                                                                opacity: 0,\n                                                                scale: 0.8\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                scale: 1\n                                                            },\n                                                            transition: {\n                                                                delay: 0.6\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Admin Account\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                                    children: \"Personal Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 mt-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"Email Address\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 435,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-900 bg-gray-50 px-3 py-2 rounded-lg\",\n                                                                            children: user.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: \"Email cannot be changed\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 mt-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        label: \"Full Name\",\n                                                                        name: \"full_name\",\n                                                                        value: formData.full_name,\n                                                                        onChange: handleChange,\n                                                                        placeholder: \"Enter your full name\",\n                                                                        fullWidth: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 23\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Full Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 458,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-900\",\n                                                                                children: user.full_name || \"Not provided\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 461,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 mt-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        label: \"Phone Number\",\n                                                                        name: \"phone\",\n                                                                        value: formData.phone,\n                                                                        onChange: handleChange,\n                                                                        placeholder: \"Enter your phone number\",\n                                                                        fullWidth: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 23\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Phone Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-900\",\n                                                                                children: user.phone || \"Not provided\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Calendar_Camera_CheckCircle_Edit2_Mail_MapPin_Phone_Save_Shield_Sparkles_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 mt-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        label: \"Location\",\n                                                                        name: \"location\",\n                                                                        value: formData.location,\n                                                                        onChange: handleChange,\n                                                                        placeholder: \"Enter your city/location\",\n                                                                        fullWidth: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 23\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Location\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-900\",\n                                                                                children: user.location || \"Not provided\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, this)\n                        }, \"profile\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"verification\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            className: \"bg-gray-50 -mx-6 -mb-6 px-6 pb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_kyc_NewKYCVerificationPage__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 15\n                            }, this)\n                        }, \"verification\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"vxbmWYnzx3eTYlDWUzontkkjA4Q=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/profile/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/kyc/NewKYCVerificationPage.tsx":
/*!*******************************************************!*\
  !*** ./src/components/kyc/NewKYCVerificationPage.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewKYCVerificationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Camera,CheckCircle,CreditCard,FileText,Loader2,MapPin,Shield,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_newKyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/newKyc */ \"(app-pages-browser)/./src/lib/services/newKyc.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction NewKYCVerificationPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Stable refs to prevent re-renders\n    const formRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const initialFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        id_document_type: \"national_id\",\n        id_document_number: \"\",\n        full_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        submission_notes: \"\"\n    });\n    // State management with stable initial values\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // KYC data\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [kycSubmission, setKycSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documentTypes, setDocumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Form data - using object reference to prevent re-renders\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData.current);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [documentErrors, setDocumentErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [documentPreviews, setDocumentPreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Get current document requirements\n    const currentDocType = documentTypes.find((dt)=>dt.id === formData.id_document_type);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!user) return;\n        const loadKYCData = async ()=>{\n            try {\n                setLoading(true);\n                // Load all data in parallel\n                const [status, submission, docTypes] = await Promise.all([\n                    _lib_services_newKyc__WEBPACK_IMPORTED_MODULE_3__.NewKYCService.getUserKYCStatus(user.id),\n                    _lib_services_newKyc__WEBPACK_IMPORTED_MODULE_3__.NewKYCService.getUserKYCSubmission(user.id),\n                    _lib_services_newKyc__WEBPACK_IMPORTED_MODULE_3__.NewKYCService.getKYCDocumentTypes()\n                ]);\n                setKycStatus(status);\n                setKycSubmission(submission);\n                setDocumentTypes(docTypes.map((dt)=>({\n                        ...dt,\n                        requires_back_photo: dt.id !== \"passport\",\n                        requires_front_photo: true,\n                        requires_selfie: true,\n                        requires_address_proof: true\n                    })));\n                // Pre-fill form if submission exists\n                if (submission) {\n                    const newFormData = {\n                        id_document_type: submission.id_document_type,\n                        id_document_number: submission.id_document_number || \"\",\n                        full_name: submission.full_name,\n                        date_of_birth: submission.date_of_birth || \"\",\n                        address: submission.address,\n                        submission_notes: submission.submission_notes || \"\"\n                    };\n                    setFormData(newFormData);\n                    initialFormData.current = newFormData;\n                }\n            } catch (err) {\n                console.error(\"Error loading KYC data:\", err);\n                setError(\"Failed to load KYC information\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadKYCData();\n    }, [\n        user\n    ]);\n    // Stable input handlers\n    const handleInputChange = (field)=>(e)=>{\n            const value = e.target.value;\n            setFormData((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n            setError(\"\");\n        };\n    // Document upload handler\n    const handleDocumentUpload = (documentType)=>(e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            // Validate file\n            const maxSize = 10 * 1024 * 1024 // 10MB\n            ;\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/webp\",\n                \"application/pdf\"\n            ];\n            if (file.size > maxSize) {\n                setDocumentErrors((prev)=>({\n                        ...prev,\n                        [documentType]: \"File size must be less than 10MB\"\n                    }));\n                return;\n            }\n            if (!allowedTypes.includes(file.type)) {\n                setDocumentErrors((prev)=>({\n                        ...prev,\n                        [documentType]: \"Please upload a valid image or PDF file\"\n                    }));\n                return;\n            }\n            // Clear error and set file\n            setDocumentErrors((prev)=>({\n                    ...prev,\n                    [documentType]: \"\"\n                }));\n            setDocuments((prev)=>({\n                    ...prev,\n                    [documentType]: file\n                }));\n            // Create preview for images\n            if (file.type.startsWith(\"image/\")) {\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    setDocumentPreviews((prev)=>{\n                        var _e_target;\n                        return {\n                            ...prev,\n                            [documentType]: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                        };\n                    });\n                };\n                reader.readAsDataURL(file);\n            } else {\n                setDocumentPreviews((prev)=>({\n                        ...prev,\n                        [documentType]: \"\"\n                    }));\n            }\n        };\n    // Remove document\n    const removeDocument = (documentType)=>()=>{\n            setDocuments((prev)=>{\n                const newDocs = {\n                    ...prev\n                };\n                delete newDocs[documentType];\n                return newDocs;\n            });\n            setDocumentPreviews((prev)=>{\n                const newPreviews = {\n                    ...prev\n                };\n                delete newPreviews[documentType];\n                return newPreviews;\n            });\n            setDocumentErrors((prev)=>({\n                    ...prev,\n                    [documentType]: \"\"\n                }));\n        };\n    // Form validation\n    const validateForm = ()=>{\n        const errors = {};\n        let isValid = true;\n        // Validate required fields\n        if (!formData.full_name.trim()) {\n            setError(\"Full name is required\");\n            isValid = false;\n        }\n        if (!formData.address.trim()) {\n            setError(\"Address is required\");\n            isValid = false;\n        }\n        // Validate required documents based on document type\n        if (!documents.id_front) {\n            errors.id_front = \"ID front photo is required\";\n            isValid = false;\n        }\n        if ((currentDocType === null || currentDocType === void 0 ? void 0 : currentDocType.requires_back_photo) && !documents.id_back) {\n            errors.id_back = \"ID back photo is required\";\n            isValid = false;\n        }\n        if (!documents.selfie) {\n            errors.selfie = \"Selfie photo is required\";\n            isValid = false;\n        }\n        if (!documents.address_proof) {\n            errors.address_proof = \"Address proof document is required\";\n            isValid = false;\n        }\n        setDocumentErrors(errors);\n        if (!isValid && !error) {\n            setError(\"Please fill in all required fields and upload all required documents\");\n        }\n        return isValid;\n    };\n    // Form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!user || !validateForm()) return;\n        try {\n            setSubmitting(true);\n            setError(\"\");\n            // Prepare documents for upload\n            const kycDocuments = {\n                id_front: documents.id_front,\n                id_back: documents.id_back || null,\n                selfie: documents.selfie,\n                address_proof: documents.address_proof\n            };\n            await _lib_services_newKyc__WEBPACK_IMPORTED_MODULE_3__.NewKYCService.submitKYCApplication(user.id, formData, kycDocuments);\n            setSuccess(\"KYC application submitted successfully! We will review your documents and notify you of the result.\");\n            // Reload KYC data\n            const [status, submission] = await Promise.all([\n                _lib_services_newKyc__WEBPACK_IMPORTED_MODULE_3__.NewKYCService.getUserKYCStatus(user.id),\n                _lib_services_newKyc__WEBPACK_IMPORTED_MODULE_3__.NewKYCService.getUserKYCSubmission(user.id)\n            ]);\n            setKycStatus(status);\n            setKycSubmission(submission);\n            // Clear form\n            setDocuments({});\n            setDocumentPreviews({});\n            setDocumentErrors({});\n        } catch (err) {\n            console.error(\"Error submitting KYC application:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to submit KYC application\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary-blue mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading KYC verification...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n            lineNumber: 298,\n            columnNumber: 7\n        }, this);\n    }\n    const canSubmit = !kycSubmission || kycSubmission.status === \"rejected\";\n    const isVerified = (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) === \"approved\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-primary-blue/10 rounded-full mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-8 w-8 text-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Identity Verification\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Complete your KYC verification to unlock all features\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Verification Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2\",\n                                            children: isVerified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-700 font-medium\",\n                                                        children: \"Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-500 mr-2 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-700 font-medium\",\n                                                        children: \"Under Review\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) === \"rejected\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-700 font-medium\",\n                                                        children: \"Rejected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-700 font-medium\",\n                                                        children: \"Not Submitted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_submitted_at) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Submitted: \",\n                                                new Date(kycStatus.kyc_submitted_at).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this),\n                                        kycStatus.kyc_approved_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Approved: \",\n                                                new Date(kycStatus.kyc_approved_at).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.rejection_reason) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 font-medium\",\n                                    children: \"Rejection Reason:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-700 mt-1\",\n                                    children: kycSubmission.rejection_reason\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                canSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-6\",\n                            children: (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit Verification Documents\" : \"Submit Verification Documents\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            ref: formRef,\n                            onSubmit: handleSubmit,\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Personal Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"full_name\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Full Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"full_name\",\n                                                                    type: \"text\",\n                                                                    value: formData.full_name,\n                                                                    onChange: handleInputChange(\"full_name\"),\n                                                                    placeholder: \"Enter your full name as per ID\",\n                                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"date_of_birth\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Date of Birth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"date_of_birth\",\n                                                                    type: \"date\",\n                                                                    value: formData.date_of_birth,\n                                                                    onChange: handleInputChange(\"date_of_birth\"),\n                                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"id_document_type\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"ID Document Type *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"id_document_type\",\n                                                            value: formData.id_document_type,\n                                                            onChange: handleInputChange(\"id_document_type\"),\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                            required: true,\n                                                            children: documentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: type.id,\n                                                                    children: type.name\n                                                                }, type.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentDocType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm text-gray-500\",\n                                                            children: currentDocType.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"id_document_number\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"ID Document Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"id_document_number\",\n                                                                    type: \"text\",\n                                                                    value: formData.id_document_number,\n                                                                    onChange: handleInputChange(\"id_document_number\"),\n                                                                    placeholder: \"Enter ID number\",\n                                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"address\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Address *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"absolute left-3 top-3 h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"address\",\n                                                            value: formData.address,\n                                                            onChange: handleInputChange(\"address\"),\n                                                            placeholder: \"Enter your full address\",\n                                                            rows: 3,\n                                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Required Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentUploadCard, {\n                                                    title: \"ID Document (Front)\",\n                                                    description: \"Clear photo of the front side of your ID\",\n                                                    required: true,\n                                                    file: documents.id_front,\n                                                    preview: documentPreviews.id_front,\n                                                    error: documentErrors.id_front,\n                                                    onUpload: handleDocumentUpload(\"id_front\"),\n                                                    onRemove: removeDocument(\"id_front\"),\n                                                    accept: \"image/*\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-8 w-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this),\n                                                (currentDocType === null || currentDocType === void 0 ? void 0 : currentDocType.requires_back_photo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentUploadCard, {\n                                                    title: \"ID Document (Back)\",\n                                                    description: \"Clear photo of the back side of your ID\",\n                                                    required: true,\n                                                    file: documents.id_back,\n                                                    preview: documentPreviews.id_back,\n                                                    error: documentErrors.id_back,\n                                                    onUpload: handleDocumentUpload(\"id_back\"),\n                                                    onRemove: removeDocument(\"id_back\"),\n                                                    accept: \"image/*\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-8 w-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentUploadCard, {\n                                                    title: \"Selfie Photo\",\n                                                    description: \"Clear selfie holding your ID document next to your face\",\n                                                    required: true,\n                                                    file: documents.selfie,\n                                                    preview: documentPreviews.selfie,\n                                                    error: documentErrors.selfie,\n                                                    onUpload: handleDocumentUpload(\"selfie\"),\n                                                    onRemove: removeDocument(\"selfie\"),\n                                                    accept: \"image/*\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-8 w-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentUploadCard, {\n                                                    title: \"Address Proof\",\n                                                    description: \"Recent utility bill, bank statement, or official document showing your address\",\n                                                    required: true,\n                                                    file: documents.address_proof,\n                                                    preview: documentPreviews.address_proof,\n                                                    error: documentErrors.address_proof,\n                                                    onUpload: handleDocumentUpload(\"address_proof\"),\n                                                    onRemove: removeDocument(\"address_proof\"),\n                                                    accept: \"image/*,application/pdf\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-8 w-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"submission_notes\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"submission_notes\",\n                                            value: formData.submission_notes,\n                                            onChange: handleInputChange(\"submission_notes\"),\n                                            placeholder: \"Any additional information you'd like to provide\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                                    children: [\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            className: \"p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-red-500 mr-3 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 19\n                                        }, this),\n                                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            className: \"p-4 bg-green-50 border border-green-200 rounded-lg flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-500 mr-3 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-800\",\n                                                    children: success\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: submitting,\n                                        className: \"px-8 py-3 bg-primary-blue text-white font-medium rounded-lg hover:bg-primary-blue/90 focus:ring-2 focus:ring-primary-blue focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submit for Verification\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 11\n                }, this),\n                isVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-lg p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-12 w-12 text-green-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-green-900 mb-2\",\n                            children: \"Identity Verified!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-700\",\n                            children: \"Your identity has been successfully verified. You now have access to all features.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n            lineNumber: 312,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n        lineNumber: 311,\n        columnNumber: 5\n    }, this);\n}\n_s(NewKYCVerificationPage, \"2QVP84oe/HY7f6ZZZcW8+5t5Gkw=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = NewKYCVerificationPage;\nfunction DocumentUploadCard(param) {\n    let { title, description, required, file, preview, error, onUpload, onRemove, accept, icon } = param;\n    _s1();\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-2 border-dashed rounded-lg p-6 transition-colors \".concat(error ? \"border-red-300 bg-red-50\" : file ? \"border-green-300 bg-green-50\" : \"border-gray-300 hover:border-primary-blue hover:bg-gray-50\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 \".concat(error ? \"bg-red-100 text-red-600\" : file ? \"bg-green-100 text-green-600\" : \"bg-gray-100 text-gray-600\"),\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 653,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 mb-1\",\n                    children: [\n                        title,\n                        \" \",\n                        required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500\",\n                            children: \"*\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 32\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 661,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500 mb-4\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 664,\n                    columnNumber: 9\n                }, this),\n                file ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative inline-block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: preview,\n                                alt: \"Preview\",\n                                className: \"w-20 h-20 object-cover rounded-lg border\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-green-700 font-medium\",\n                                    children: file.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onRemove,\n                                    className: \"p-1 text-red-500 hover:text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>{\n                                var _inputRef_current;\n                                return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.click();\n                            },\n                            className: \"text-sm text-primary-blue hover:text-primary-blue/80\",\n                            children: \"Change File\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        var _inputRef_current;\n                        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.click();\n                    },\n                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:ring-2 focus:ring-primary-blue focus:ring-offset-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Camera_CheckCircle_CreditCard_FileText_Loader2_MapPin_Shield_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                            lineNumber: 701,\n                            columnNumber: 13\n                        }, this),\n                        \"Choose File\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    ref: inputRef,\n                    type: \"file\",\n                    accept: accept,\n                    onChange: onUpload,\n                    className: \"hidden\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 706,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n                    lineNumber: 715,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n            lineNumber: 652,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\NewKYCVerificationPage.tsx\",\n        lineNumber: 647,\n        columnNumber: 5\n    }, this);\n}\n_s1(DocumentUploadCard, \"iD9XNNsNOlNDckBemnvlLS+aHYk=\");\n_c1 = DocumentUploadCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewKYCVerificationPage\");\n$RefreshReg$(_c1, \"DocumentUploadCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/kyc/NewKYCVerificationPage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/kycStorage.ts":
/*!****************************************!*\
  !*** ./src/lib/services/kycStorage.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCStorageService: function() { return /* binding */ KYCStorageService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass KYCStorageService {\n    /**\n   * Ensure KYC bucket exists before operations\n   */ static async ensureBucketExists() {\n        try {\n            // Check if bucket exists\n            const { data: buckets, error: listError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.listBuckets();\n            if (listError) {\n                console.error(\"Error listing buckets:\", listError);\n                console.log(\"Assuming KYC bucket exists and continuing...\");\n                return;\n            }\n            const bucketExists = buckets === null || buckets === void 0 ? void 0 : buckets.some((bucket)=>bucket.name === this.BUCKET_NAME);\n            if (!bucketExists) {\n                console.log(\"Creating KYC bucket: \".concat(this.BUCKET_NAME));\n                const { error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.createBucket(this.BUCKET_NAME, {\n                    public: false,\n                    allowedMimeTypes: this.ALLOWED_TYPES,\n                    fileSizeLimit: this.MAX_FILE_SIZE\n                });\n                if (createError) {\n                    console.error(\"Error creating KYC bucket:\", createError);\n                    // If bucket creation fails due to RLS policies, assume it exists\n                    if (createError.message.includes(\"row-level security\") || createError.message.includes(\"already exists\")) {\n                        console.log(\"KYC bucket likely exists or creation blocked by RLS. Continuing...\");\n                        return;\n                    }\n                    throw new Error(\"Failed to create KYC storage bucket: \".concat(createError.message));\n                }\n                console.log(\"KYC bucket created successfully\");\n            }\n        } catch (error) {\n            console.error(\"Error ensuring KYC bucket exists:\", error);\n            // Don't throw error for bucket existence check - assume it exists\n            console.log(\"Continuing with KYC operations assuming bucket exists...\");\n        }\n    }\n    /**\n   * Validate KYC document file\n   */ static validateFile(file, documentType) {\n        // Check file size\n        if (file.size > this.MAX_FILE_SIZE) {\n            throw new Error(\"File size must be less than \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB\"));\n        }\n        // Check file type\n        if (!this.ALLOWED_TYPES.includes(file.type)) {\n            throw new Error(\"File type \".concat(file.type, \" is not supported. Allowed types: \").concat(this.ALLOWED_TYPES.join(\", \")));\n        }\n        // Additional validation for specific document types\n        if (documentType === \"selfie\" && file.type === \"application/pdf\") {\n            throw new Error(\"Selfie photos must be image files, not PDF\");\n        }\n        // Check if file name is reasonable\n        if (file.name.length > 255) {\n            throw new Error(\"File name is too long\");\n        }\n    }\n    /**\n   * Generate secure filename for KYC document\n   */ static generateSecureFileName(userId, documentType, originalName) {\n        var _originalName_split_pop;\n        const timestamp = Date.now();\n        const randomId = Math.random().toString(36).substring(2);\n        const fileExt = ((_originalName_split_pop = originalName.split(\".\").pop()) === null || _originalName_split_pop === void 0 ? void 0 : _originalName_split_pop.toLowerCase()) || \"jpg\";\n        // Create folder structure: userId/documentType/timestamp-randomId.ext\n        return \"\".concat(userId, \"/\").concat(documentType, \"/\").concat(timestamp, \"-\").concat(randomId, \".\").concat(fileExt);\n    }\n    /**\n   * Upload a single KYC document\n   */ static async uploadKYCDocument(file, userId, documentType) {\n        try {\n            // Ensure bucket exists\n            await this.ensureBucketExists();\n            // Validate file\n            this.validateFile(file, documentType);\n            // Generate secure filename\n            const fileName = this.generateSecureFileName(userId, documentType, file.name);\n            console.log(\"Uploading KYC document: \".concat(fileName, \" to bucket: \").concat(this.BUCKET_NAME));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).upload(fileName, file, {\n                cacheControl: \"3600\",\n                upsert: false // Don't overwrite existing files\n            });\n            if (error) {\n                console.error(\"KYC document upload error:\", error);\n                throw new Error(\"Upload failed: \".concat(error.message));\n            }\n            console.log(\"KYC document upload successful:\", data);\n            // For private buckets, we need to create signed URLs for access\n            // Return the file path for now, signed URL will be generated when needed\n            return fileName;\n        } catch (error) {\n            console.error(\"Error uploading KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upload multiple KYC documents\n   */ static async uploadKYCDocuments(documents, userId) {\n        const results = {};\n        try {\n            // Upload all documents concurrently\n            const uploadPromises = documents.map(async (doc)=>{\n                const filePath = await this.uploadKYCDocument(doc.file, userId, doc.type);\n                return {\n                    type: doc.type,\n                    filePath\n                };\n            });\n            const uploadResults = await Promise.all(uploadPromises);\n            // Build results object\n            uploadResults.forEach((result)=>{\n                results[result.type] = result.filePath;\n            });\n            return results;\n        } catch (error) {\n            console.error(\"Error uploading multiple KYC documents:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get signed URL for KYC document (for admin review)\n   */ static async getKYCDocumentSignedUrl(filePath) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).createSignedUrl(filePath, expiresIn);\n            if (error) {\n                console.error(\"Error creating signed URL:\", error);\n                throw new Error(\"Failed to create signed URL: \".concat(error.message));\n            }\n            return data.signedUrl;\n        } catch (error) {\n            console.error(\"Error getting KYC document signed URL:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC document (for cleanup or resubmission)\n   */ static async deleteKYCDocument(filePath) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).remove([\n                filePath\n            ]);\n            if (error) {\n                console.error(\"Error deleting KYC document:\", error);\n                throw new Error(\"Failed to delete document: \".concat(error.message));\n            }\n            console.log(\"KYC document deleted successfully:\", filePath);\n        } catch (error) {\n            console.error(\"Error deleting KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get multiple signed URLs for KYC documents\n   */ static async getKYCDocumentSignedUrls(filePaths) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const urlPromises = filePaths.map(async (filePath)=>{\n                const signedUrl = await this.getKYCDocumentSignedUrl(filePath, expiresIn);\n                return {\n                    filePath,\n                    signedUrl\n                };\n            });\n            const results = await Promise.all(urlPromises);\n            const urlMap = {};\n            results.forEach((result)=>{\n                urlMap[result.filePath] = result.signedUrl;\n            });\n            return urlMap;\n        } catch (error) {\n            console.error(\"Error getting multiple KYC document signed URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Validate document type enum\n   */ static isValidDocumentType(type) {\n        return [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ].includes(type);\n    }\n    /**\n   * Get human-readable document type name\n   */ static getDocumentTypeName(type) {\n        const names = {\n            id_front: \"ID Document (Front)\",\n            id_back: \"ID Document (Back)\",\n            selfie: \"Selfie Photo\",\n            address_proof: \"Address Proof\"\n        };\n        return names[type];\n    }\n    /**\n   * Get document type requirements\n   */ static getDocumentTypeRequirements(type) {\n        const requirements = {\n            id_front: \"Clear photo of the front side of your government-issued ID (National ID, Passport, or Driving License)\",\n            id_back: \"Clear photo of the back side of your government-issued ID\",\n            selfie: \"Clear selfie photo holding your ID document next to your face\",\n            address_proof: \"Recent utility bill, bank statement, or official document showing your address (PDF or image)\"\n        };\n        return requirements[type];\n    }\n}\nKYCStorageService.BUCKET_NAME = \"kyc-documents\";\nKYCStorageService.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for KYC documents\n;\nKYCStorageService.ALLOWED_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/webp\",\n    \"application/pdf\" // Allow PDF for address proof documents\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kycStorage.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/newKyc.ts":
/*!************************************!*\
  !*** ./src/lib/services/newKyc.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewKYCService: function() { return /* binding */ NewKYCService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _kycStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kycStorage */ \"(app-pages-browser)/./src/lib/services/kycStorage.ts\");\n\n\nclass NewKYCService {\n    /**\n   * Get user's KYC status from users table\n   */ static async getUserKYCStatus(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"kyc_status, kyc_submitted_at, kyc_approved_at\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user KYC status:\", error);\n                return {\n                    kyc_status: \"not_submitted\"\n                };\n            }\n            return data || {\n                kyc_status: \"not_submitted\"\n            };\n        } catch (error) {\n            console.error(\"Error getting user KYC status:\", error);\n            return {\n                kyc_status: \"not_submitted\"\n            };\n        }\n    }\n    /**\n   * Get user's KYC submission details\n   */ static async getUserKYCSubmission(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"*\").eq(\"user_id\", userId).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    // No submission found\n                    return null;\n                }\n                throw new Error(\"Failed to fetch KYC submission: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting user KYC submission:\", error);\n            return null;\n        }\n    }\n    /**\n   * Get available KYC document types\n   */ static async getKYCDocumentTypes() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_DOCUMENT_TYPES).select(\"*\").eq(\"is_active\", true).order(\"id\");\n            if (error) {\n                throw new Error(\"Failed to fetch document types: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC document types:\", error);\n            return [];\n        }\n    }\n    /**\n   * Submit KYC application with flexible document requirements\n   */ static async submitKYCApplication(userId, submissionData, documents) {\n        try {\n            // Validate document requirements based on type\n            this.validateDocumentRequirements(submissionData.id_document_type, documents);\n            // Check if user already has a submission\n            const existingSubmission = await this.getUserKYCSubmission(userId);\n            if (existingSubmission && existingSubmission.status !== \"rejected\") {\n                throw new Error(\"You already have a pending or approved KYC submission\");\n            }\n            // Upload documents to storage\n            const documentUrls = await this.uploadKYCDocuments(userId, documents);\n            // Prepare submission data\n            const kycSubmissionData = {\n                user_id: userId,\n                id_document_front_url: documentUrls.id_front,\n                id_document_back_url: documentUrls.id_back || null,\n                selfie_photo_url: documentUrls.selfie,\n                address_proof_url: documentUrls.address_proof,\n                id_document_type: submissionData.id_document_type,\n                id_document_number: submissionData.id_document_number || null,\n                full_name: submissionData.full_name,\n                date_of_birth: submissionData.date_of_birth || null,\n                address: submissionData.address,\n                submission_notes: submissionData.submission_notes || null,\n                status: \"pending\"\n            };\n            // Insert or update submission\n            let result;\n            if (existingSubmission) {\n                // Update existing rejected submission\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).update(kycSubmissionData).eq(\"user_id\", userId).select().single();\n                if (error) throw error;\n                result = data;\n            } else {\n                // Insert new submission\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).insert(kycSubmissionData).select().single();\n                if (error) throw error;\n                result = data;\n            }\n            console.log(\"KYC application submitted successfully:\", result);\n            return result;\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Validate document requirements based on document type\n   */ static validateDocumentRequirements(documentType, documents) {\n        // All document types require front, selfie, and address proof\n        if (!documents.id_front) {\n            throw new Error(\"ID front photo is required\");\n        }\n        if (!documents.selfie) {\n            throw new Error(\"Selfie photo is required\");\n        }\n        if (!documents.address_proof) {\n            throw new Error(\"Address proof document is required\");\n        }\n        // National ID and Driving License require back photo\n        if ((documentType === \"national_id\" || documentType === \"driving_license\") && !documents.id_back) {\n            throw new Error(\"ID back photo is required for this document type\");\n        }\n        // Passport doesn't require back photo\n        if (documentType === \"passport\" && documents.id_back) {\n            console.log(\"Note: Back photo not required for passport, but will be stored if provided\");\n        }\n    }\n    /**\n   * Upload KYC documents to storage\n   */ static async uploadKYCDocuments(userId, documents) {\n        try {\n            const uploadPromises = [];\n            // Upload front document\n            uploadPromises.push(_kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.uploadKYCDocument(documents.id_front, userId, \"id_front\").then((url)=>({\n                    type: \"id_front\",\n                    url\n                })));\n            // Upload back document if provided\n            if (documents.id_back) {\n                uploadPromises.push(_kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.uploadKYCDocument(documents.id_back, userId, \"id_back\").then((url)=>({\n                        type: \"id_back\",\n                        url\n                    })));\n            }\n            // Upload selfie\n            uploadPromises.push(_kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.uploadKYCDocument(documents.selfie, userId, \"selfie\").then((url)=>({\n                    type: \"selfie\",\n                    url\n                })));\n            // Upload address proof\n            uploadPromises.push(_kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.uploadKYCDocument(documents.address_proof, userId, \"address_proof\").then((url)=>({\n                    type: \"address_proof\",\n                    url\n                })));\n            const uploadResults = await Promise.all(uploadPromises);\n            // Convert to object\n            const documentUrls = {};\n            uploadResults.forEach((result)=>{\n                documentUrls[result.type] = result.url;\n            });\n            return documentUrls;\n        } catch (error) {\n            console.error(\"Error uploading KYC documents:\", error);\n            throw new Error(\"Failed to upload documents. Please try again.\");\n        }\n    }\n    /**\n   * Get KYC submission history for user\n   */ static async getKYCStatusHistory(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_STATUS_HISTORY).select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(\"Failed to fetch KYC status history: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC status history:\", error);\n            return [];\n        }\n    }\n    /**\n   * Check if user is KYC verified\n   */ static async isUserKYCVerified(userId) {\n        try {\n            const status = await this.getUserKYCStatus(userId);\n            return (status === null || status === void 0 ? void 0 : status.kyc_status) === \"approved\";\n        } catch (error) {\n            console.error(\"Error checking KYC verification status:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc2VydmljZXMvbmV3S3ljLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDtBQUNEO0FBdUR6QyxNQUFNRztJQUNYOztHQUVDLEdBQ0QsYUFBYUMsaUJBQWlCQyxNQUFjLEVBQTZCO1FBQ3ZFLElBQUk7WUFDRixNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTVAsbURBQVFBLENBQ25DUSxJQUFJLENBQUNQLGlEQUFNQSxDQUFDUSxLQUFLLEVBQ2pCQyxNQUFNLENBQUMsaURBQ1BDLEVBQUUsQ0FBQyxNQUFNTixRQUNUTyxNQUFNO1lBRVQsSUFBSUwsT0FBTztnQkFDVE0sUUFBUU4sS0FBSyxDQUFDLG1DQUFtQ0E7Z0JBQ2pELE9BQU87b0JBQUVPLFlBQVk7Z0JBQWdCO1lBQ3ZDO1lBRUEsT0FBT1IsUUFBUTtnQkFBRVEsWUFBWTtZQUFnQjtRQUMvQyxFQUFFLE9BQU9QLE9BQU87WUFDZE0sUUFBUU4sS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaEQsT0FBTztnQkFBRU8sWUFBWTtZQUFnQjtRQUN2QztJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhQyxxQkFBcUJWLE1BQWMsRUFBaUM7UUFDL0UsSUFBSTtZQUNGLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNUCxtREFBUUEsQ0FDbkNRLElBQUksQ0FBQ1AsaURBQU1BLENBQUNlLGVBQWUsRUFDM0JOLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsV0FBV04sUUFDZE8sTUFBTTtZQUVULElBQUlMLE9BQU87Z0JBQ1QsSUFBSUEsTUFBTVUsSUFBSSxLQUFLLFlBQVk7b0JBQzdCLHNCQUFzQjtvQkFDdEIsT0FBTztnQkFDVDtnQkFDQSxNQUFNLElBQUlDLE1BQU0sbUNBQWlELE9BQWRYLE1BQU1ZLE9BQU87WUFDbEU7WUFFQSxPQUFPYjtRQUNULEVBQUUsT0FBT0MsT0FBTztZQUNkTSxRQUFRTixLQUFLLENBQUMsc0NBQXNDQTtZQUNwRCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYWEsc0JBQWtEO1FBQzdELElBQUk7WUFDRixNQUFNLEVBQUVkLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTVAsbURBQVFBLENBQ25DUSxJQUFJLENBQUNQLGlEQUFNQSxDQUFDb0Isa0JBQWtCLEVBQzlCWCxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLGFBQWEsTUFDaEJXLEtBQUssQ0FBQztZQUVULElBQUlmLE9BQU87Z0JBQ1QsTUFBTSxJQUFJVyxNQUFNLG1DQUFpRCxPQUFkWCxNQUFNWSxPQUFPO1lBQ2xFO1lBRUEsT0FBT2IsUUFBUSxFQUFFO1FBQ25CLEVBQUUsT0FBT0MsT0FBTztZQUNkTSxRQUFRTixLQUFLLENBQUMscUNBQXFDQTtZQUNuRCxPQUFPLEVBQUU7UUFDWDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhZ0IscUJBQ1hsQixNQUFjLEVBQ2RtQixjQUFpQyxFQUNqQ0MsU0FBdUIsRUFDQztRQUN4QixJQUFJO1lBQ0YsK0NBQStDO1lBQy9DLElBQUksQ0FBQ0MsNEJBQTRCLENBQUNGLGVBQWVHLGdCQUFnQixFQUFFRjtZQUVuRSx5Q0FBeUM7WUFDekMsTUFBTUcscUJBQXFCLE1BQU0sSUFBSSxDQUFDYixvQkFBb0IsQ0FBQ1Y7WUFDM0QsSUFBSXVCLHNCQUFzQkEsbUJBQW1CQyxNQUFNLEtBQUssWUFBWTtnQkFDbEUsTUFBTSxJQUFJWCxNQUFNO1lBQ2xCO1lBRUEsOEJBQThCO1lBQzlCLE1BQU1ZLGVBQWUsTUFBTSxJQUFJLENBQUNDLGtCQUFrQixDQUFDMUIsUUFBUW9CO1lBRTNELDBCQUEwQjtZQUMxQixNQUFNTyxvQkFBb0I7Z0JBQ3hCQyxTQUFTNUI7Z0JBQ1Q2Qix1QkFBdUJKLGFBQWFLLFFBQVE7Z0JBQzVDQyxzQkFBc0JOLGFBQWFPLE9BQU8sSUFBSTtnQkFDOUNDLGtCQUFrQlIsYUFBYVMsTUFBTTtnQkFDckNDLG1CQUFtQlYsYUFBYVcsYUFBYTtnQkFDN0NkLGtCQUFrQkgsZUFBZUcsZ0JBQWdCO2dCQUNqRGUsb0JBQW9CbEIsZUFBZWtCLGtCQUFrQixJQUFJO2dCQUN6REMsV0FBV25CLGVBQWVtQixTQUFTO2dCQUNuQ0MsZUFBZXBCLGVBQWVvQixhQUFhLElBQUk7Z0JBQy9DQyxTQUFTckIsZUFBZXFCLE9BQU87Z0JBQy9CQyxrQkFBa0J0QixlQUFlc0IsZ0JBQWdCLElBQUk7Z0JBQ3JEakIsUUFBUTtZQUNWO1lBRUEsOEJBQThCO1lBQzlCLElBQUlrQjtZQUNKLElBQUluQixvQkFBb0I7Z0JBQ3RCLHNDQUFzQztnQkFDdEMsTUFBTSxFQUFFdEIsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNUCxtREFBUUEsQ0FDbkNRLElBQUksQ0FBQ1AsaURBQU1BLENBQUNlLGVBQWUsRUFDM0JnQyxNQUFNLENBQUNoQixtQkFDUHJCLEVBQUUsQ0FBQyxXQUFXTixRQUNkSyxNQUFNLEdBQ05FLE1BQU07Z0JBRVQsSUFBSUwsT0FBTyxNQUFNQTtnQkFDakJ3QyxTQUFTekM7WUFDWCxPQUFPO2dCQUNMLHdCQUF3QjtnQkFDeEIsTUFBTSxFQUFFQSxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1QLG1EQUFRQSxDQUNuQ1EsSUFBSSxDQUFDUCxpREFBTUEsQ0FBQ2UsZUFBZSxFQUMzQmlDLE1BQU0sQ0FBQ2pCLG1CQUNQdEIsTUFBTSxHQUNORSxNQUFNO2dCQUVULElBQUlMLE9BQU8sTUFBTUE7Z0JBQ2pCd0MsU0FBU3pDO1lBQ1g7WUFFQU8sUUFBUXFDLEdBQUcsQ0FBQywyQ0FBMkNIO1lBQ3ZELE9BQU9BO1FBQ1QsRUFBRSxPQUFPeEMsT0FBTztZQUNkTSxRQUFRTixLQUFLLENBQUMscUNBQXFDQTtZQUNuRCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE9BQWVtQiw2QkFDYnlCLFlBQW9CLEVBQ3BCMUIsU0FBdUIsRUFDakI7UUFDTiw4REFBOEQ7UUFDOUQsSUFBSSxDQUFDQSxVQUFVVSxRQUFRLEVBQUU7WUFDdkIsTUFBTSxJQUFJakIsTUFBTTtRQUNsQjtRQUNBLElBQUksQ0FBQ08sVUFBVWMsTUFBTSxFQUFFO1lBQ3JCLE1BQU0sSUFBSXJCLE1BQU07UUFDbEI7UUFDQSxJQUFJLENBQUNPLFVBQVVnQixhQUFhLEVBQUU7WUFDNUIsTUFBTSxJQUFJdkIsTUFBTTtRQUNsQjtRQUVBLHFEQUFxRDtRQUNyRCxJQUFJLENBQUNpQyxpQkFBaUIsaUJBQWlCQSxpQkFBaUIsaUJBQWdCLEtBQU0sQ0FBQzFCLFVBQVVZLE9BQU8sRUFBRTtZQUNoRyxNQUFNLElBQUluQixNQUFNO1FBQ2xCO1FBRUEsc0NBQXNDO1FBQ3RDLElBQUlpQyxpQkFBaUIsY0FBYzFCLFVBQVVZLE9BQU8sRUFBRTtZQUNwRHhCLFFBQVFxQyxHQUFHLENBQUM7UUFDZDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFxQm5CLG1CQUNuQjFCLE1BQWMsRUFDZG9CLFNBQXVCLEVBQ1U7UUFDakMsSUFBSTtZQUNGLE1BQU0yQixpQkFBMkQsRUFBRTtZQUVuRSx3QkFBd0I7WUFDeEJBLGVBQWVDLElBQUksQ0FDakJuRCwwREFBaUJBLENBQUNvRCxpQkFBaUIsQ0FBQzdCLFVBQVVVLFFBQVEsRUFBRTlCLFFBQVEsWUFDN0RrRCxJQUFJLENBQUNDLENBQUFBLE1BQVE7b0JBQUVDLE1BQU07b0JBQVlEO2dCQUFJO1lBRzFDLG1DQUFtQztZQUNuQyxJQUFJL0IsVUFBVVksT0FBTyxFQUFFO2dCQUNyQmUsZUFBZUMsSUFBSSxDQUNqQm5ELDBEQUFpQkEsQ0FBQ29ELGlCQUFpQixDQUFDN0IsVUFBVVksT0FBTyxFQUFFaEMsUUFBUSxXQUM1RGtELElBQUksQ0FBQ0MsQ0FBQUEsTUFBUTt3QkFBRUMsTUFBTTt3QkFBV0Q7b0JBQUk7WUFFM0M7WUFFQSxnQkFBZ0I7WUFDaEJKLGVBQWVDLElBQUksQ0FDakJuRCwwREFBaUJBLENBQUNvRCxpQkFBaUIsQ0FBQzdCLFVBQVVjLE1BQU0sRUFBRWxDLFFBQVEsVUFDM0RrRCxJQUFJLENBQUNDLENBQUFBLE1BQVE7b0JBQUVDLE1BQU07b0JBQVVEO2dCQUFJO1lBR3hDLHVCQUF1QjtZQUN2QkosZUFBZUMsSUFBSSxDQUNqQm5ELDBEQUFpQkEsQ0FBQ29ELGlCQUFpQixDQUFDN0IsVUFBVWdCLGFBQWEsRUFBRXBDLFFBQVEsaUJBQ2xFa0QsSUFBSSxDQUFDQyxDQUFBQSxNQUFRO29CQUFFQyxNQUFNO29CQUFpQkQ7Z0JBQUk7WUFHL0MsTUFBTUUsZ0JBQWdCLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQ1I7WUFFeEMsb0JBQW9CO1lBQ3BCLE1BQU10QixlQUF1QyxDQUFDO1lBQzlDNEIsY0FBY0csT0FBTyxDQUFDZCxDQUFBQTtnQkFDcEJqQixZQUFZLENBQUNpQixPQUFPVSxJQUFJLENBQUMsR0FBR1YsT0FBT1MsR0FBRztZQUN4QztZQUVBLE9BQU8xQjtRQUNULEVBQUUsT0FBT3ZCLE9BQU87WUFDZE0sUUFBUU4sS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaEQsTUFBTSxJQUFJVyxNQUFNO1FBQ2xCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWE0QyxvQkFBb0J6RCxNQUFjLEVBQUU7UUFDL0MsSUFBSTtZQUNGLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNUCxtREFBUUEsQ0FDbkNRLElBQUksQ0FBQ1AsaURBQU1BLENBQUM4RCxrQkFBa0IsRUFDOUJyRCxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFdBQVdOLFFBQ2RpQixLQUFLLENBQUMsY0FBYztnQkFBRTBDLFdBQVc7WUFBTTtZQUUxQyxJQUFJekQsT0FBTztnQkFDVCxNQUFNLElBQUlXLE1BQU0sdUNBQXFELE9BQWRYLE1BQU1ZLE9BQU87WUFDdEU7WUFFQSxPQUFPYixRQUFRLEVBQUU7UUFDbkIsRUFBRSxPQUFPQyxPQUFPO1lBQ2RNLFFBQVFOLEtBQUssQ0FBQyxxQ0FBcUNBO1lBQ25ELE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWEwRCxrQkFBa0I1RCxNQUFjLEVBQW9CO1FBQy9ELElBQUk7WUFDRixNQUFNd0IsU0FBUyxNQUFNLElBQUksQ0FBQ3pCLGdCQUFnQixDQUFDQztZQUMzQyxPQUFPd0IsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRZixVQUFVLE1BQUs7UUFDaEMsRUFBRSxPQUFPUCxPQUFPO1lBQ2RNLFFBQVFOLEtBQUssQ0FBQywyQ0FBMkNBO1lBQ3pELE9BQU87UUFDVDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9zZXJ2aWNlcy9uZXdLeWMudHM/YjUxYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdXBhYmFzZSwgVEFCTEVTIH0gZnJvbSAnQC9saWIvc3VwYWJhc2UnXG5pbXBvcnQgeyBLWUNTdG9yYWdlU2VydmljZSB9IGZyb20gJy4va3ljU3RvcmFnZSdcblxuLy8gVXBkYXRlZCB0eXBlcyBiYXNlZCBvbiBmbGV4aWJsZSBzY2hlbWFcbmV4cG9ydCBpbnRlcmZhY2UgS1lDU3VibWlzc2lvbiB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcl9pZDogc3RyaW5nXG4gIGlkX2RvY3VtZW50X2Zyb250X3VybDogc3RyaW5nXG4gIGlkX2RvY3VtZW50X2JhY2tfdXJsPzogc3RyaW5nIC8vIE9wdGlvbmFsIGZvciBwYXNzcG9ydFxuICBzZWxmaWVfcGhvdG9fdXJsOiBzdHJpbmdcbiAgYWRkcmVzc19wcm9vZl91cmw6IHN0cmluZ1xuICBpZF9kb2N1bWVudF90eXBlOiAnbmF0aW9uYWxfaWQnIHwgJ3Bhc3Nwb3J0JyB8ICdkcml2aW5nX2xpY2Vuc2UnXG4gIGlkX2RvY3VtZW50X251bWJlcj86IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xuICBkYXRlX29mX2JpcnRoPzogc3RyaW5nXG4gIGFkZHJlc3M6IHN0cmluZ1xuICBzdGF0dXM6ICdwZW5kaW5nJyB8ICd1bmRlcl9yZXZpZXcnIHwgJ2FwcHJvdmVkJyB8ICdyZWplY3RlZCdcbiAgc3VibWlzc2lvbl9ub3Rlcz86IHN0cmluZ1xuICByZXZpZXdlZF9ieT86IHN0cmluZ1xuICByZXZpZXdlZF9hdD86IHN0cmluZ1xuICByZWplY3Rpb25fcmVhc29uPzogc3RyaW5nXG4gIGFkbWluX25vdGVzPzogc3RyaW5nXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBLWUNTdWJtaXNzaW9uRGF0YSB7XG4gIGlkX2RvY3VtZW50X3R5cGU6ICduYXRpb25hbF9pZCcgfCAncGFzc3BvcnQnIHwgJ2RyaXZpbmdfbGljZW5zZSdcbiAgaWRfZG9jdW1lbnRfbnVtYmVyPzogc3RyaW5nXG4gIGZ1bGxfbmFtZTogc3RyaW5nXG4gIGRhdGVfb2ZfYmlydGg/OiBzdHJpbmdcbiAgYWRkcmVzczogc3RyaW5nXG4gIHN1Ym1pc3Npb25fbm90ZXM/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBLWUNEb2N1bWVudHMge1xuICBpZF9mcm9udDogRmlsZVxuICBpZF9iYWNrPzogRmlsZSB8IG51bGwgLy8gT3B0aW9uYWwgZm9yIHBhc3Nwb3J0XG4gIHNlbGZpZTogRmlsZVxuICBhZGRyZXNzX3Byb29mOiBGaWxlXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgS1lDRG9jdW1lbnRUeXBlIHtcbiAgaWQ6IHN0cmluZ1xuICBuYW1lOiBzdHJpbmdcbiAgZGVzY3JpcHRpb246IHN0cmluZ1xuICBpc19hY3RpdmU6IGJvb2xlYW5cbiAgY3JlYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgS1lDU3RhdHVzIHtcbiAga3ljX3N0YXR1czogc3RyaW5nXG4gIGt5Y19zdWJtaXR0ZWRfYXQ/OiBzdHJpbmdcbiAga3ljX2FwcHJvdmVkX2F0Pzogc3RyaW5nXG59XG5cbmV4cG9ydCBjbGFzcyBOZXdLWUNTZXJ2aWNlIHtcbiAgLyoqXG4gICAqIEdldCB1c2VyJ3MgS1lDIHN0YXR1cyBmcm9tIHVzZXJzIHRhYmxlXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0VXNlcktZQ1N0YXR1cyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8S1lDU3RhdHVzIHwgbnVsbD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuVVNFUlMpXG4gICAgICAgIC5zZWxlY3QoJ2t5Y19zdGF0dXMsIGt5Y19zdWJtaXR0ZWRfYXQsIGt5Y19hcHByb3ZlZF9hdCcpXG4gICAgICAgIC5lcSgnaWQnLCB1c2VySWQpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdXNlciBLWUMgc3RhdHVzOicsIGVycm9yKVxuICAgICAgICByZXR1cm4geyBreWNfc3RhdHVzOiAnbm90X3N1Ym1pdHRlZCcgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZGF0YSB8fCB7IGt5Y19zdGF0dXM6ICdub3Rfc3VibWl0dGVkJyB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgdXNlciBLWUMgc3RhdHVzOicsIGVycm9yKVxuICAgICAgcmV0dXJuIHsga3ljX3N0YXR1czogJ25vdF9zdWJtaXR0ZWQnIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IHVzZXIncyBLWUMgc3VibWlzc2lvbiBkZXRhaWxzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0VXNlcktZQ1N1Ym1pc3Npb24odXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPEtZQ1N1Ym1pc3Npb24gfCBudWxsPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5LWUNfU1VCTUlTU0lPTlMpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdQR1JTVDExNicpIHtcbiAgICAgICAgICAvLyBObyBzdWJtaXNzaW9uIGZvdW5kXG4gICAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBLWUMgc3VibWlzc2lvbjogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBkYXRhXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgdXNlciBLWUMgc3VibWlzc2lvbjonLCBlcnJvcilcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBhdmFpbGFibGUgS1lDIGRvY3VtZW50IHR5cGVzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0S1lDRG9jdW1lbnRUeXBlcygpOiBQcm9taXNlPEtZQ0RvY3VtZW50VHlwZVtdPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5LWUNfRE9DVU1FTlRfVFlQRVMpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAuZXEoJ2lzX2FjdGl2ZScsIHRydWUpXG4gICAgICAgIC5vcmRlcignaWQnKVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggZG9jdW1lbnQgdHlwZXM6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZGF0YSB8fCBbXVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIEtZQyBkb2N1bWVudCB0eXBlczonLCBlcnJvcilcbiAgICAgIHJldHVybiBbXVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTdWJtaXQgS1lDIGFwcGxpY2F0aW9uIHdpdGggZmxleGlibGUgZG9jdW1lbnQgcmVxdWlyZW1lbnRzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgc3VibWl0S1lDQXBwbGljYXRpb24oXG4gICAgdXNlcklkOiBzdHJpbmcsXG4gICAgc3VibWlzc2lvbkRhdGE6IEtZQ1N1Ym1pc3Npb25EYXRhLFxuICAgIGRvY3VtZW50czogS1lDRG9jdW1lbnRzXG4gICk6IFByb21pc2U8S1lDU3VibWlzc2lvbj4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBWYWxpZGF0ZSBkb2N1bWVudCByZXF1aXJlbWVudHMgYmFzZWQgb24gdHlwZVxuICAgICAgdGhpcy52YWxpZGF0ZURvY3VtZW50UmVxdWlyZW1lbnRzKHN1Ym1pc3Npb25EYXRhLmlkX2RvY3VtZW50X3R5cGUsIGRvY3VtZW50cylcblxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBhbHJlYWR5IGhhcyBhIHN1Ym1pc3Npb25cbiAgICAgIGNvbnN0IGV4aXN0aW5nU3VibWlzc2lvbiA9IGF3YWl0IHRoaXMuZ2V0VXNlcktZQ1N1Ym1pc3Npb24odXNlcklkKVxuICAgICAgaWYgKGV4aXN0aW5nU3VibWlzc2lvbiAmJiBleGlzdGluZ1N1Ym1pc3Npb24uc3RhdHVzICE9PSAncmVqZWN0ZWQnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignWW91IGFscmVhZHkgaGF2ZSBhIHBlbmRpbmcgb3IgYXBwcm92ZWQgS1lDIHN1Ym1pc3Npb24nKVxuICAgICAgfVxuXG4gICAgICAvLyBVcGxvYWQgZG9jdW1lbnRzIHRvIHN0b3JhZ2VcbiAgICAgIGNvbnN0IGRvY3VtZW50VXJscyA9IGF3YWl0IHRoaXMudXBsb2FkS1lDRG9jdW1lbnRzKHVzZXJJZCwgZG9jdW1lbnRzKVxuXG4gICAgICAvLyBQcmVwYXJlIHN1Ym1pc3Npb24gZGF0YVxuICAgICAgY29uc3Qga3ljU3VibWlzc2lvbkRhdGEgPSB7XG4gICAgICAgIHVzZXJfaWQ6IHVzZXJJZCxcbiAgICAgICAgaWRfZG9jdW1lbnRfZnJvbnRfdXJsOiBkb2N1bWVudFVybHMuaWRfZnJvbnQsXG4gICAgICAgIGlkX2RvY3VtZW50X2JhY2tfdXJsOiBkb2N1bWVudFVybHMuaWRfYmFjayB8fCBudWxsLFxuICAgICAgICBzZWxmaWVfcGhvdG9fdXJsOiBkb2N1bWVudFVybHMuc2VsZmllLFxuICAgICAgICBhZGRyZXNzX3Byb29mX3VybDogZG9jdW1lbnRVcmxzLmFkZHJlc3NfcHJvb2YsXG4gICAgICAgIGlkX2RvY3VtZW50X3R5cGU6IHN1Ym1pc3Npb25EYXRhLmlkX2RvY3VtZW50X3R5cGUsXG4gICAgICAgIGlkX2RvY3VtZW50X251bWJlcjogc3VibWlzc2lvbkRhdGEuaWRfZG9jdW1lbnRfbnVtYmVyIHx8IG51bGwsXG4gICAgICAgIGZ1bGxfbmFtZTogc3VibWlzc2lvbkRhdGEuZnVsbF9uYW1lLFxuICAgICAgICBkYXRlX29mX2JpcnRoOiBzdWJtaXNzaW9uRGF0YS5kYXRlX29mX2JpcnRoIHx8IG51bGwsXG4gICAgICAgIGFkZHJlc3M6IHN1Ym1pc3Npb25EYXRhLmFkZHJlc3MsXG4gICAgICAgIHN1Ym1pc3Npb25fbm90ZXM6IHN1Ym1pc3Npb25EYXRhLnN1Ym1pc3Npb25fbm90ZXMgfHwgbnVsbCxcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZycgYXMgY29uc3RcbiAgICAgIH1cblxuICAgICAgLy8gSW5zZXJ0IG9yIHVwZGF0ZSBzdWJtaXNzaW9uXG4gICAgICBsZXQgcmVzdWx0XG4gICAgICBpZiAoZXhpc3RpbmdTdWJtaXNzaW9uKSB7XG4gICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyByZWplY3RlZCBzdWJtaXNzaW9uXG4gICAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oVEFCTEVTLktZQ19TVUJNSVNTSU9OUylcbiAgICAgICAgICAudXBkYXRlKGt5Y1N1Ym1pc3Npb25EYXRhKVxuICAgICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgICAuc2luZ2xlKClcblxuICAgICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgICAgIHJlc3VsdCA9IGRhdGFcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEluc2VydCBuZXcgc3VibWlzc2lvblxuICAgICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKFRBQkxFUy5LWUNfU1VCTUlTU0lPTlMpXG4gICAgICAgICAgLmluc2VydChreWNTdWJtaXNzaW9uRGF0YSlcbiAgICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgICAuc2luZ2xlKClcblxuICAgICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgICAgIHJlc3VsdCA9IGRhdGFcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ0tZQyBhcHBsaWNhdGlvbiBzdWJtaXR0ZWQgc3VjY2Vzc2Z1bGx5OicsIHJlc3VsdClcbiAgICAgIHJldHVybiByZXN1bHRcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3VibWl0dGluZyBLWUMgYXBwbGljYXRpb246JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBWYWxpZGF0ZSBkb2N1bWVudCByZXF1aXJlbWVudHMgYmFzZWQgb24gZG9jdW1lbnQgdHlwZVxuICAgKi9cbiAgcHJpdmF0ZSBzdGF0aWMgdmFsaWRhdGVEb2N1bWVudFJlcXVpcmVtZW50cyhcbiAgICBkb2N1bWVudFR5cGU6IHN0cmluZyxcbiAgICBkb2N1bWVudHM6IEtZQ0RvY3VtZW50c1xuICApOiB2b2lkIHtcbiAgICAvLyBBbGwgZG9jdW1lbnQgdHlwZXMgcmVxdWlyZSBmcm9udCwgc2VsZmllLCBhbmQgYWRkcmVzcyBwcm9vZlxuICAgIGlmICghZG9jdW1lbnRzLmlkX2Zyb250KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0lEIGZyb250IHBob3RvIGlzIHJlcXVpcmVkJylcbiAgICB9XG4gICAgaWYgKCFkb2N1bWVudHMuc2VsZmllKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NlbGZpZSBwaG90byBpcyByZXF1aXJlZCcpXG4gICAgfVxuICAgIGlmICghZG9jdW1lbnRzLmFkZHJlc3NfcHJvb2YpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQWRkcmVzcyBwcm9vZiBkb2N1bWVudCBpcyByZXF1aXJlZCcpXG4gICAgfVxuXG4gICAgLy8gTmF0aW9uYWwgSUQgYW5kIERyaXZpbmcgTGljZW5zZSByZXF1aXJlIGJhY2sgcGhvdG9cbiAgICBpZiAoKGRvY3VtZW50VHlwZSA9PT0gJ25hdGlvbmFsX2lkJyB8fCBkb2N1bWVudFR5cGUgPT09ICdkcml2aW5nX2xpY2Vuc2UnKSAmJiAhZG9jdW1lbnRzLmlkX2JhY2spIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSUQgYmFjayBwaG90byBpcyByZXF1aXJlZCBmb3IgdGhpcyBkb2N1bWVudCB0eXBlJylcbiAgICB9XG5cbiAgICAvLyBQYXNzcG9ydCBkb2Vzbid0IHJlcXVpcmUgYmFjayBwaG90b1xuICAgIGlmIChkb2N1bWVudFR5cGUgPT09ICdwYXNzcG9ydCcgJiYgZG9jdW1lbnRzLmlkX2JhY2spIHtcbiAgICAgIGNvbnNvbGUubG9nKCdOb3RlOiBCYWNrIHBob3RvIG5vdCByZXF1aXJlZCBmb3IgcGFzc3BvcnQsIGJ1dCB3aWxsIGJlIHN0b3JlZCBpZiBwcm92aWRlZCcpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFVwbG9hZCBLWUMgZG9jdW1lbnRzIHRvIHN0b3JhZ2VcbiAgICovXG4gIHByaXZhdGUgc3RhdGljIGFzeW5jIHVwbG9hZEtZQ0RvY3VtZW50cyhcbiAgICB1c2VySWQ6IHN0cmluZyxcbiAgICBkb2N1bWVudHM6IEtZQ0RvY3VtZW50c1xuICApOiBQcm9taXNlPFJlY29yZDxzdHJpbmcsIHN0cmluZz4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXBsb2FkUHJvbWlzZXM6IFByb21pc2U8eyB0eXBlOiBzdHJpbmc7IHVybDogc3RyaW5nIH0+W10gPSBbXVxuXG4gICAgICAvLyBVcGxvYWQgZnJvbnQgZG9jdW1lbnRcbiAgICAgIHVwbG9hZFByb21pc2VzLnB1c2goXG4gICAgICAgIEtZQ1N0b3JhZ2VTZXJ2aWNlLnVwbG9hZEtZQ0RvY3VtZW50KGRvY3VtZW50cy5pZF9mcm9udCwgdXNlcklkLCAnaWRfZnJvbnQnKVxuICAgICAgICAgIC50aGVuKHVybCA9PiAoeyB0eXBlOiAnaWRfZnJvbnQnLCB1cmwgfSkpXG4gICAgICApXG5cbiAgICAgIC8vIFVwbG9hZCBiYWNrIGRvY3VtZW50IGlmIHByb3ZpZGVkXG4gICAgICBpZiAoZG9jdW1lbnRzLmlkX2JhY2spIHtcbiAgICAgICAgdXBsb2FkUHJvbWlzZXMucHVzaChcbiAgICAgICAgICBLWUNTdG9yYWdlU2VydmljZS51cGxvYWRLWUNEb2N1bWVudChkb2N1bWVudHMuaWRfYmFjaywgdXNlcklkLCAnaWRfYmFjaycpXG4gICAgICAgICAgICAudGhlbih1cmwgPT4gKHsgdHlwZTogJ2lkX2JhY2snLCB1cmwgfSkpXG4gICAgICAgIClcbiAgICAgIH1cblxuICAgICAgLy8gVXBsb2FkIHNlbGZpZVxuICAgICAgdXBsb2FkUHJvbWlzZXMucHVzaChcbiAgICAgICAgS1lDU3RvcmFnZVNlcnZpY2UudXBsb2FkS1lDRG9jdW1lbnQoZG9jdW1lbnRzLnNlbGZpZSwgdXNlcklkLCAnc2VsZmllJylcbiAgICAgICAgICAudGhlbih1cmwgPT4gKHsgdHlwZTogJ3NlbGZpZScsIHVybCB9KSlcbiAgICAgIClcblxuICAgICAgLy8gVXBsb2FkIGFkZHJlc3MgcHJvb2ZcbiAgICAgIHVwbG9hZFByb21pc2VzLnB1c2goXG4gICAgICAgIEtZQ1N0b3JhZ2VTZXJ2aWNlLnVwbG9hZEtZQ0RvY3VtZW50KGRvY3VtZW50cy5hZGRyZXNzX3Byb29mLCB1c2VySWQsICdhZGRyZXNzX3Byb29mJylcbiAgICAgICAgICAudGhlbih1cmwgPT4gKHsgdHlwZTogJ2FkZHJlc3NfcHJvb2YnLCB1cmwgfSkpXG4gICAgICApXG5cbiAgICAgIGNvbnN0IHVwbG9hZFJlc3VsdHMgPSBhd2FpdCBQcm9taXNlLmFsbCh1cGxvYWRQcm9taXNlcylcblxuICAgICAgLy8gQ29udmVydCB0byBvYmplY3RcbiAgICAgIGNvbnN0IGRvY3VtZW50VXJsczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHt9XG4gICAgICB1cGxvYWRSZXN1bHRzLmZvckVhY2gocmVzdWx0ID0+IHtcbiAgICAgICAgZG9jdW1lbnRVcmxzW3Jlc3VsdC50eXBlXSA9IHJlc3VsdC51cmxcbiAgICAgIH0pXG5cbiAgICAgIHJldHVybiBkb2N1bWVudFVybHNcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBsb2FkaW5nIEtZQyBkb2N1bWVudHM6JywgZXJyb3IpXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byB1cGxvYWQgZG9jdW1lbnRzLiBQbGVhc2UgdHJ5IGFnYWluLicpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBLWUMgc3VibWlzc2lvbiBoaXN0b3J5IGZvciB1c2VyXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0S1lDU3RhdHVzSGlzdG9yeSh1c2VySWQ6IHN0cmluZykge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuS1lDX1NUQVRVU19ISVNUT1JZKVxuICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKVxuICAgICAgICAub3JkZXIoJ2NyZWF0ZWRfYXQnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIEtZQyBzdGF0dXMgaGlzdG9yeTogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBkYXRhIHx8IFtdXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgS1lDIHN0YXR1cyBoaXN0b3J5OicsIGVycm9yKVxuICAgICAgcmV0dXJuIFtdXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIHVzZXIgaXMgS1lDIHZlcmlmaWVkXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgaXNVc2VyS1lDVmVyaWZpZWQodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3RhdHVzID0gYXdhaXQgdGhpcy5nZXRVc2VyS1lDU3RhdHVzKHVzZXJJZClcbiAgICAgIHJldHVybiBzdGF0dXM/Lmt5Y19zdGF0dXMgPT09ICdhcHByb3ZlZCdcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgS1lDIHZlcmlmaWNhdGlvbiBzdGF0dXM6JywgZXJyb3IpXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsIlRBQkxFUyIsIktZQ1N0b3JhZ2VTZXJ2aWNlIiwiTmV3S1lDU2VydmljZSIsImdldFVzZXJLWUNTdGF0dXMiLCJ1c2VySWQiLCJkYXRhIiwiZXJyb3IiLCJmcm9tIiwiVVNFUlMiLCJzZWxlY3QiLCJlcSIsInNpbmdsZSIsImNvbnNvbGUiLCJreWNfc3RhdHVzIiwiZ2V0VXNlcktZQ1N1Ym1pc3Npb24iLCJLWUNfU1VCTUlTU0lPTlMiLCJjb2RlIiwiRXJyb3IiLCJtZXNzYWdlIiwiZ2V0S1lDRG9jdW1lbnRUeXBlcyIsIktZQ19ET0NVTUVOVF9UWVBFUyIsIm9yZGVyIiwic3VibWl0S1lDQXBwbGljYXRpb24iLCJzdWJtaXNzaW9uRGF0YSIsImRvY3VtZW50cyIsInZhbGlkYXRlRG9jdW1lbnRSZXF1aXJlbWVudHMiLCJpZF9kb2N1bWVudF90eXBlIiwiZXhpc3RpbmdTdWJtaXNzaW9uIiwic3RhdHVzIiwiZG9jdW1lbnRVcmxzIiwidXBsb2FkS1lDRG9jdW1lbnRzIiwia3ljU3VibWlzc2lvbkRhdGEiLCJ1c2VyX2lkIiwiaWRfZG9jdW1lbnRfZnJvbnRfdXJsIiwiaWRfZnJvbnQiLCJpZF9kb2N1bWVudF9iYWNrX3VybCIsImlkX2JhY2siLCJzZWxmaWVfcGhvdG9fdXJsIiwic2VsZmllIiwiYWRkcmVzc19wcm9vZl91cmwiLCJhZGRyZXNzX3Byb29mIiwiaWRfZG9jdW1lbnRfbnVtYmVyIiwiZnVsbF9uYW1lIiwiZGF0ZV9vZl9iaXJ0aCIsImFkZHJlc3MiLCJzdWJtaXNzaW9uX25vdGVzIiwicmVzdWx0IiwidXBkYXRlIiwiaW5zZXJ0IiwibG9nIiwiZG9jdW1lbnRUeXBlIiwidXBsb2FkUHJvbWlzZXMiLCJwdXNoIiwidXBsb2FkS1lDRG9jdW1lbnQiLCJ0aGVuIiwidXJsIiwidHlwZSIsInVwbG9hZFJlc3VsdHMiLCJQcm9taXNlIiwiYWxsIiwiZm9yRWFjaCIsImdldEtZQ1N0YXR1c0hpc3RvcnkiLCJLWUNfU1RBVFVTX0hJU1RPUlkiLCJhc2NlbmRpbmciLCJpc1VzZXJLWUNWZXJpZmllZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/newKyc.ts\n"));

/***/ })

});