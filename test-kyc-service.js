const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
})

async function testKYCService() {
  console.log('🧪 Testing KYC Service functionality...\n')
  
  try {
    // Test 1: Get KYC document types
    console.log('1. Testing KYC document types...')
    const { data: docTypes, error: docTypesError } = await supabase
      .from('kyc_document_types')
      .select('*')
    
    if (docTypesError) {
      console.log('❌ Error fetching document types:', docTypesError.message)
    } else {
      console.log('✅ Document types fetched successfully:')
      docTypes.forEach(type => {
        console.log(`   - ${type.id}: ${type.name}`)
      })
    }
    
    // Test 2: Create a test user and check KYC status
    console.log('\n2. Testing user KYC status...')
    
    // First, create a test user
    const testEmail = `kyctest${Date.now()}@example.com`
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: 'testpassword123',
      email_confirm: true
    })
    
    if (authError) {
      console.log('❌ Error creating test user:', authError.message)
      return
    }
    
    console.log('✅ Test user created:', authUser.user.id)
    
    // Create user profile
    const { error: profileError } = await supabase
      .from('users')
      .insert({
        id: authUser.user.id,
        email: testEmail,
        full_name: 'KYC Test User',
        user_type: 'user',
        role: 'user'
      })
    
    if (profileError) {
      console.log('❌ Error creating user profile:', profileError.message)
    } else {
      console.log('✅ User profile created')
    }
    
    // Test 3: Check user's KYC status
    console.log('\n3. Testing KYC status check...')
    const { data: kycStatus, error: kycStatusError } = await supabase
      .from('users')
      .select('kyc_status, kyc_submitted_at, kyc_approved_at')
      .eq('id', authUser.user.id)
      .single()
    
    if (kycStatusError) {
      console.log('❌ Error fetching KYC status:', kycStatusError.message)
    } else {
      console.log('✅ KYC status fetched successfully:')
      console.log(`   Status: ${kycStatus.kyc_status}`)
      console.log(`   Submitted: ${kycStatus.kyc_submitted_at || 'Not submitted'}`)
      console.log(`   Approved: ${kycStatus.kyc_approved_at || 'Not approved'}`)
    }
    
    // Test 4: Test KYC submissions table
    console.log('\n4. Testing KYC submissions table...')
    const { data: submissions, error: submissionsError } = await supabase
      .from('kyc_submissions')
      .select('*')
      .eq('user_id', authUser.user.id)
    
    if (submissionsError) {
      console.log('❌ Error fetching KYC submissions:', submissionsError.message)
    } else {
      console.log('✅ KYC submissions table accessible')
      console.log(`   Found ${submissions.length} submissions for user`)
    }
    
    // Test 5: Test KYC status history table
    console.log('\n5. Testing KYC status history table...')
    const { data: history, error: historyError } = await supabase
      .from('kyc_status_history')
      .select('*')
      .eq('user_id', authUser.user.id)
    
    if (historyError) {
      console.log('❌ Error fetching KYC status history:', historyError.message)
    } else {
      console.log('✅ KYC status history table accessible')
      console.log(`   Found ${history.length} history records for user`)
    }
    
    // Cleanup
    console.log('\n6. Cleaning up test user...')
    await supabase.auth.admin.deleteUser(authUser.user.id)
    console.log('✅ Test user cleaned up')
    
    console.log('\n🎉 KYC Service test completed successfully!')
    console.log('✅ All KYC database tables are working correctly')
    console.log('✅ KYC service can now be used in the application')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testKYCService()
