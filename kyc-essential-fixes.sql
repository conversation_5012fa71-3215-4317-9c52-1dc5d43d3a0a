-- Essential KYC Database Fixes
-- Execute this step by step in Supabase SQL Editor

-- =====================================================
-- STEP 1: Fix Foreign Key References
-- =====================================================

-- Drop existing foreign key constraints that reference auth.users
ALTER TABLE kyc_submissions DROP CONSTRAINT IF EXISTS kyc_submissions_user_id_fkey;
ALTER TABLE kyc_submissions DROP CONSTRAINT IF EXISTS kyc_submissions_reviewed_by_fkey;
ALTER TABLE kyc_status_history DROP CONSTRAINT IF EXISTS kyc_status_history_user_id_fkey;
ALTER TABLE kyc_status_history DROP CONSTRAINT IF EXISTS kyc_status_history_changed_by_fkey;

-- Add correct foreign key constraints referencing public.users
ALTER TABLE kyc_submissions 
ADD CONSTRAINT kyc_submissions_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE kyc_submissions 
ADD CONSTRAINT kyc_submissions_reviewed_by_fkey 
FOREIGN KEY (reviewed_by) REFERENCES public.users(id) ON DELETE SET NULL;

ALTER TABLE kyc_status_history 
ADD CONSTRAINT kyc_status_history_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE kyc_status_history 
ADD CONSTRAINT kyc_status_history_changed_by_fkey 
FOREIGN KEY (changed_by) REFERENCES public.users(id) ON DELETE SET NULL;

-- =====================================================
-- STEP 2: Enable RLS and Create Basic Policies
-- =====================================================

-- Enable RLS on all KYC tables
ALTER TABLE kyc_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_document_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_status_history ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can insert own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can update own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Admins can view all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Admins can update all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Everyone can read KYC document types" ON kyc_document_types;
DROP POLICY IF EXISTS "Admins can modify KYC document types" ON kyc_document_types;
DROP POLICY IF EXISTS "Users can view own KYC status history" ON kyc_status_history;
DROP POLICY IF EXISTS "Admins can view all KYC status history" ON kyc_status_history;
DROP POLICY IF EXISTS "System can insert KYC status history" ON kyc_status_history;

-- KYC Submissions Policies
CREATE POLICY "Users can view own KYC submissions" ON kyc_submissions
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own KYC submissions" ON kyc_submissions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own KYC submissions" ON kyc_submissions
    FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Admins can view all KYC submissions" ON kyc_submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

CREATE POLICY "Admins can update all KYC submissions" ON kyc_submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- KYC Document Types Policies
CREATE POLICY "Everyone can read KYC document types" ON kyc_document_types
    FOR SELECT USING (true);

CREATE POLICY "Admins can modify KYC document types" ON kyc_document_types
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- KYC Status History Policies
CREATE POLICY "Users can view own KYC status history" ON kyc_status_history
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Admins can view all KYC status history" ON kyc_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

CREATE POLICY "System can insert KYC status history" ON kyc_status_history
    FOR INSERT WITH CHECK (true);

-- =====================================================
-- STEP 3: Grant Permissions
-- =====================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON kyc_submissions TO authenticated;
GRANT SELECT ON kyc_document_types TO authenticated;
GRANT SELECT, INSERT ON kyc_status_history TO authenticated;

-- Grant permissions to service role (for admin operations)
GRANT ALL ON kyc_submissions TO service_role;
GRANT ALL ON kyc_document_types TO service_role;
GRANT ALL ON kyc_status_history TO service_role;

-- =====================================================
-- STEP 4: Create Helper Functions
-- =====================================================

-- Function to check if user is KYC verified
CREATE OR REPLACE FUNCTION is_kyc_verified(user_id_param uuid)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = user_id_param 
        AND kyc_status = 'approved'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's KYC status
CREATE OR REPLACE FUNCTION get_user_kyc_status(user_id_param uuid)
RETURNS TABLE(
    kyc_status varchar(20),
    kyc_submitted_at timestamp with time zone,
    kyc_approved_at timestamp with time zone
) AS $$
BEGIN
    RETURN QUERY
    SELECT u.kyc_status, u.kyc_submitted_at, u.kyc_approved_at
    FROM public.users u
    WHERE u.id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 5: Verify Setup
-- =====================================================

-- Check if all tables exist and have correct structure
SELECT 
    'kyc_submissions' as table_name,
    COUNT(*) as row_count
FROM kyc_submissions
UNION ALL
SELECT 
    'kyc_document_types' as table_name,
    COUNT(*) as row_count
FROM kyc_document_types
UNION ALL
SELECT 
    'kyc_status_history' as table_name,
    COUNT(*) as row_count
FROM kyc_status_history;

-- Verify foreign key constraints
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('kyc_submissions', 'kyc_status_history')
ORDER BY tc.table_name, tc.constraint_name;

SELECT 'KYC database fixes completed successfully!' as status;
