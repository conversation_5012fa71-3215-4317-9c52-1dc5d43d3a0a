"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/shop/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MerchantWallet.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/MerchantWallet.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MerchantWallet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Clock,Eye,EyeOff,RefreshCw,Send,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/merchantWallet */ \"(app-pages-browser)/./src/lib/services/merchantWallet.ts\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx\");\n/* harmony import */ var _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useKYCVerification */ \"(app-pages-browser)/./src/hooks/useKYCVerification.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MerchantWallet(param) {\n    let { shopId, userId } = param;\n    _s();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBalance, setShowBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [transferAmount, setTransferAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showTransferModal, setShowTransferModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { requireKYCForAction } = (0,_hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_4__.useKYCCheck)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWalletData();\n    }, [\n        shopId\n    ]);\n    const loadWalletData = async ()=>{\n        try {\n            setLoading(true);\n            const [walletData, statsData] = await Promise.all([\n                _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.getMerchantWallet(shopId),\n                _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.getMerchantWallet(shopId).then((w)=>w ? _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.getMerchantWalletStats(w.id) : null)\n            ]);\n            setWallet(walletData);\n            setStats(statsData);\n        } catch (error) {\n            console.error(\"Error loading wallet data:\", error);\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_3__.showAlert)({\n                title: \"Error\",\n                message: \"Failed to load wallet data\",\n                variant: \"danger\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTransfer = async ()=>{\n        if (!wallet || !transferAmount) return;\n        const amount = parseFloat(transferAmount);\n        if (isNaN(amount) || amount <= 0) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_3__.showAlert)({\n                title: \"Invalid Amount\",\n                message: \"Please enter a valid transfer amount\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        if (amount > wallet.balance) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_3__.showAlert)({\n                title: \"Insufficient Balance\",\n                message: \"Transfer amount exceeds available balance\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        const confirmed = await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_3__.showConfirmation)({\n            title: \"Confirm Transfer\",\n            message: \"Transfer \".concat(_lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(amount), \" from merchant wallet to your main wallet?\"),\n            confirmText: \"Transfer\",\n            variant: \"info\"\n        });\n        if (confirmed) {\n            try {\n                setTransferLoading(true);\n                await _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.transferToMainWallet(wallet.id, amount, userId);\n                await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_3__.showAlert)({\n                    title: \"Transfer Successful\",\n                    message: \"\".concat(_lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(amount), \" has been transferred to your main wallet\"),\n                    variant: \"success\"\n                });\n                setTransferAmount(\"\");\n                setShowTransferModal(false);\n                loadWalletData() // Refresh data\n                ;\n            } catch (error) {\n                console.error(\"Transfer error:\", error);\n                await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_3__.showAlert)({\n                    title: \"Transfer Failed\",\n                    message: error instanceof Error ? error.message : \"Failed to transfer funds\",\n                    variant: \"danger\"\n                });\n            } finally{\n                setTransferLoading(false);\n            }\n        }\n    };\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return \"Just now\";\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        if (diffInDays < 7) return \"\".concat(diffInDays, \"d ago\");\n        return date.toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded w-2/3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (!wallet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Merchant Wallet Not Available\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Your merchant wallet will be created once your shop is approved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-sm p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Merchant Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowBalance(!showBalance),\n                                        className: \"p-1 hover:bg-white/20 rounded-full transition-colors\",\n                                        children: showBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 30\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 63\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: loadWalletData,\n                                        className: \"p-1 hover:bg-white/20 rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-100 text-sm mb-1\",\n                                                children: \"Available Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: showBalance ? _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(wallet.balance) : \"••••••\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-100 text-sm mb-1\",\n                                                children: \"Pending Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: showBalance ? _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(wallet.pending_balance || 0) : \"••••••\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 pt-2 border-t border-blue-500/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100 text-xs\",\n                                    children: [\n                                        \"Total: \",\n                                        showBalance ? _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency((wallet.balance || 0) + (wallet.pending_balance || 0)) : \"••••••\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowTransferModal(true),\n                        disabled: wallet.balance <= 0,\n                        className: \"w-full bg-white text-blue-600 py-2 px-4 rounded-lg font-medium hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            \"Transfer to Main Wallet\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-orange-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Pending\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(stats.pendingBalance)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-green-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Earned\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(stats.totalEarned)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-blue-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"This Month\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(stats.monthlyEarnings)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-purple-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Withdrawn\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(stats.totalWithdrawn)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this),\n            (stats === null || stats === void 0 ? void 0 : stats.recentTransactions) && stats.recentTransactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Recent Transactions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: stats.recentTransactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(transaction.type === \"credit\" ? \"bg-green-100\" : \"bg-red-100\"),\n                                                children: transaction.type === \"credit\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.getCategoryDisplayName(transaction.category)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: formatTimeAgo(transaction.created_at)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-semibold \".concat(_lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.getTransactionTypeColor(transaction.type)),\n                                            children: [\n                                                transaction.type === \"credit\" ? \"+\" : \"-\",\n                                                _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(transaction.amount)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, transaction.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this),\n            showTransferModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Transfer to Main Wallet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Transfer Amount\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\",\n                                            children: \"Rs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: transferAmount,\n                                            onChange: (e)=>setTransferAmount(e.target.value),\n                                            placeholder: \"0.00\",\n                                            min: \"0\",\n                                            max: wallet.balance,\n                                            step: \"0.01\",\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: [\n                                        \"Available: \",\n                                        _lib_services_merchantWallet__WEBPACK_IMPORTED_MODULE_2__.MerchantWalletService.formatCurrency(wallet.balance)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTransferModal(false),\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleTransfer,\n                                    disabled: transferLoading || !transferAmount || parseFloat(transferAmount) <= 0,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                                    children: transferLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Clock_Eye_EyeOff_RefreshCw_Send_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 19\n                                    }, this) : \"Transfer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\dashboard\\\\MerchantWallet.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(MerchantWallet, \"nQGHfyHjwzboCTIuRgC9jbjnDKs=\", false, function() {\n    return [\n        _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_4__.useKYCCheck\n    ];\n});\n_c = MerchantWallet;\nvar _c;\n$RefreshReg$(_c, \"MerchantWallet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9NZXJjaGFudFdhbGxldC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBWXRCO0FBQ3FIO0FBQzFEO0FBQ3hCO0FBT3pDLFNBQVNlLGVBQWUsS0FBdUM7UUFBdkMsRUFBRUMsTUFBTSxFQUFFQyxNQUFNLEVBQXVCLEdBQXZDOztJQUNyQyxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR25CLCtDQUFRQSxDQUF3QjtJQUM1RCxNQUFNLENBQUNvQixPQUFPQyxTQUFTLEdBQUdyQiwrQ0FBUUEsQ0FBNkI7SUFDL0QsTUFBTSxDQUFDc0IsU0FBU0MsV0FBVyxHQUFHdkIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDd0IsaUJBQWlCQyxtQkFBbUIsR0FBR3pCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzBCLGFBQWFDLGVBQWUsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzRCLGdCQUFnQkMsa0JBQWtCLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUM4QixtQkFBbUJDLHFCQUFxQixHQUFHL0IsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxFQUFFZ0MsbUJBQW1CLEVBQUUsR0FBR2xCLHNFQUFXQTtJQUUzQ2IsZ0RBQVNBLENBQUM7UUFDUmdDO0lBQ0YsR0FBRztRQUFDakI7S0FBTztJQUVYLE1BQU1pQixpQkFBaUI7UUFDckIsSUFBSTtZQUNGVixXQUFXO1lBQ1gsTUFBTSxDQUFDVyxZQUFZQyxVQUFVLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO2dCQUNoRDFCLCtFQUFxQkEsQ0FBQzJCLGlCQUFpQixDQUFDdEI7Z0JBQ3hDTCwrRUFBcUJBLENBQUMyQixpQkFBaUIsQ0FBQ3RCLFFBQVF1QixJQUFJLENBQUNDLENBQUFBLElBQ25EQSxJQUFJN0IsK0VBQXFCQSxDQUFDOEIsc0JBQXNCLENBQUNELEVBQUVFLEVBQUUsSUFBSTthQUU1RDtZQUVEdkIsVUFBVWU7WUFDVmIsU0FBU2M7UUFDWCxFQUFFLE9BQU9RLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUMsTUFBTTlCLDRFQUFTQSxDQUFDO2dCQUNkZ0MsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsU0FBUztZQUNYO1FBQ0YsU0FBVTtZQUNSeEIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNeUIsaUJBQWlCO1FBQ3JCLElBQUksQ0FBQzlCLFVBQVUsQ0FBQ1UsZ0JBQWdCO1FBRWhDLE1BQU1xQixTQUFTQyxXQUFXdEI7UUFDMUIsSUFBSXVCLE1BQU1GLFdBQVdBLFVBQVUsR0FBRztZQUNoQyxNQUFNcEMsNEVBQVNBLENBQUM7Z0JBQ2RnQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxTQUFTO1lBQ1g7WUFDQTtRQUNGO1FBRUEsSUFBSUUsU0FBUy9CLE9BQU9rQyxPQUFPLEVBQUU7WUFDM0IsTUFBTXZDLDRFQUFTQSxDQUFDO2dCQUNkZ0MsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsU0FBUztZQUNYO1lBQ0E7UUFDRjtRQUVBLE1BQU1NLFlBQVksTUFBTXpDLG1GQUFnQkEsQ0FBQztZQUN2Q2lDLE9BQU87WUFDUEMsU0FBUyxZQUF5RCxPQUE3Q25DLCtFQUFxQkEsQ0FBQzJDLGNBQWMsQ0FBQ0wsU0FBUTtZQUNsRU0sYUFBYTtZQUNiUixTQUFTO1FBQ1g7UUFFQSxJQUFJTSxXQUFXO1lBQ2IsSUFBSTtnQkFDRjVCLG1CQUFtQjtnQkFDbkIsTUFBTWQsK0VBQXFCQSxDQUFDNkMsb0JBQW9CLENBQUN0QyxPQUFPd0IsRUFBRSxFQUFFTyxRQUFRaEM7Z0JBRXBFLE1BQU1KLDRFQUFTQSxDQUFDO29CQUNkZ0MsT0FBTztvQkFDUEMsU0FBUyxHQUFnRCxPQUE3Q25DLCtFQUFxQkEsQ0FBQzJDLGNBQWMsQ0FBQ0wsU0FBUTtvQkFDekRGLFNBQVM7Z0JBQ1g7Z0JBRUFsQixrQkFBa0I7Z0JBQ2xCRSxxQkFBcUI7Z0JBQ3JCRSxpQkFBaUIsZUFBZTs7WUFDbEMsRUFBRSxPQUFPVSxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsbUJBQW1CQTtnQkFDakMsTUFBTTlCLDRFQUFTQSxDQUFDO29CQUNkZ0MsT0FBTztvQkFDUEMsU0FBU0gsaUJBQWlCYyxRQUFRZCxNQUFNRyxPQUFPLEdBQUc7b0JBQ2xEQyxTQUFTO2dCQUNYO1lBQ0YsU0FBVTtnQkFDUnRCLG1CQUFtQjtZQUNyQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNaUMsZ0JBQWdCLENBQUNDO1FBQ3JCLE1BQU1DLE9BQU8sSUFBSUMsS0FBS0Y7UUFDdEIsTUFBTUcsTUFBTSxJQUFJRDtRQUNoQixNQUFNRSxjQUFjQyxLQUFLQyxLQUFLLENBQUMsQ0FBQ0gsSUFBSUksT0FBTyxLQUFLTixLQUFLTSxPQUFPLEVBQUMsSUFBTSxRQUFPLEtBQUssRUFBQztRQUVoRixJQUFJSCxjQUFjLEdBQUcsT0FBTztRQUM1QixJQUFJQSxjQUFjLElBQUksT0FBTyxHQUFlLE9BQVpBLGFBQVk7UUFFNUMsTUFBTUksYUFBYUgsS0FBS0MsS0FBSyxDQUFDRixjQUFjO1FBQzVDLElBQUlJLGFBQWEsR0FBRyxPQUFPLEdBQWMsT0FBWEEsWUFBVztRQUV6QyxPQUFPUCxLQUFLUSxrQkFBa0I7SUFDaEM7SUFFQSxJQUFJOUMsU0FBUztRQUNYLHFCQUNFLDhEQUFDK0M7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzs7Ozs7MENBQ2YsOERBQUNEO2dDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUt6QjtJQUVBLElBQUksQ0FBQ3BELFFBQVE7UUFDWCxxQkFDRSw4REFBQ21EO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3BFLHdKQUFNQTt3QkFBQ29FLFdBQVU7Ozs7OztrQ0FDbEIsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUF5Qzs7Ozs7O2tDQUN2RCw4REFBQ0U7d0JBQUVGLFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQU1yQztJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3BFLHdKQUFNQTt3Q0FBQ29FLFdBQVU7Ozs7OztrREFDbEIsOERBQUNDO3dDQUFHRCxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQUV4Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRzt3Q0FDQ0MsU0FBUyxJQUFNL0MsZUFBZSxDQUFDRDt3Q0FDL0I0QyxXQUFVO2tEQUVUNUMsNEJBQWMsOERBQUNsQix3SkFBTUE7NENBQUM4RCxXQUFVOzs7OztpRUFBZSw4REFBQy9ELHdKQUFHQTs0Q0FBQytELFdBQVU7Ozs7Ozs7Ozs7O2tEQUVqRSw4REFBQ0c7d0NBQ0NDLFNBQVN6Qzt3Q0FDVHFDLFdBQVU7a0RBRVYsNEVBQUM3RCx3SkFBU0E7NENBQUM2RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLM0IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNHO2dEQUFFRixXQUFVOzBEQUE2Qjs7Ozs7OzBEQUMxQyw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQ1Y1QyxjQUNHZiwrRUFBcUJBLENBQUMyQyxjQUFjLENBQUNwQyxPQUFPa0MsT0FBTyxJQUNuRDs7Ozs7Ozs7Ozs7O2tEQUlSLDhEQUFDaUI7OzBEQUNDLDhEQUFDRztnREFBRUYsV0FBVTswREFBNkI7Ozs7OzswREFDMUMsOERBQUNFO2dEQUFFRixXQUFVOzBEQUNWNUMsY0FDR2YsK0VBQXFCQSxDQUFDMkMsY0FBYyxDQUFDcEMsT0FBT3lELGVBQWUsSUFBSSxLQUMvRDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtWLDhEQUFDTjtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0U7b0NBQUVGLFdBQVU7O3dDQUF3Qjt3Q0FBUTVDLGNBQ3pDZiwrRUFBcUJBLENBQUMyQyxjQUFjLENBQUMsQ0FBQ3BDLE9BQU9rQyxPQUFPLElBQUksS0FBTWxDLENBQUFBLE9BQU95RCxlQUFlLElBQUksTUFDeEY7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLUiw4REFBQ0Y7d0JBQ0NDLFNBQVMsSUFBTTNDLHFCQUFxQjt3QkFDcEM2QyxVQUFVMUQsT0FBT2tDLE9BQU8sSUFBSTt3QkFDNUJrQixXQUFVOzswQ0FFViw4REFBQzVELHdKQUFJQTtnQ0FBQzRELFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7Ozs7Ozs7WUFNcENsRCx1QkFDQyw4REFBQ2lEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNoRSx5SkFBS0E7d0NBQUNnRSxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFbkIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQW9DOzs7Ozs7c0RBQ2pELDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFDVjNELCtFQUFxQkEsQ0FBQzJDLGNBQWMsQ0FBQ2xDLE1BQU15RCxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNbEUsOERBQUNSO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDbkUseUpBQVVBO3dDQUFDbUUsV0FBVTs7Ozs7Ozs7Ozs7OENBRXhCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNFOzRDQUFFRixXQUFVO3NEQUFvQzs7Ozs7O3NEQUNqRCw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQ1YzRCwrRUFBcUJBLENBQUMyQyxjQUFjLENBQUNsQyxNQUFNMEQsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTS9ELDhEQUFDVDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2xFLHlKQUFZQTt3Q0FBQ2tFLFdBQVU7Ozs7Ozs7Ozs7OzhDQUUxQiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBb0M7Ozs7OztzREFDakQsOERBQUNFOzRDQUFFRixXQUFVO3NEQUNWM0QsK0VBQXFCQSxDQUFDMkMsY0FBYyxDQUFDbEMsTUFBTTJELGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1uRSw4REFBQ1Y7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNqRSx5SkFBYUE7d0NBQUNpRSxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFM0IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQW9DOzs7Ozs7c0RBQ2pELDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFDVjNELCtFQUFxQkEsQ0FBQzJDLGNBQWMsQ0FBQ2xDLE1BQU00RCxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVNyRTVELENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTzZELGtCQUFrQixLQUFJN0QsTUFBTTZELGtCQUFrQixDQUFDQyxNQUFNLEdBQUcsbUJBQzlELDhEQUFDYjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNhO3dCQUFHYixXQUFVO2tDQUEyQzs7Ozs7O2tDQUN6RCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1psRCxNQUFNNkQsa0JBQWtCLENBQUNHLEdBQUcsQ0FBQyxDQUFDQyw0QkFDN0IsOERBQUNoQjtnQ0FBeUJDLFdBQVU7O2tEQUNsQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVyxrQkFFZixPQURDZSxZQUFZQyxJQUFJLEtBQUssV0FBVyxpQkFBaUI7MERBRWhERCxZQUFZQyxJQUFJLEtBQUsseUJBQ3BCLDhEQUFDakYseUpBQWFBO29EQUFDaUUsV0FBVTs7Ozs7eUVBRXpCLDhEQUFDbEUseUpBQVlBO29EQUFDa0UsV0FBVTs7Ozs7Ozs7Ozs7MERBRzVCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNFO3dEQUFFRixXQUFVO2tFQUNWM0QsK0VBQXFCQSxDQUFDNEUsc0JBQXNCLENBQUNGLFlBQVlHLFFBQVE7Ozs7OztrRUFFcEUsOERBQUNoQjt3REFBRUYsV0FBVTtrRUFDVlosY0FBYzJCLFlBQVlJLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJM0MsOERBQUNwQjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0U7NENBQUVGLFdBQVcseUJBRWIsT0FEQzNELCtFQUFxQkEsQ0FBQytFLHVCQUF1QixDQUFDTCxZQUFZQyxJQUFJOztnREFFN0RELFlBQVlDLElBQUksS0FBSyxXQUFXLE1BQU07Z0RBQ3RDM0UsK0VBQXFCQSxDQUFDMkMsY0FBYyxDQUFDK0IsWUFBWXBDLE1BQU07Ozs7Ozs7Ozs7Ozs7K0JBekJwRG9DLFlBQVkzQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBbUMvQlosbUNBQ0MsOERBQUN1QztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBMkM7Ozs7OztzQ0FFekQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3FCO29DQUFNckIsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3NCOzRDQUFLdEIsV0FBVTtzREFBbUU7Ozs7OztzREFDbkYsOERBQUN1Qjs0Q0FDQ1AsTUFBSzs0Q0FDTFEsT0FBT2xFOzRDQUNQbUUsVUFBVSxDQUFDQyxJQUFNbkUsa0JBQWtCbUUsRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUNqREksYUFBWTs0Q0FDWkMsS0FBSTs0Q0FDSkMsS0FBS2xGLE9BQU9rQyxPQUFPOzRDQUNuQmlELE1BQUs7NENBQ0wvQixXQUFVOzs7Ozs7Ozs7Ozs7OENBR2QsOERBQUNFO29DQUFFRixXQUFVOzt3Q0FBNkI7d0NBQzVCM0QsK0VBQXFCQSxDQUFDMkMsY0FBYyxDQUFDcEMsT0FBT2tDLE9BQU87Ozs7Ozs7Ozs7Ozs7c0NBSW5FLDhEQUFDaUI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRztvQ0FDQ0MsU0FBUyxJQUFNM0MscUJBQXFCO29DQUNwQ3VDLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ0c7b0NBQ0NDLFNBQVMxQjtvQ0FDVDRCLFVBQVVwRCxtQkFBbUIsQ0FBQ0ksa0JBQWtCc0IsV0FBV3RCLG1CQUFtQjtvQ0FDOUUwQyxXQUFVOzhDQUVUOUMsZ0NBQ0MsOERBQUNmLHdKQUFTQTt3Q0FBQzZELFdBQVU7Ozs7OytDQUVyQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTbEI7R0FqV3dCdkQ7O1FBUVVELGtFQUFXQTs7O0tBUnJCQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvTWVyY2hhbnRXYWxsZXQudHN4PzNiZmUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFdhbGxldCxcbiAgVHJlbmRpbmdVcCxcbiAgQXJyb3dVcFJpZ2h0LFxuICBBcnJvd0Rvd25MZWZ0LFxuICBDbG9jayxcbiAgRG9sbGFyU2lnbixcbiAgRXllLFxuICBFeWVPZmYsXG4gIFJlZnJlc2hDdyxcbiAgU2VuZFxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBNZXJjaGFudFdhbGxldFNlcnZpY2UsIHR5cGUgTWVyY2hhbnRXYWxsZXQsIE1lcmNoYW50V2FsbGV0U3RhdHMsIE1lcmNoYW50V2FsbGV0VHJhbnNhY3Rpb24gfSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9tZXJjaGFudFdhbGxldCdcbmltcG9ydCB7IHNob3dDb25maXJtYXRpb24sIHNob3dBbGVydCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9Db25maXJtYXRpb25EaWFsb2cnXG5pbXBvcnQgeyB1c2VLWUNDaGVjayB9IGZyb20gJ0AvaG9va3MvdXNlS1lDVmVyaWZpY2F0aW9uJ1xuXG5pbnRlcmZhY2UgTWVyY2hhbnRXYWxsZXRQcm9wcyB7XG4gIHNob3BJZDogc3RyaW5nXG4gIHVzZXJJZDogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1lcmNoYW50V2FsbGV0KHsgc2hvcElkLCB1c2VySWQgfTogTWVyY2hhbnRXYWxsZXRQcm9wcykge1xuICBjb25zdCBbd2FsbGV0LCBzZXRXYWxsZXRdID0gdXNlU3RhdGU8TWVyY2hhbnRXYWxsZXQgfCBudWxsPihudWxsKVxuICBjb25zdCBbc3RhdHMsIHNldFN0YXRzXSA9IHVzZVN0YXRlPE1lcmNoYW50V2FsbGV0U3RhdHMgfCBudWxsPihudWxsKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbdHJhbnNmZXJMb2FkaW5nLCBzZXRUcmFuc2ZlckxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzaG93QmFsYW5jZSwgc2V0U2hvd0JhbGFuY2VdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3RyYW5zZmVyQW1vdW50LCBzZXRUcmFuc2ZlckFtb3VudF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW3Nob3dUcmFuc2Zlck1vZGFsLCBzZXRTaG93VHJhbnNmZXJNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgeyByZXF1aXJlS1lDRm9yQWN0aW9uIH0gPSB1c2VLWUNDaGVjaygpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkV2FsbGV0RGF0YSgpXG4gIH0sIFtzaG9wSWRdKVxuXG4gIGNvbnN0IGxvYWRXYWxsZXREYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICBjb25zdCBbd2FsbGV0RGF0YSwgc3RhdHNEYXRhXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgTWVyY2hhbnRXYWxsZXRTZXJ2aWNlLmdldE1lcmNoYW50V2FsbGV0KHNob3BJZCksXG4gICAgICAgIE1lcmNoYW50V2FsbGV0U2VydmljZS5nZXRNZXJjaGFudFdhbGxldChzaG9wSWQpLnRoZW4odyA9PiBcbiAgICAgICAgICB3ID8gTWVyY2hhbnRXYWxsZXRTZXJ2aWNlLmdldE1lcmNoYW50V2FsbGV0U3RhdHMody5pZCkgOiBudWxsXG4gICAgICAgIClcbiAgICAgIF0pXG4gICAgICBcbiAgICAgIHNldFdhbGxldCh3YWxsZXREYXRhKVxuICAgICAgc2V0U3RhdHMoc3RhdHNEYXRhKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHdhbGxldCBkYXRhOicsIGVycm9yKVxuICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXG4gICAgICAgIG1lc3NhZ2U6ICdGYWlsZWQgdG8gbG9hZCB3YWxsZXQgZGF0YScsXG4gICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRyYW5zZmVyID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghd2FsbGV0IHx8ICF0cmFuc2ZlckFtb3VudCkgcmV0dXJuXG5cbiAgICBjb25zdCBhbW91bnQgPSBwYXJzZUZsb2F0KHRyYW5zZmVyQW1vdW50KVxuICAgIGlmIChpc05hTihhbW91bnQpIHx8IGFtb3VudCA8PSAwKSB7XG4gICAgICBhd2FpdCBzaG93QWxlcnQoe1xuICAgICAgICB0aXRsZTogJ0ludmFsaWQgQW1vdW50JyxcbiAgICAgICAgbWVzc2FnZTogJ1BsZWFzZSBlbnRlciBhIHZhbGlkIHRyYW5zZmVyIGFtb3VudCcsXG4gICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgaWYgKGFtb3VudCA+IHdhbGxldC5iYWxhbmNlKSB7XG4gICAgICBhd2FpdCBzaG93QWxlcnQoe1xuICAgICAgICB0aXRsZTogJ0luc3VmZmljaWVudCBCYWxhbmNlJyxcbiAgICAgICAgbWVzc2FnZTogJ1RyYW5zZmVyIGFtb3VudCBleGNlZWRzIGF2YWlsYWJsZSBiYWxhbmNlJyxcbiAgICAgICAgdmFyaWFudDogJ2RhbmdlcidcbiAgICAgIH0pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBjb25zdCBjb25maXJtZWQgPSBhd2FpdCBzaG93Q29uZmlybWF0aW9uKHtcbiAgICAgIHRpdGxlOiAnQ29uZmlybSBUcmFuc2ZlcicsXG4gICAgICBtZXNzYWdlOiBgVHJhbnNmZXIgJHtNZXJjaGFudFdhbGxldFNlcnZpY2UuZm9ybWF0Q3VycmVuY3koYW1vdW50KX0gZnJvbSBtZXJjaGFudCB3YWxsZXQgdG8geW91ciBtYWluIHdhbGxldD9gLFxuICAgICAgY29uZmlybVRleHQ6ICdUcmFuc2ZlcicsXG4gICAgICB2YXJpYW50OiAnaW5mbydcbiAgICB9KVxuXG4gICAgaWYgKGNvbmZpcm1lZCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgc2V0VHJhbnNmZXJMb2FkaW5nKHRydWUpXG4gICAgICAgIGF3YWl0IE1lcmNoYW50V2FsbGV0U2VydmljZS50cmFuc2ZlclRvTWFpbldhbGxldCh3YWxsZXQuaWQsIGFtb3VudCwgdXNlcklkKVxuICAgICAgICBcbiAgICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgICB0aXRsZTogJ1RyYW5zZmVyIFN1Y2Nlc3NmdWwnLFxuICAgICAgICAgIG1lc3NhZ2U6IGAke01lcmNoYW50V2FsbGV0U2VydmljZS5mb3JtYXRDdXJyZW5jeShhbW91bnQpfSBoYXMgYmVlbiB0cmFuc2ZlcnJlZCB0byB5b3VyIG1haW4gd2FsbGV0YCxcbiAgICAgICAgICB2YXJpYW50OiAnc3VjY2VzcydcbiAgICAgICAgfSlcbiAgICAgICAgXG4gICAgICAgIHNldFRyYW5zZmVyQW1vdW50KCcnKVxuICAgICAgICBzZXRTaG93VHJhbnNmZXJNb2RhbChmYWxzZSlcbiAgICAgICAgbG9hZFdhbGxldERhdGEoKSAvLyBSZWZyZXNoIGRhdGFcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1RyYW5zZmVyIGVycm9yOicsIGVycm9yKVxuICAgICAgICBhd2FpdCBzaG93QWxlcnQoe1xuICAgICAgICAgIHRpdGxlOiAnVHJhbnNmZXIgRmFpbGVkJyxcbiAgICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdGYWlsZWQgdG8gdHJhbnNmZXIgZnVuZHMnLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICAgIH0pXG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRUcmFuc2ZlckxvYWRpbmcoZmFsc2UpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgY29uc3QgZm9ybWF0VGltZUFnbyA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVN0cmluZylcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXG4gICAgY29uc3QgZGlmZkluSG91cnMgPSBNYXRoLmZsb29yKChub3cuZ2V0VGltZSgpIC0gZGF0ZS5nZXRUaW1lKCkpIC8gKDEwMDAgKiA2MCAqIDYwKSlcbiAgICBcbiAgICBpZiAoZGlmZkluSG91cnMgPCAxKSByZXR1cm4gJ0p1c3Qgbm93J1xuICAgIGlmIChkaWZmSW5Ib3VycyA8IDI0KSByZXR1cm4gYCR7ZGlmZkluSG91cnN9aCBhZ29gXG4gICAgXG4gICAgY29uc3QgZGlmZkluRGF5cyA9IE1hdGguZmxvb3IoZGlmZkluSG91cnMgLyAyNClcbiAgICBpZiAoZGlmZkluRGF5cyA8IDcpIHJldHVybiBgJHtkaWZmSW5EYXlzfWQgYWdvYFxuICAgIFxuICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygpXG4gIH1cblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xLzMgbWItNFwiPjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTIwMCByb3VuZGVkXCI+PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTIwMCByb3VuZGVkIHctMi8zXCI+PC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKCF3YWxsZXQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICA8V2FsbGV0IGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+TWVyY2hhbnQgV2FsbGV0IE5vdCBBdmFpbGFibGU8L2gzPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIFlvdXIgbWVyY2hhbnQgd2FsbGV0IHdpbGwgYmUgY3JlYXRlZCBvbmNlIHlvdXIgc2hvcCBpcyBhcHByb3ZlZC5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgey8qIFdhbGxldCBCYWxhbmNlIENhcmQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1ibHVlLTcwMCByb3VuZGVkLXhsIHNoYWRvdy1zbSBwLTYgdGV4dC13aGl0ZVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPFdhbGxldCBjbGFzc05hbWU9XCJoLTYgdy02IG1yLTJcIiAvPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPk1lcmNoYW50IFdhbGxldDwvaDM+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0JhbGFuY2UoIXNob3dCYWxhbmNlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xIGhvdmVyOmJnLXdoaXRlLzIwIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtzaG93QmFsYW5jZSA/IDxFeWVPZmYgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEV5ZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz59XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17bG9hZFdhbGxldERhdGF9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy13aGl0ZS8yMCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMTAwIHRleHQtc20gbWItMVwiPkF2YWlsYWJsZSBCYWxhbmNlPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj5cbiAgICAgICAgICAgICAgICB7c2hvd0JhbGFuY2VcbiAgICAgICAgICAgICAgICAgID8gTWVyY2hhbnRXYWxsZXRTZXJ2aWNlLmZvcm1hdEN1cnJlbmN5KHdhbGxldC5iYWxhbmNlKVxuICAgICAgICAgICAgICAgICAgOiAn4oCi4oCi4oCi4oCi4oCi4oCiJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMTAwIHRleHQtc20gbWItMVwiPlBlbmRpbmcgQmFsYW5jZTwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAge3Nob3dCYWxhbmNlXG4gICAgICAgICAgICAgICAgICA/IE1lcmNoYW50V2FsbGV0U2VydmljZS5mb3JtYXRDdXJyZW5jeSh3YWxsZXQucGVuZGluZ19iYWxhbmNlIHx8IDApXG4gICAgICAgICAgICAgICAgICA6ICfigKLigKLigKLigKLigKLigKInXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgcHQtMiBib3JkZXItdCBib3JkZXItYmx1ZS01MDAvMzBcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0xMDAgdGV4dC14c1wiPlRvdGFsOiB7c2hvd0JhbGFuY2VcbiAgICAgICAgICAgICAgPyBNZXJjaGFudFdhbGxldFNlcnZpY2UuZm9ybWF0Q3VycmVuY3koKHdhbGxldC5iYWxhbmNlIHx8IDApICsgKHdhbGxldC5wZW5kaW5nX2JhbGFuY2UgfHwgMCkpXG4gICAgICAgICAgICAgIDogJ+KAouKAouKAouKAouKAouKAoidcbiAgICAgICAgICAgIH08L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93VHJhbnNmZXJNb2RhbCh0cnVlKX1cbiAgICAgICAgICBkaXNhYmxlZD17d2FsbGV0LmJhbGFuY2UgPD0gMH1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUgdGV4dC1ibHVlLTYwMCBweS0yIHB4LTQgcm91bmRlZC1sZyBmb250LW1lZGl1bSBob3ZlcjpiZy1ibHVlLTUwIHRyYW5zaXRpb24tY29sb3JzIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxTZW5kIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgVHJhbnNmZXIgdG8gTWFpbiBXYWxsZXRcbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFN0YXRzIENhcmRzICovfVxuICAgICAge3N0YXRzICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLW9yYW5nZS0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtb3JhbmdlLTYwMFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTNcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5QZW5kaW5nPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtNZXJjaGFudFdhbGxldFNlcnZpY2UuZm9ybWF0Q3VycmVuY3koc3RhdHMucGVuZGluZ0JhbGFuY2UpfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ncmVlbi0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+VG90YWwgRWFybmVkPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtNZXJjaGFudFdhbGxldFNlcnZpY2UuZm9ybWF0Q3VycmVuY3koc3RhdHMudG90YWxFYXJuZWQpfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ibHVlLTEwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPEFycm93VXBSaWdodCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+VGhpcyBNb250aDwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICB7TWVyY2hhbnRXYWxsZXRTZXJ2aWNlLmZvcm1hdEN1cnJlbmN5KHN0YXRzLm1vbnRobHlFYXJuaW5ncyl9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLXB1cnBsZS0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxBcnJvd0Rvd25MZWZ0IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wdXJwbGUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtM1wiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPldpdGhkcmF3bjwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICB7TWVyY2hhbnRXYWxsZXRTZXJ2aWNlLmZvcm1hdEN1cnJlbmN5KHN0YXRzLnRvdGFsV2l0aGRyYXduKX1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIFJlY2VudCBUcmFuc2FjdGlvbnMgKi99XG4gICAgICB7c3RhdHM/LnJlY2VudFRyYW5zYWN0aW9ucyAmJiBzdGF0cy5yZWNlbnRUcmFuc2FjdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlJlY2VudCBUcmFuc2FjdGlvbnM8L2g0PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICB7c3RhdHMucmVjZW50VHJhbnNhY3Rpb25zLm1hcCgodHJhbnNhY3Rpb24pID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e3RyYW5zYWN0aW9uLmlkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHktMiBib3JkZXItYiBib3JkZXItZ3JheS0xMDAgbGFzdDpib3JkZXItYi0wXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1sZyAke1xuICAgICAgICAgICAgICAgICAgICB0cmFuc2FjdGlvbi50eXBlID09PSAnY3JlZGl0JyA/ICdiZy1ncmVlbi0xMDAnIDogJ2JnLXJlZC0xMDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHt0cmFuc2FjdGlvbi50eXBlID09PSAnY3JlZGl0JyA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dEb3duTGVmdCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dVcFJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtNZXJjaGFudFdhbGxldFNlcnZpY2UuZ2V0Q2F0ZWdvcnlEaXNwbGF5TmFtZSh0cmFuc2FjdGlvbi5jYXRlZ29yeSl9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWVBZ28odHJhbnNhY3Rpb24uY3JlYXRlZF9hdCl9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXNlbWlib2xkICR7XG4gICAgICAgICAgICAgICAgICAgIE1lcmNoYW50V2FsbGV0U2VydmljZS5nZXRUcmFuc2FjdGlvblR5cGVDb2xvcih0cmFuc2FjdGlvbi50eXBlKVxuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7dHJhbnNhY3Rpb24udHlwZSA9PT0gJ2NyZWRpdCcgPyAnKycgOiAnLSd9XG4gICAgICAgICAgICAgICAgICAgIHtNZXJjaGFudFdhbGxldFNlcnZpY2UuZm9ybWF0Q3VycmVuY3kodHJhbnNhY3Rpb24uYW1vdW50KX1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogVHJhbnNmZXIgTW9kYWwgKi99XG4gICAgICB7c2hvd1RyYW5zZmVyTW9kYWwgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHAtNiB3LWZ1bGwgbWF4LXctbWQgbXgtNFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5UcmFuc2ZlciB0byBNYWluIFdhbGxldDwvaDM+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBUcmFuc2ZlciBBbW91bnRcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtZ3JheS01MDBcIj5Sczwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3RyYW5zZmVyQW1vdW50fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRUcmFuc2ZlckFtb3VudChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjAuMDBcIlxuICAgICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgICBtYXg9e3dhbGxldC5iYWxhbmNlfVxuICAgICAgICAgICAgICAgICAgc3RlcD1cIjAuMDFcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHBsLTEwIHByLTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICBBdmFpbGFibGU6IHtNZXJjaGFudFdhbGxldFNlcnZpY2UuZm9ybWF0Q3VycmVuY3kod2FsbGV0LmJhbGFuY2UpfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1RyYW5zZmVyTW9kYWwoZmFsc2UpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweC00IHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCB0ZXh0LWdyYXktNzAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVUcmFuc2Zlcn1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17dHJhbnNmZXJMb2FkaW5nIHx8ICF0cmFuc2ZlckFtb3VudCB8fCBwYXJzZUZsb2F0KHRyYW5zZmVyQW1vdW50KSA8PSAwfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweC00IHB5LTIgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHt0cmFuc2ZlckxvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgJ1RyYW5zZmVyJ1xuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiV2FsbGV0IiwiVHJlbmRpbmdVcCIsIkFycm93VXBSaWdodCIsIkFycm93RG93bkxlZnQiLCJDbG9jayIsIkV5ZSIsIkV5ZU9mZiIsIlJlZnJlc2hDdyIsIlNlbmQiLCJNZXJjaGFudFdhbGxldFNlcnZpY2UiLCJzaG93Q29uZmlybWF0aW9uIiwic2hvd0FsZXJ0IiwidXNlS1lDQ2hlY2siLCJNZXJjaGFudFdhbGxldCIsInNob3BJZCIsInVzZXJJZCIsIndhbGxldCIsInNldFdhbGxldCIsInN0YXRzIiwic2V0U3RhdHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInRyYW5zZmVyTG9hZGluZyIsInNldFRyYW5zZmVyTG9hZGluZyIsInNob3dCYWxhbmNlIiwic2V0U2hvd0JhbGFuY2UiLCJ0cmFuc2ZlckFtb3VudCIsInNldFRyYW5zZmVyQW1vdW50Iiwic2hvd1RyYW5zZmVyTW9kYWwiLCJzZXRTaG93VHJhbnNmZXJNb2RhbCIsInJlcXVpcmVLWUNGb3JBY3Rpb24iLCJsb2FkV2FsbGV0RGF0YSIsIndhbGxldERhdGEiLCJzdGF0c0RhdGEiLCJQcm9taXNlIiwiYWxsIiwiZ2V0TWVyY2hhbnRXYWxsZXQiLCJ0aGVuIiwidyIsImdldE1lcmNoYW50V2FsbGV0U3RhdHMiLCJpZCIsImVycm9yIiwiY29uc29sZSIsInRpdGxlIiwibWVzc2FnZSIsInZhcmlhbnQiLCJoYW5kbGVUcmFuc2ZlciIsImFtb3VudCIsInBhcnNlRmxvYXQiLCJpc05hTiIsImJhbGFuY2UiLCJjb25maXJtZWQiLCJmb3JtYXRDdXJyZW5jeSIsImNvbmZpcm1UZXh0IiwidHJhbnNmZXJUb01haW5XYWxsZXQiLCJFcnJvciIsImZvcm1hdFRpbWVBZ28iLCJkYXRlU3RyaW5nIiwiZGF0ZSIsIkRhdGUiLCJub3ciLCJkaWZmSW5Ib3VycyIsIk1hdGgiLCJmbG9vciIsImdldFRpbWUiLCJkaWZmSW5EYXlzIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJwIiwiYnV0dG9uIiwib25DbGljayIsInBlbmRpbmdfYmFsYW5jZSIsImRpc2FibGVkIiwicGVuZGluZ0JhbGFuY2UiLCJ0b3RhbEVhcm5lZCIsIm1vbnRobHlFYXJuaW5ncyIsInRvdGFsV2l0aGRyYXduIiwicmVjZW50VHJhbnNhY3Rpb25zIiwibGVuZ3RoIiwiaDQiLCJtYXAiLCJ0cmFuc2FjdGlvbiIsInR5cGUiLCJnZXRDYXRlZ29yeURpc3BsYXlOYW1lIiwiY2F0ZWdvcnkiLCJjcmVhdGVkX2F0IiwiZ2V0VHJhbnNhY3Rpb25UeXBlQ29sb3IiLCJsYWJlbCIsInNwYW4iLCJpbnB1dCIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJtaW4iLCJtYXgiLCJzdGVwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MerchantWallet.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useKYCVerification.tsx":
/*!******************************************!*\
  !*** ./src/hooks/useKYCVerification.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useKYCCheck: function() { return /* binding */ useKYCCheck; },\n/* harmony export */   useKYCVerification: function() { return /* binding */ useKYCVerification; },\n/* harmony export */   withKYCVerification: function() { return /* binding */ withKYCVerification; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/kyc */ \"(app-pages-browser)/./src/lib/services/kyc.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nfunction useKYCVerification() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isVerified: false,\n        status: \"not_submitted\",\n        loading: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchKYCStatus();\n        } else {\n            setKycStatus({\n                isVerified: false,\n                status: \"not_submitted\",\n                loading: false\n            });\n        }\n    }, [\n        user\n    ]);\n    const fetchKYCStatus = async ()=>{\n        if (!user) return;\n        try {\n            setKycStatus((prev)=>({\n                    ...prev,\n                    loading: true,\n                    error: undefined\n                }));\n            const status = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCStatus(user.id);\n            setKycStatus({\n                isVerified: (status === null || status === void 0 ? void 0 : status.kyc_status) === \"approved\",\n                status: (status === null || status === void 0 ? void 0 : status.kyc_status) || \"not_submitted\",\n                submittedAt: status === null || status === void 0 ? void 0 : status.kyc_submitted_at,\n                approvedAt: status === null || status === void 0 ? void 0 : status.kyc_approved_at,\n                loading: false\n            });\n        } catch (error) {\n            console.error(\"Error fetching KYC status:\", error);\n            setKycStatus((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: error instanceof Error ? error.message : \"Failed to fetch KYC status\"\n                }));\n        }\n    };\n    const checkKYCRequired = (action)=>{\n        if (kycStatus.loading) {\n            return {\n                allowed: false,\n                message: \"Checking verification status...\"\n            };\n        }\n        if (!kycStatus.isVerified) {\n            const actionMessages = {\n                withdrawal: \"You must complete KYC verification before making withdrawals. Please go to Profile > Identity Verification to submit your documents.\",\n                p2p_transfer: \"You must complete KYC verification before making P2P transfers. Please go to Profile > Identity Verification to submit your documents.\",\n                shop_creation: \"You must complete KYC verification before creating a shop. Please go to Profile > Identity Verification to submit your documents.\",\n                default: \"You must complete KYC verification to perform this action. Please go to Profile > Identity Verification to submit your documents.\"\n            };\n            return {\n                allowed: false,\n                message: actionMessages[action] || actionMessages.default\n            };\n        }\n        return {\n            allowed: true\n        };\n    };\n    const refreshKYCStatus = ()=>{\n        if (user) {\n            fetchKYCStatus();\n        }\n    };\n    return {\n        kycStatus,\n        checkKYCRequired,\n        refreshKYCStatus\n    };\n}\n_s(useKYCVerification, \"zdlgqZeGInA6O7RvzrO2JywUqHE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n// Higher-order component for KYC protection\nfunction withKYCVerification(Component) {\n    let requiredAction = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"default\";\n    var _s = $RefreshSig$();\n    return _s(function KYCProtectedComponent(props) {\n        _s();\n        const { kycStatus, checkKYCRequired } = useKYCVerification();\n        const verification = checkKYCRequired(requiredAction);\n        if (kycStatus.loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this);\n        }\n        if (!verification.allowed) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-yellow-100 rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-6 w-6 text-yellow-600\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-yellow-800 mb-2\",\n                        children: \"KYC Verification Required\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-700 mb-4\",\n                        children: verification.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/dashboard/profile\",\n                        className: \"inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors\",\n                        children: \"Complete Verification\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\hooks\\\\useKYCVerification.tsx\",\n            lineNumber: 137,\n            columnNumber: 12\n        }, this);\n    }, \"ogPg9b+aoCpTO2DN13+2c7b5h04=\", false, function() {\n        return [\n            useKYCVerification\n        ];\n    });\n}\n// Hook for checking specific KYC requirements\nfunction useKYCCheck() {\n    _s1();\n    const { kycStatus, checkKYCRequired } = useKYCVerification();\n    const requireKYCForAction = async (action)=>{\n        const verification = checkKYCRequired(action);\n        if (!verification.allowed && verification.message) {\n            // You can integrate with your alert system here\n            console.warn(\"KYC verification required:\", verification.message);\n            return false;\n        }\n        return verification.allowed;\n    };\n    return {\n        kycStatus,\n        requireKYCForAction,\n        isKYCVerified: kycStatus.isVerified\n    };\n}\n_s1(useKYCCheck, \"ogPg9b+aoCpTO2DN13+2c7b5h04=\", false, function() {\n    return [\n        useKYCVerification\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useKYCVerification.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/kyc.ts":
/*!*********************************!*\
  !*** ./src/lib/services/kyc.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCService: function() { return /* binding */ KYCService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _kycStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kycStorage */ \"(app-pages-browser)/./src/lib/services/kycStorage.ts\");\n\n\nclass KYCService {\n    /**\n   * Get user's KYC status from users table\n   */ static async getUserKYCStatus(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"kyc_status, kyc_submitted_at, kyc_approved_at\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user KYC status:\", error);\n                // Return default status if columns don't exist yet\n                if (error.message.includes(\"column\") && error.message.includes(\"does not exist\")) {\n                    return {\n                        kyc_status: \"not_submitted\"\n                    };\n                }\n                throw new Error(\"Failed to fetch KYC status: \".concat(error.message));\n            }\n            return data || {\n                kyc_status: \"not_submitted\"\n            };\n        } catch (error) {\n            console.error(\"Error getting user KYC status:\", error);\n            // Return default status for any database errors\n            return {\n                kyc_status: \"not_submitted\"\n            };\n        }\n    }\n    /**\n   * Get user's KYC submission details\n   */ static async getUserKYCSubmission(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"*\").eq(\"user_id\", userId).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    // No submission found\n                    return null;\n                }\n                // Handle table not found error\n                if (error.message.includes(\"Could not find the table\") || error.message.includes(\"schema cache\")) {\n                    console.warn(\"KYC submissions table not found - database migration may be needed\");\n                    return null;\n                }\n                console.error(\"Error fetching KYC submission:\", error);\n                throw new Error(\"Failed to fetch KYC submission: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting user KYC submission:\", error);\n            // Return null for table not found errors instead of throwing\n            if (error instanceof Error && (error.message.includes(\"Could not find the table\") || error.message.includes(\"schema cache\"))) {\n                return null;\n            }\n            throw error;\n        }\n    }\n    /**\n   * Submit KYC application with documents\n   */ static async submitKYCApplication(userId, submissionData, documents) {\n        try {\n            // Check if user already has a submission\n            const existingSubmission = await this.getUserKYCSubmission(userId);\n            if (existingSubmission && existingSubmission.status !== \"rejected\") {\n                throw new Error(\"You already have a pending or approved KYC submission\");\n            }\n            // Prepare document uploads\n            const documentUploads = [\n                {\n                    file: documents.id_front,\n                    type: \"id_front\"\n                },\n                {\n                    file: documents.id_back,\n                    type: \"id_back\"\n                },\n                {\n                    file: documents.selfie,\n                    type: \"selfie\"\n                },\n                {\n                    file: documents.address_proof,\n                    type: \"address_proof\"\n                }\n            ];\n            // Upload all documents\n            console.log(\"Uploading KYC documents...\");\n            const uploadedDocuments = await _kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.uploadKYCDocuments(documentUploads, userId);\n            // If there's an existing rejected submission, delete it first\n            if (existingSubmission) {\n                await this.deleteKYCSubmission(existingSubmission.id);\n            }\n            // Create KYC submission record\n            const submissionRecord = {\n                user_id: userId,\n                id_document_front_url: uploadedDocuments.id_front,\n                id_document_back_url: uploadedDocuments.id_back,\n                selfie_photo_url: uploadedDocuments.selfie,\n                address_proof_url: uploadedDocuments.address_proof,\n                id_document_type: submissionData.id_document_type,\n                id_document_number: submissionData.id_document_number,\n                full_name: submissionData.full_name,\n                date_of_birth: submissionData.date_of_birth,\n                address: submissionData.address,\n                submission_notes: submissionData.submission_notes,\n                status: \"pending\"\n            };\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).insert(submissionRecord).select().single();\n            if (error) {\n                console.error(\"Error creating KYC submission:\", error);\n                throw new Error(\"Failed to submit KYC application: \".concat(error.message));\n            }\n            console.log(\"KYC application submitted successfully\");\n            return data;\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all KYC submissions for admin review\n   */ static async getAllKYCSubmissions() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, status = arguments.length > 2 ? arguments[2] : void 0, searchTerm = arguments.length > 3 ? arguments[3] : void 0;\n        try {\n            // First, try the direct join approach\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"\\n          *,\\n          user:users(full_name, email)\\n        \", {\n                count: \"exact\"\n            });\n            // Apply filters\n            if (status) {\n                query = query.eq(\"status\", status);\n            }\n            if (searchTerm) {\n                query = query.or(\"full_name.ilike.%\".concat(searchTerm, \"%,id_document_number.ilike.%\").concat(searchTerm, \"%\"));\n            }\n            // Apply pagination\n            const offset = (page - 1) * limit;\n            query = query.order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            const { data, error, count } = await query;\n            if (error) {\n                console.error(\"Error fetching KYC submissions with join:\", error);\n                // Fallback: Get submissions first, then get user data separately\n                console.log(\"Trying fallback approach...\");\n                let fallbackQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"*\", {\n                    count: \"exact\"\n                });\n                if (status) {\n                    fallbackQuery = fallbackQuery.eq(\"status\", status);\n                }\n                if (searchTerm) {\n                    fallbackQuery = fallbackQuery.or(\"full_name.ilike.%\".concat(searchTerm, \"%,id_document_number.ilike.%\").concat(searchTerm, \"%\"));\n                }\n                const fallbackOffset = (page - 1) * limit;\n                fallbackQuery = fallbackQuery.order(\"created_at\", {\n                    ascending: false\n                }).range(fallbackOffset, fallbackOffset + limit - 1);\n                const { data: submissions, error: submissionsError, count: submissionsCount } = await fallbackQuery;\n                if (submissionsError) {\n                    throw new Error(\"Failed to fetch KYC submissions: \".concat(submissionsError.message));\n                }\n                // Get user data for each submission\n                const submissionsWithUsers = await Promise.all((submissions || []).map(async (submission)=>{\n                    const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"full_name, email\").eq(\"id\", submission.user_id).single();\n                    if (userError) {\n                        console.warn(\"Could not fetch user data for submission \".concat(submission.id, \":\"), userError);\n                        return {\n                            ...submission,\n                            user: {\n                                full_name: \"Unknown User\",\n                                email: \"<EMAIL>\"\n                            }\n                        };\n                    }\n                    return {\n                        ...submission,\n                        user: userData\n                    };\n                }));\n                return {\n                    submissions: submissionsWithUsers,\n                    total: submissionsCount || 0\n                };\n            }\n            return {\n                submissions: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting all KYC submissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Review KYC submission (admin only)\n   */ static async reviewKYCSubmission(submissionId, reviewData, reviewerId) {\n        try {\n            const updateData = {\n                status: reviewData.status,\n                reviewed_by: reviewerId,\n                reviewed_at: new Date().toISOString(),\n                rejection_reason: reviewData.rejection_reason,\n                admin_notes: reviewData.admin_notes\n            };\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).update(updateData).eq(\"id\", submissionId).select().single();\n            if (error) {\n                console.error(\"Error reviewing KYC submission:\", error);\n                throw new Error(\"Failed to review KYC submission: \".concat(error.message));\n            }\n            console.log(\"KYC submission \".concat(submissionId, \" reviewed as \").concat(reviewData.status));\n            return data;\n        } catch (error) {\n            console.error(\"Error reviewing KYC submission:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get KYC status history for a user\n   */ static async getKYCStatusHistory(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_STATUS_HISTORY).select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching KYC status history:\", error);\n                throw new Error(\"Failed to fetch KYC status history: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC status history:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Check if user is KYC verified\n   */ static async isUserKYCVerified(userId) {\n        try {\n            const status = await this.getUserKYCStatus(userId);\n            return (status === null || status === void 0 ? void 0 : status.kyc_status) === \"approved\";\n        } catch (error) {\n            console.error(\"Error checking KYC verification status:\", error);\n            return false;\n        }\n    }\n    /**\n   * Get KYC document signed URLs for admin review\n   */ static async getKYCDocumentUrls(submission) {\n        try {\n            const filePaths = [\n                submission.id_document_front_url,\n                submission.id_document_back_url,\n                submission.selfie_photo_url,\n                submission.address_proof_url\n            ];\n            const signedUrls = await _kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.getKYCDocumentSignedUrls(filePaths, 3600) // 1 hour expiry\n            ;\n            return {\n                id_front_url: signedUrls[submission.id_document_front_url],\n                id_back_url: signedUrls[submission.id_document_back_url],\n                selfie_url: signedUrls[submission.selfie_photo_url],\n                address_proof_url: signedUrls[submission.address_proof_url]\n            };\n        } catch (error) {\n            console.error(\"Error getting KYC document URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC submission (for resubmission)\n   */ static async deleteKYCSubmission(submissionId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).delete().eq(\"id\", submissionId);\n            if (error) {\n                console.error(\"Error deleting KYC submission:\", error);\n                throw new Error(\"Failed to delete KYC submission: \".concat(error.message));\n            }\n        } catch (error) {\n            console.error(\"Error deleting KYC submission:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get KYC document types\n   */ static async getKYCDocumentTypes() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_DOCUMENT_TYPES).select(\"*\").eq(\"is_active\", true).order(\"name\");\n            if (error) {\n                console.error(\"Error fetching KYC document types:\", error);\n                throw new Error(\"Failed to fetch document types: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC document types:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kyc.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/kycStorage.ts":
/*!****************************************!*\
  !*** ./src/lib/services/kycStorage.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCStorageService: function() { return /* binding */ KYCStorageService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass KYCStorageService {\n    /**\n   * Ensure KYC bucket exists before operations\n   */ static async ensureBucketExists() {\n        try {\n            // Check if bucket exists\n            const { data: buckets, error: listError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.listBuckets();\n            if (listError) {\n                console.error(\"Error listing buckets:\", listError);\n                console.log(\"Assuming KYC bucket exists and continuing...\");\n                return;\n            }\n            const bucketExists = buckets === null || buckets === void 0 ? void 0 : buckets.some((bucket)=>bucket.name === this.BUCKET_NAME);\n            if (!bucketExists) {\n                console.log(\"Creating KYC bucket: \".concat(this.BUCKET_NAME));\n                const { error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.createBucket(this.BUCKET_NAME, {\n                    public: false,\n                    allowedMimeTypes: this.ALLOWED_TYPES,\n                    fileSizeLimit: this.MAX_FILE_SIZE\n                });\n                if (createError) {\n                    console.error(\"Error creating KYC bucket:\", createError);\n                    // If bucket creation fails due to RLS policies, assume it exists\n                    if (createError.message.includes(\"row-level security\") || createError.message.includes(\"already exists\")) {\n                        console.log(\"KYC bucket likely exists or creation blocked by RLS. Continuing...\");\n                        return;\n                    }\n                    throw new Error(\"Failed to create KYC storage bucket: \".concat(createError.message));\n                }\n                console.log(\"KYC bucket created successfully\");\n            }\n        } catch (error) {\n            console.error(\"Error ensuring KYC bucket exists:\", error);\n            // Don't throw error for bucket existence check - assume it exists\n            console.log(\"Continuing with KYC operations assuming bucket exists...\");\n        }\n    }\n    /**\n   * Validate KYC document file\n   */ static validateFile(file, documentType) {\n        // Check file size\n        if (file.size > this.MAX_FILE_SIZE) {\n            throw new Error(\"File size must be less than \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB\"));\n        }\n        // Check file type\n        if (!this.ALLOWED_TYPES.includes(file.type)) {\n            throw new Error(\"File type \".concat(file.type, \" is not supported. Allowed types: \").concat(this.ALLOWED_TYPES.join(\", \")));\n        }\n        // Additional validation for specific document types\n        if (documentType === \"selfie\" && file.type === \"application/pdf\") {\n            throw new Error(\"Selfie photos must be image files, not PDF\");\n        }\n        // Check if file name is reasonable\n        if (file.name.length > 255) {\n            throw new Error(\"File name is too long\");\n        }\n    }\n    /**\n   * Generate secure filename for KYC document\n   */ static generateSecureFileName(userId, documentType, originalName) {\n        var _originalName_split_pop;\n        const timestamp = Date.now();\n        const randomId = Math.random().toString(36).substring(2);\n        const fileExt = ((_originalName_split_pop = originalName.split(\".\").pop()) === null || _originalName_split_pop === void 0 ? void 0 : _originalName_split_pop.toLowerCase()) || \"jpg\";\n        // Create folder structure: userId/documentType/timestamp-randomId.ext\n        return \"\".concat(userId, \"/\").concat(documentType, \"/\").concat(timestamp, \"-\").concat(randomId, \".\").concat(fileExt);\n    }\n    /**\n   * Upload a single KYC document\n   */ static async uploadKYCDocument(file, userId, documentType) {\n        try {\n            // Ensure bucket exists\n            await this.ensureBucketExists();\n            // Validate file\n            this.validateFile(file, documentType);\n            // Generate secure filename\n            const fileName = this.generateSecureFileName(userId, documentType, file.name);\n            console.log(\"Uploading KYC document: \".concat(fileName, \" to bucket: \").concat(this.BUCKET_NAME));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).upload(fileName, file, {\n                cacheControl: \"3600\",\n                upsert: false // Don't overwrite existing files\n            });\n            if (error) {\n                console.error(\"KYC document upload error:\", error);\n                throw new Error(\"Upload failed: \".concat(error.message));\n            }\n            console.log(\"KYC document upload successful:\", data);\n            // For private buckets, we need to create signed URLs for access\n            // Return the file path for now, signed URL will be generated when needed\n            return fileName;\n        } catch (error) {\n            console.error(\"Error uploading KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upload multiple KYC documents\n   */ static async uploadKYCDocuments(documents, userId) {\n        const results = {};\n        try {\n            // Upload all documents concurrently\n            const uploadPromises = documents.map(async (doc)=>{\n                const filePath = await this.uploadKYCDocument(doc.file, userId, doc.type);\n                return {\n                    type: doc.type,\n                    filePath\n                };\n            });\n            const uploadResults = await Promise.all(uploadPromises);\n            // Build results object\n            uploadResults.forEach((result)=>{\n                results[result.type] = result.filePath;\n            });\n            return results;\n        } catch (error) {\n            console.error(\"Error uploading multiple KYC documents:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get signed URL for KYC document (for admin review)\n   */ static async getKYCDocumentSignedUrl(filePath) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).createSignedUrl(filePath, expiresIn);\n            if (error) {\n                console.error(\"Error creating signed URL:\", error);\n                throw new Error(\"Failed to create signed URL: \".concat(error.message));\n            }\n            return data.signedUrl;\n        } catch (error) {\n            console.error(\"Error getting KYC document signed URL:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC document (for cleanup or resubmission)\n   */ static async deleteKYCDocument(filePath) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).remove([\n                filePath\n            ]);\n            if (error) {\n                console.error(\"Error deleting KYC document:\", error);\n                throw new Error(\"Failed to delete document: \".concat(error.message));\n            }\n            console.log(\"KYC document deleted successfully:\", filePath);\n        } catch (error) {\n            console.error(\"Error deleting KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get multiple signed URLs for KYC documents\n   */ static async getKYCDocumentSignedUrls(filePaths) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const urlPromises = filePaths.map(async (filePath)=>{\n                const signedUrl = await this.getKYCDocumentSignedUrl(filePath, expiresIn);\n                return {\n                    filePath,\n                    signedUrl\n                };\n            });\n            const results = await Promise.all(urlPromises);\n            const urlMap = {};\n            results.forEach((result)=>{\n                urlMap[result.filePath] = result.signedUrl;\n            });\n            return urlMap;\n        } catch (error) {\n            console.error(\"Error getting multiple KYC document signed URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Validate document type enum\n   */ static isValidDocumentType(type) {\n        return [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ].includes(type);\n    }\n    /**\n   * Get human-readable document type name\n   */ static getDocumentTypeName(type) {\n        const names = {\n            id_front: \"ID Document (Front)\",\n            id_back: \"ID Document (Back)\",\n            selfie: \"Selfie Photo\",\n            address_proof: \"Address Proof\"\n        };\n        return names[type];\n    }\n    /**\n   * Get document type requirements\n   */ static getDocumentTypeRequirements(type) {\n        const requirements = {\n            id_front: \"Clear photo of the front side of your government-issued ID (National ID, Passport, or Driving License)\",\n            id_back: \"Clear photo of the back side of your government-issued ID\",\n            selfie: \"Clear selfie photo holding your ID document next to your face\",\n            address_proof: \"Recent utility bill, bank statement, or official document showing your address (PDF or image)\"\n        };\n        return requirements[type];\n    }\n}\nKYCStorageService.BUCKET_NAME = \"kyc-documents\";\nKYCStorageService.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for KYC documents\n;\nKYCStorageService.ALLOWED_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/webp\",\n    \"application/pdf\" // Allow PDF for address proof documents\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kycStorage.ts\n"));

/***/ })

});