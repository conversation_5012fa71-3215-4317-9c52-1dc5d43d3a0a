"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx":
/*!****************************************************!*\
  !*** ./src/components/kyc/KYCVerificationForm.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KYCVerificationForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/kyc */ \"(app-pages-browser)/./src/lib/services/kyc.ts\");\n/* harmony import */ var _KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./KYCStatusBadge */ \"(app-pages-browser)/./src/components/kyc/KYCStatusBadge.tsx\");\n/* harmony import */ var _KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./KYCDocumentUpload */ \"(app-pages-browser)/./src/components/kyc/KYCDocumentUpload.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_premium__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/premium */ \"(app-pages-browser)/./src/components/ui/premium/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCVerificationForm(param) {\n    let { onSubmissionComplete } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // KYC Status\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [kycSubmission, setKycSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Form Data\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id_document_type: \"national_id\",\n        id_document_number: \"\",\n        full_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        submission_notes: \"\"\n    });\n    // Document Files\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [documentErrors, setDocumentErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Document Types\n    const [documentTypes, setDocumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchKYCData();\n            fetchDocumentTypes();\n        }\n    }, [\n        user\n    ]);\n    const fetchKYCData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [status, submission] = await Promise.all([\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCStatus(user.id),\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCSubmission(user.id)\n            ]);\n            setKycStatus(status);\n            setKycSubmission(submission);\n            // Pre-fill form with existing submission data\n            if (submission) {\n                setFormData({\n                    id_document_type: submission.id_document_type,\n                    id_document_number: submission.id_document_number || \"\",\n                    full_name: submission.full_name,\n                    date_of_birth: submission.date_of_birth || \"\",\n                    address: submission.address,\n                    submission_notes: submission.submission_notes || \"\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching KYC data:\", error);\n            setError(\"Failed to load KYC information\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        user\n    ]);\n    const fetchDocumentTypes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const types = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getKYCDocumentTypes();\n            setDocumentTypes(types);\n        } catch (error) {\n            console.error(\"Error fetching document types:\", error);\n        }\n    }, []);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setError(\"\");\n    }, []);\n    const handleDocumentSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((documentType, file)=>{\n        if (file) {\n            setDocuments((prev)=>({\n                    ...prev,\n                    [documentType]: file\n                }));\n            setDocumentErrors((prev)=>({\n                    ...prev,\n                    [documentType]: \"\"\n                }));\n        } else {\n            setDocuments((prev)=>{\n                const newDocs = {\n                    ...prev\n                };\n                delete newDocs[documentType];\n                return newDocs;\n            });\n        }\n    }, []);\n    const validateForm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const errors = {};\n        let isValid = true;\n        // Validate required fields\n        if (!formData.full_name.trim()) {\n            setError(\"Full name is required\");\n            isValid = false;\n        }\n        if (!formData.address.trim()) {\n            setError(\"Address is required\");\n            isValid = false;\n        }\n        // Validate required documents\n        const requiredDocs = [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ];\n        for (const docType of requiredDocs){\n            if (!documents[docType]) {\n                errors[docType] = \"This document is required\";\n                isValid = false;\n            }\n        }\n        setDocumentErrors(errors);\n        if (!isValid && !error) {\n            setError(\"Please fill in all required fields and upload all required documents\");\n        }\n        return isValid;\n    }, [\n        formData.full_name,\n        formData.address,\n        documents,\n        error\n    ]);\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (e)=>{\n        e.preventDefault();\n        if (!user || !validateForm()) return;\n        try {\n            setSubmitting(true);\n            setError(\"\");\n            const kycDocuments = {\n                id_front: documents.id_front,\n                id_back: documents.id_back,\n                selfie: documents.selfie,\n                address_proof: documents.address_proof\n            };\n            await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.submitKYCApplication(user.id, formData, kycDocuments);\n            setSuccess(\"KYC application submitted successfully! We will review your documents and notify you of the result.\");\n            // Refresh KYC data\n            await fetchKYCData();\n            if (onSubmissionComplete) {\n                onSubmissionComplete();\n            }\n            // Clear form\n            setDocuments({});\n            setDocumentErrors({});\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to submit KYC application\");\n        } finally{\n            setSubmitting(false);\n        }\n    }, [\n        user,\n        validateForm,\n        documents,\n        formData,\n        fetchKYCData,\n        onSubmissionComplete\n    ]);\n    // Memoize computed values to prevent unnecessary re-renders\n    const canSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>!kycSubmission || kycSubmission.status === \"rejected\", [\n        kycSubmission\n    ]);\n    const isVerified = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) === \"approved\", [\n        kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCStatusCard, {\n                    status: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\",\n                    submittedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_submitted_at,\n                    approvedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_approved_at,\n                    rejectionReason: kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.rejection_reason\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                delay: 0.1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Verification Progress\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCProgress, {\n                            currentStatus: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            canSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.SlideInUp, {\n                delay: 0.2,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-primary-blue/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-primary-blue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit KYC Documents\" : \"Submit KYC Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Complete your identity verification to unlock all features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Full Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    id: \"kyc-full-name\",\n                                                    type: \"text\",\n                                                    value: formData.full_name,\n                                                    onChange: (e)=>handleInputChange(\"full_name\", e.target.value),\n                                                    placeholder: \"Enter your full name as per ID\",\n                                                    required: true,\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Date of Birth\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    id: \"kyc-date-of-birth\",\n                                                    type: \"date\",\n                                                    value: formData.date_of_birth,\n                                                    onChange: (e)=>handleInputChange(\"date_of_birth\", e.target.value),\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Type *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"kyc-document-type\",\n                                                    value: formData.id_document_type,\n                                                    onChange: (e)=>handleInputChange(\"id_document_type\", e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                    required: true,\n                                                    children: documentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type.id,\n                                                            children: type.name\n                                                        }, type.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    id: \"kyc-document-number\",\n                                                    type: \"text\",\n                                                    value: formData.id_document_number,\n                                                    onChange: (e)=>handleInputChange(\"id_document_number\", e.target.value),\n                                                    placeholder: \"Enter ID number\",\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Address *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"kyc-address\",\n                                            value: formData.address,\n                                            onChange: (e)=>handleInputChange(\"address\", e.target.value),\n                                            placeholder: \"Enter your full address\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Required Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_front\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_front\", file),\n                                                    selectedFile: documents.id_front,\n                                                    error: documentErrors.id_front,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_back\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_back\", file),\n                                                    selectedFile: documents.id_back,\n                                                    error: documentErrors.id_back,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"selfie\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"selfie\", file),\n                                                    selectedFile: documents.selfie,\n                                                    error: documentErrors.selfie,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"address_proof\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"address_proof\", file),\n                                                    selectedFile: documents.address_proof,\n                                                    error: documentErrors.address_proof,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.submission_notes,\n                                            onChange: (e)=>handleInputChange(\"submission_notes\", e.target.value),\n                                            placeholder: \"Any additional information you'd like to provide\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                    children: [\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 19\n                                        }, this),\n                                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-green-600 bg-green-50 border border-green-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: success\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumButton, {\n                                        type: \"submit\",\n                                        disabled: submitting,\n                                        className: \"px-8 py-3\",\n                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 23\n                                                }, this),\n                                                (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit Documents\" : \"Submit for Verification\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCVerificationForm, \"NksgwO5G1kpZzqYHGDng3YTISnY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = KYCVerificationForm;\nvar _c;\n$RefreshReg$(_c, \"KYCVerificationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx\n"));

/***/ })

});