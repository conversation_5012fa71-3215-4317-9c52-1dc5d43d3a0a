"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/wallet/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/wallet/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WalletPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_wallet_CommissionBreakdownCards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/wallet/CommissionBreakdownCards */ \"(app-pages-browser)/./src/components/wallet/CommissionBreakdownCards.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_services_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/storage */ \"(app-pages-browser)/./src/lib/services/storage.ts\");\n/* harmony import */ var _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/commissionSystem */ \"(app-pages-browser)/./src/lib/services/commissionSystem.ts\");\n/* harmony import */ var _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useKYCVerification */ \"(app-pages-browser)/./src/hooks/useKYCVerification.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/AlertContext */ \"(app-pages-browser)/./src/contexts/AlertContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { showAlert } = (0,_contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__.useAlert)();\n    const { requireKYCForAction, isKYCVerified } = (0,_hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__.useKYCCheck)();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transfers, setTransfers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [depositRequests, setDepositRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [withdrawalRequests, setWithdrawalRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalBalance: 0,\n        totalDeposits: 0,\n        transfersSent: 0,\n        transfersReceived: 0,\n        totalWithdrawals: 0,\n        pendingDeposits: 0\n    });\n    const [commissionBreakdown, setCommissionBreakdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        directCommission: 0,\n        levelCommission: 0,\n        rsmBonus: 0,\n        zmBonus: 0,\n        okdoiHeadCommission: 0,\n        totalCommissions: 0\n    });\n    const [extendedCommissionData, setExtendedCommissionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        directCommission: 0,\n        levelCommission: 0,\n        voucherCommission: 0,\n        festivalBonus: 0,\n        savingCommission: 0,\n        giftCenterCommission: 0,\n        entertainmentCommission: 0,\n        medicalCommission: 0,\n        educationCommission: 0,\n        creditCommission: 0,\n        // ZM specific\n        zmBonuses: 0,\n        petralAllowanceZM: 0,\n        leasingFacilityZM: 0,\n        phoneBillZM: 0,\n        // RSM specific\n        rsmBonuses: 0,\n        petralAllowanceRSM: 0,\n        leasingFacilityRSM: 0,\n        phoneBillRSM: 0,\n        // OKDOI Head specific\n        okdoiHeadCommission: 0,\n        totalCommissions: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_transactions\");\n    // Modal states\n    const [showTransferModal, setShowTransferModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDepositModal, setShowDepositModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWithdrawalModal, setShowWithdrawalModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Transfer form\n    const [transferForm, setTransferForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        receiverEmail: \"\",\n        amount: \"\",\n        description: \"\"\n    });\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Deposit form\n    const [depositForm, setDepositForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        depositor_name: \"\",\n        notes: \"\",\n        terms_accepted: false\n    });\n    const [depositProof, setDepositProof] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [depositLoading, setDepositLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Withdrawal form\n    const [withdrawalForm, setWithdrawalForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        bank_name: \"\",\n        account_number: \"\",\n        account_holder_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        branch: \"\",\n        notes: \"\"\n    });\n    const [withdrawalLoading, setWithdrawalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchWalletData();\n        }\n    }, [\n        user\n    ]);\n    const fetchWalletData = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            setError(null);\n            console.log(\"Fetching wallet data for user:\", user.id);\n            // Fetch wallet with error handling\n            const walletData = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserWallet(user.id);\n            setWallet(walletData);\n            console.log(\"Wallet data fetched:\", walletData);\n            // Fetch recent transactions with error handling\n            const { transactions: transactionsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getWalletTransactions(user.id, 1, 10);\n            setTransactions(transactionsData || []);\n            console.log(\"Transactions fetched:\", (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.length) || 0);\n            // Fetch recent transfers with error handling\n            const { transfers: transfersData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserTransfers(user.id, 1, 10);\n            setTransfers(transfersData || []);\n            console.log(\"Transfers fetched:\", (transfersData === null || transfersData === void 0 ? void 0 : transfersData.length) || 0);\n            // Fetch deposit requests with error handling\n            const { requests: depositsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserDepositRequests(user.id, 1, 10);\n            setDepositRequests(depositsData || []);\n            console.log(\"Deposit requests fetched:\", (depositsData === null || depositsData === void 0 ? void 0 : depositsData.length) || 0);\n            // Fetch withdrawal requests with error handling\n            const { requests: withdrawalsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserWithdrawalRequests(user.id, 1, 10);\n            setWithdrawalRequests(withdrawalsData || []);\n            console.log(\"Withdrawal requests fetched:\", (withdrawalsData === null || withdrawalsData === void 0 ? void 0 : withdrawalsData.length) || 0);\n            // Fetch commission breakdown\n            try {\n                const commissionData = await _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__.CommissionSystemService.getCommissionBreakdown(user.id);\n                setCommissionBreakdown(commissionData);\n                // Get real extended commission data from the database\n                const extendedData = await _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__.CommissionSystemService.getExtendedCommissionBreakdown(user.id);\n                setExtendedCommissionData(extendedData);\n                console.log(\"Commission breakdown fetched:\", commissionData);\n            } catch (commissionError) {\n                console.error(\"Error fetching commission breakdown:\", commissionError);\n            // Don't fail the entire load if commission data fails\n            }\n            // Calculate stats with null safety\n            const totalDeposits = (transactionsData || []).filter((t)=>t.transaction_type === \"deposit\" && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const transfersSent = (transfersData || []).filter((t)=>t.sender_id === user.id && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const transfersReceived = (transfersData || []).filter((t)=>t.receiver_id === user.id && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const totalWithdrawals = (transactionsData || []).filter((t)=>t.transaction_type === \"withdrawal\" && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const pendingDeposits = (depositsData || []).filter((d)=>d.status === \"pending\").reduce((sum, d)=>sum + (d.amount || 0), 0);\n            setStats({\n                totalBalance: (walletData === null || walletData === void 0 ? void 0 : walletData.balance) || 0,\n                totalDeposits,\n                transfersSent,\n                transfersReceived,\n                totalWithdrawals,\n                pendingDeposits\n            });\n            console.log(\"Wallet data fetch completed successfully\");\n        } catch (err) {\n            console.error(\"Error fetching wallet data:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load wallet data\");\n            // Reset data on error\n            setWallet(null);\n            setTransactions([]);\n            setTransfers([]);\n            setDepositRequests([]);\n            setWithdrawalRequests([]);\n            setStats({\n                totalBalance: 0,\n                totalDeposits: 0,\n                transfersSent: 0,\n                transfersReceived: 0,\n                totalWithdrawals: 0,\n                pendingDeposits: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTransfer = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Check KYC verification status\n        const canTransfer = await requireKYCForAction(\"p2p_transfer\");\n        if (!canTransfer) {\n            await showAlert({\n                title: \"KYC Verification Required\",\n                message: \"You must complete KYC verification before making P2P transfers. Please go to Profile > Identity Verification to submit your documents.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setTransferLoading(true);\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createP2PTransfer(user.id, transferForm.receiverEmail, parseFloat(transferForm.amount), transferForm.description || undefined);\n            setShowTransferModal(false);\n            setTransferForm({\n                receiverEmail: \"\",\n                amount: \"\",\n                description: \"\"\n            });\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Transfer completed successfully!\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Transfer failed\",\n                variant: \"danger\"\n            });\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    const handleDeposit = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Validation\n        if (!depositForm.terms_accepted) {\n            await showAlert({\n                title: \"Terms Required\",\n                message: \"Please accept the terms and conditions to proceed.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        if (!depositProof) {\n            await showAlert({\n                title: \"Proof Required\",\n                message: \"Please upload a deposit receipt or proof of payment.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setDepositLoading(true);\n            // Upload proof file first\n            let proofUrl = \"\";\n            if (depositProof) {\n                proofUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_6__.StorageService.uploadImage(depositProof, user.id);\n            }\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createDepositRequest(user.id, {\n                amount: parseFloat(depositForm.amount),\n                depositor_name: depositForm.depositor_name,\n                notes: depositForm.notes || undefined,\n                deposit_slip_url: proofUrl\n            });\n            setShowDepositModal(false);\n            setDepositForm({\n                amount: \"\",\n                depositor_name: \"\",\n                notes: \"\",\n                terms_accepted: false\n            });\n            setDepositProof(null);\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Deposit request submitted successfully! We will review it shortly.\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Failed to submit deposit request\",\n                variant: \"danger\"\n            });\n        } finally{\n            setDepositLoading(false);\n        }\n    };\n    const handleWithdrawal = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Check KYC verification status\n        const canWithdraw = await requireKYCForAction(\"wallet_withdrawal\");\n        if (!canWithdraw) {\n            await showAlert({\n                title: \"KYC Verification Required\",\n                message: \"You must complete KYC verification before making withdrawals. Please go to Profile > Identity Verification to submit your documents.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setWithdrawalLoading(true);\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createWithdrawalRequest(user.id, {\n                amount: parseFloat(withdrawalForm.amount),\n                bank_name: withdrawalForm.bank_name,\n                account_number: withdrawalForm.account_number,\n                account_holder_name: withdrawalForm.account_holder_name,\n                branch: withdrawalForm.branch || undefined,\n                notes: withdrawalForm.notes || undefined\n            });\n            setShowWithdrawalModal(false);\n            setWithdrawalForm({\n                amount: \"\",\n                bank_name: \"\",\n                account_number: \"\",\n                account_holder_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n                branch: \"\",\n                notes: \"\"\n            });\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Withdrawal request submitted successfully! We will process it shortly.\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Failed to submit withdrawal request\",\n                variant: \"danger\"\n            });\n        } finally{\n            setWithdrawalLoading(false);\n        }\n    };\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 16\n                }, this);\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 16\n                }, this);\n            case \"commission\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTransactionColor = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n            case \"commission\":\n                return \"text-green-600\";\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return \"text-red-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 456,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 455,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Error Loading Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 466,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 465,\n            columnNumber: 7\n        }, this);\n    }\n    if (!wallet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Wallet Not Found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Your wallet is being set up. Please try again in a moment.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 485,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 484,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"My Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage your funds and transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowDepositModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Funds\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (!isKYCVerified) {\n                                                showAlert({\n                                                    title: \"KYC Verification Required\",\n                                                    message: \"You must complete KYC verification before sending money. Please go to Profile > Identity Verification to submit your documents.\",\n                                                    variant: \"warning\"\n                                                });\n                                                return;\n                                            }\n                                            setShowTransferModal(true);\n                                        },\n                                        disabled: !isKYCVerified,\n                                        className: \"flex items-center px-4 py-2 rounded-lg transition-colors \".concat(isKYCVerified ? \"bg-primary-blue text-white hover:bg-primary-blue/90\" : \"bg-gray-300 text-gray-500 cursor-not-allowed\"),\n                                        title: !isKYCVerified ? \"KYC verification required\" : \"\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Send Money\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (!isKYCVerified) {\n                                                showAlert({\n                                                    title: \"KYC Verification Required\",\n                                                    message: \"You must complete KYC verification before withdrawing funds. Please go to Profile > Identity Verification to submit your documents.\",\n                                                    variant: \"warning\"\n                                                });\n                                                return;\n                                            }\n                                            setShowWithdrawalModal(true);\n                                        },\n                                        disabled: !isKYCVerified,\n                                        className: \"flex items-center px-4 py-2 rounded-lg transition-colors \".concat(isKYCVerified ? \"bg-purple-600 text-white hover:bg-purple-700\" : \"bg-gray-300 text-gray-500 cursor-not-allowed\"),\n                                        title: !isKYCVerified ? \"KYC verification required\" : \"\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Withdraw\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-blue to-secondary-blue rounded-xl p-8 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-100 mb-2\",\n                                            children: \"Available Balance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.totalDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Transfers Sent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.transfersSent)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Transfers Received\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.transfersReceived)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-6 w-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Withdrawals\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.totalWithdrawals)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Pending Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.pendingDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_CommissionBreakdownCards__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        data: extendedCommissionData,\n                        userType: user.user_type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                {\n                                    id: \"all_transactions\",\n                                    name: \"All Transactions\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"]\n                                },\n                                {\n                                    id: \"fund_transfers\",\n                                    name: \"Fund Transfers\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                                },\n                                {\n                                    id: \"deposits\",\n                                    name: \"Deposits\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                                },\n                                {\n                                    id: \"withdrawals\",\n                                    name: \"Withdrawals\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n                                },\n                                {\n                                    id: \"commissions\",\n                                    name: \"Commissions\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 17\n                                        }, this),\n                                        tab.name\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            activeTab === \"all_transactions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"All Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.map((transaction)=>{\n                                                var _transaction_p2p_transfer_receiver, _transaction_p2p_transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                                                                            children: transaction.transaction_type.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.reference_type === \"p2p_transfer\" && transaction.p2p_transfer ? transaction.transaction_type === \"transfer_out\" ? \"Transfer to \".concat(((_transaction_p2p_transfer_receiver = transaction.p2p_transfer.receiver) === null || _transaction_p2p_transfer_receiver === void 0 ? void 0 : _transaction_p2p_transfer_receiver.full_name) || \"Unknown\") : \"Transfer from \".concat(((_transaction_p2p_transfer_sender = transaction.p2p_transfer.sender) === null || _transaction_p2p_transfer_sender === void 0 ? void 0 : _transaction_p2p_transfer_sender.full_name) || \"Unknown\") : transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transaction.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transaction.reference_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transaction.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 716,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium mr-2 \".concat(getTransactionColor(transaction.transaction_type)),\n                                                                            children: [\n                                                                                transaction.transaction_type.includes(\"in\") || transaction.transaction_type === \"deposit\" || transaction.transaction_type === \"refund\" ? \"+\" : \"-\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.amount)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 723,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(transaction.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Balance: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.balance_after)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transactions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transactions found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 679,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"fund_transfers\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"P2P Transfers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transfers.map((transfer)=>{\n                                                var _transfer_receiver, _transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                    children: transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: [\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"Sent to\" : \"Received from\",\n                                                                                \" \",\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? (_transfer_receiver = transfer.receiver) === null || _transfer_receiver === void 0 ? void 0 : _transfer_receiver.full_name : (_transfer_sender = transfer.sender) === null || _transfer_sender === void 0 ? void 0 : _transfer_sender.full_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 759,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transfer.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 763,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transfer.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transfer.reference_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 765,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transfer.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium mr-2 \".concat(transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"text-red-600\" : \"text-green-600\"),\n                                                                        children: [\n                                                                            transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"-\" : \"+\",\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transfer.amount)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 776,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    getStatusIcon(transfer.status)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transfer.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 749,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transfers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transfers found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"deposits\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            depositRequests.map((deposit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 805,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 804,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                                    children: [\n                                                                                        \"Bank Deposit - \",\n                                                                                        deposit.bank_name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 808,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        deposit.account_holder_name,\n                                                                                        \" (\",\n                                                                                        deposit.account_number,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 811,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-lg font-bold text-gray-900\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(deposit.amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 817,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                getStatusIcon(deposit.status),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-1 text-xs font-medium capitalize \".concat(deposit.status === \"approved\" ? \"text-green-600\" : deposit.status === \"rejected\" ? \"text-red-600\" : \"text-yellow-600\"),\n                                                                                    children: deposit.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 822,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 820,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        new Date(deposit.created_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                deposit.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-mono\",\n                                                                    children: [\n                                                                        \"Ref: \",\n                                                                        deposit.reference_number\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.transaction_reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Bank Reference: \",\n                                                                        deposit.transaction_reference\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Notes: \",\n                                                                        deposit.notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.admin_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Admin Notes: \",\n                                                                        deposit.admin_notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 831,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, deposit.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            depositRequests.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No deposit requests found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 797,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"withdrawals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Withdrawal Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            withdrawalRequests.map((withdrawal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: [\n                                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(withdrawal.amount),\n                                                                                    \" to \",\n                                                                                    withdrawal.bank_name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 866,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Account: \",\n                                                                                    withdrawal.account_number,\n                                                                                    \" (\",\n                                                                                    withdrawal.account_holder_name,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 869,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            withdrawal.branch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Branch: \",\n                                                                                    withdrawal.branch\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 873,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            withdrawal.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400 font-mono\",\n                                                                                children: [\n                                                                                    \"Ref: \",\n                                                                                    withdrawal.reference_number\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 876,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            withdrawal.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Notes: \",\n                                                                                    withdrawal.notes\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 881,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(withdrawal.status === \"processed\" ? \"bg-green-100 text-green-800\" : withdrawal.status === \"approved\" ? \"bg-blue-100 text-blue-800\" : withdrawal.status === \"rejected\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                            children: withdrawal.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 885,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 884,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1 ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        new Date(withdrawal.created_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                withdrawal.approved_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Approved: \",\n                                                                        new Date(withdrawal.approved_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                withdrawal.processed_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Processed: \",\n                                                                        new Date(withdrawal.processed_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                withdrawal.admin_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Admin Notes: \",\n                                                                        withdrawal.admin_notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, withdrawal.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            withdrawalRequests.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No withdrawal requests found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 858,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"commissions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Commission Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.filter((t)=>t.transaction_type === \"commission\").map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: \"Commission Earned\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 928,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 931,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transaction.reference_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                transaction.reference_id.slice(-8)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 935,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transaction.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 939,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 927,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium mr-2 text-green-600\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.amount)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 946,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(transaction.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 945,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Balance: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.balance_after)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 951,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            transactions.filter((t)=>t.transaction_type === \"commission\").length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"No commission transactions found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Commissions will appear here when you earn them from referrals or other activities.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 920,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 676,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this),\n            showTransferModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Send Money\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleTransfer,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Receiver Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            required: true,\n                                            value: transferForm.receiverEmail,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        receiverEmail: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Enter receiver's email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 976,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            max: (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0,\n                                            step: \"0.01\",\n                                            value: transferForm.amount,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 993,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Available: \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 989,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Description (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: transferForm.description,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"What's this for?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowTransferModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: transferLoading,\n                                            className: \"flex-1 px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 disabled:opacity-50\",\n                                            children: transferLoading ? \"Sending...\" : \"Send Money\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1028,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 973,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 972,\n                columnNumber: 9\n            }, this),\n            showDepositModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Request Cash Deposit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1045,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleDeposit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1048,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            step: \"0.01\",\n                                            value: depositForm.amount,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1051,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1047,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Depositor Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1063,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: depositForm.depositor_name,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        depositor_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Name of person who made the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1066,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1062,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Deposit Receipt/Proof *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1076,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            required: true,\n                                            accept: \"image/*,.pdf\",\n                                            onChange: (e)=>{\n                                                var _e_target_files;\n                                                const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                if (file) {\n                                                    // Validate file size (5MB max)\n                                                    if (file.size > 5 * 1024 * 1024) {\n                                                        showAlert({\n                                                            title: \"File Too Large\",\n                                                            message: \"Please select a file smaller than 5MB.\",\n                                                            variant: \"warning\"\n                                                        });\n                                                        e.target.value = \"\";\n                                                        return;\n                                                    }\n                                                    setDepositProof(file);\n                                                }\n                                            },\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Upload bank deposit slip, receipt, or proof of payment (Max 5MB)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1101,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1075,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: depositForm.notes,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            rows: 3,\n                                            placeholder: \"Additional information about the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1109,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"terms\",\n                                            required: true,\n                                            checked: depositForm.terms_accepted,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        terms_accepted: e.target.checked\n                                                    })),\n                                            className: \"mt-1 h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"terms\",\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    target: \"_blank\",\n                                                    className: \"text-primary-blue hover:underline\",\n                                                    children: \"Terms and Conditions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"and confirm that the deposit information provided is accurate. *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1126,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowDepositModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: depositLoading,\n                                            className: \"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                            children: depositLoading ? \"Submitting...\" : \"Submit Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1142,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1046,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 1044,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 1043,\n                columnNumber: 9\n            }, this),\n            showWithdrawalModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Request Withdrawal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1159,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleWithdrawal,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            min: \"1\",\n                                            max: (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0,\n                                            required: true,\n                                            value: withdrawalForm.amount,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter amount\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Available balance: \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1176,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1161,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Bank Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.bank_name,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        bank_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter bank name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1185,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Account Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.account_number,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        account_number: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter account number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Account Holder Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1210,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.account_holder_name,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        account_holder_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter account holder name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1209,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Branch (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: withdrawalForm.branch,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        branch: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter branch name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: withdrawalForm.notes,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            rows: 3,\n                                            placeholder: \"Any additional notes...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1240,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowWithdrawalModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: withdrawalLoading,\n                                            className: \"flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50\",\n                                            children: withdrawalLoading ? \"Submitting...\" : \"Submit Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1257,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 1158,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 1157,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n        lineNumber: 502,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"AtW9q3+eKyNQGhLzYmuBoYBSO6M=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__.useAlert,\n        _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__.useKYCCheck\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/wallet/page.tsx\n"));

/***/ })

});