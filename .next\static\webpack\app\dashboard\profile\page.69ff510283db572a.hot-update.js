"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/lib/services/kycStorage.ts":
/*!****************************************!*\
  !*** ./src/lib/services/kycStorage.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCStorageService: function() { return /* binding */ KYCStorageService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass KYCStorageService {\n    /**\n   * Ensure KYC bucket exists before operations\n   */ static async ensureBucketExists() {\n        try {\n            // Check if bucket exists\n            const { data: buckets, error: listError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.listBuckets();\n            if (listError) {\n                console.warn(\"Cannot list buckets (likely due to RLS policies):\", listError.message);\n                console.log(\"Assuming KYC bucket exists and continuing...\");\n                return;\n            }\n            const bucketExists = buckets === null || buckets === void 0 ? void 0 : buckets.some((bucket)=>bucket.name === this.BUCKET_NAME);\n            if (!bucketExists) {\n                console.log(\"KYC bucket '\".concat(this.BUCKET_NAME, \"' not found in bucket list\"));\n                console.log(\"This is expected if RLS policies restrict bucket listing\");\n                console.log(\"Assuming bucket exists and continuing with upload...\");\n                return;\n            }\n            console.log(\"KYC bucket '\".concat(this.BUCKET_NAME, \"' confirmed to exist\"));\n        } catch (error) {\n            console.warn(\"Error checking KYC bucket existence:\", error);\n            console.log(\"Assuming bucket exists and continuing with operations...\");\n        }\n    }\n    /**\n   * Validate KYC document file\n   */ static validateFile(file, documentType) {\n        // Check file size\n        if (file.size > this.MAX_FILE_SIZE) {\n            throw new Error(\"File size must be less than \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB\"));\n        }\n        // Check file type\n        if (!this.ALLOWED_TYPES.includes(file.type)) {\n            throw new Error(\"File type \".concat(file.type, \" is not supported. Allowed types: \").concat(this.ALLOWED_TYPES.join(\", \")));\n        }\n        // Additional validation for specific document types\n        if (documentType === \"selfie\" && file.type === \"application/pdf\") {\n            throw new Error(\"Selfie photos must be image files, not PDF\");\n        }\n        // Check if file name is reasonable\n        if (file.name.length > 255) {\n            throw new Error(\"File name is too long\");\n        }\n    }\n    /**\n   * Generate secure filename for KYC document\n   */ static generateSecureFileName(userId, documentType, originalName) {\n        var _originalName_split_pop;\n        const timestamp = Date.now();\n        const randomId = Math.random().toString(36).substring(2);\n        const fileExt = ((_originalName_split_pop = originalName.split(\".\").pop()) === null || _originalName_split_pop === void 0 ? void 0 : _originalName_split_pop.toLowerCase()) || \"jpg\";\n        // Create folder structure: userId/documentType/timestamp-randomId.ext\n        return \"\".concat(userId, \"/\").concat(documentType, \"/\").concat(timestamp, \"-\").concat(randomId, \".\").concat(fileExt);\n    }\n    /**\n   * Upload a single KYC document\n   */ static async uploadKYCDocument(file, userId, documentType) {\n        try {\n            // Ensure bucket exists (or assume it exists)\n            await this.ensureBucketExists();\n            // Validate file\n            this.validateFile(file, documentType);\n            // Generate secure filename\n            const fileName = this.generateSecureFileName(userId, documentType, file.name);\n            console.log(\"Uploading KYC document: \".concat(fileName, \" to bucket: \").concat(this.BUCKET_NAME));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).upload(fileName, file, {\n                cacheControl: \"3600\",\n                upsert: true // Allow overwriting for resubmissions\n            });\n            if (error) {\n                console.error(\"KYC document upload error:\", error);\n                // Provide more specific error messages\n                if (error.message.includes(\"row-level security\")) {\n                    throw new Error(\"Upload failed: Storage access denied. Please ensure you are logged in and try again.\");\n                } else if (error.message.includes(\"Bucket not found\")) {\n                    throw new Error(\"Upload failed: Storage bucket not configured. Please contact support.\");\n                } else if (error.message.includes(\"File size\")) {\n                    throw new Error(\"Upload failed: File too large. Maximum size is \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB.\"));\n                } else {\n                    throw new Error(\"Upload failed: \".concat(error.message));\n                }\n            }\n            console.log(\"KYC document upload successful:\", data);\n            // For private buckets, we need to create signed URLs for access\n            // Return the file path for now, signed URL will be generated when needed\n            return fileName;\n        } catch (error) {\n            console.error(\"Error uploading KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upload multiple KYC documents\n   */ static async uploadKYCDocuments(documents, userId) {\n        const results = {};\n        try {\n            // Upload all documents concurrently\n            const uploadPromises = documents.map(async (doc)=>{\n                const filePath = await this.uploadKYCDocument(doc.file, userId, doc.type);\n                return {\n                    type: doc.type,\n                    filePath\n                };\n            });\n            const uploadResults = await Promise.all(uploadPromises);\n            // Build results object\n            uploadResults.forEach((result)=>{\n                results[result.type] = result.filePath;\n            });\n            return results;\n        } catch (error) {\n            console.error(\"Error uploading multiple KYC documents:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get signed URL for KYC document (for admin review)\n   */ static async getKYCDocumentSignedUrl(filePath) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).createSignedUrl(filePath, expiresIn);\n            if (error) {\n                console.error(\"Error creating signed URL:\", error);\n                throw new Error(\"Failed to create signed URL: \".concat(error.message));\n            }\n            return data.signedUrl;\n        } catch (error) {\n            console.error(\"Error getting KYC document signed URL:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC document (for cleanup or resubmission)\n   */ static async deleteKYCDocument(filePath) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).remove([\n                filePath\n            ]);\n            if (error) {\n                console.error(\"Error deleting KYC document:\", error);\n                throw new Error(\"Failed to delete document: \".concat(error.message));\n            }\n            console.log(\"KYC document deleted successfully:\", filePath);\n        } catch (error) {\n            console.error(\"Error deleting KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get multiple signed URLs for KYC documents\n   */ static async getKYCDocumentSignedUrls(filePaths) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const urlPromises = filePaths.map(async (filePath)=>{\n                const signedUrl = await this.getKYCDocumentSignedUrl(filePath, expiresIn);\n                return {\n                    filePath,\n                    signedUrl\n                };\n            });\n            const results = await Promise.all(urlPromises);\n            const urlMap = {};\n            results.forEach((result)=>{\n                urlMap[result.filePath] = result.signedUrl;\n            });\n            return urlMap;\n        } catch (error) {\n            console.error(\"Error getting multiple KYC document signed URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Validate document type enum\n   */ static isValidDocumentType(type) {\n        return [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ].includes(type);\n    }\n    /**\n   * Get human-readable document type name\n   */ static getDocumentTypeName(type) {\n        const names = {\n            id_front: \"ID Document (Front)\",\n            id_back: \"ID Document (Back)\",\n            selfie: \"Selfie Photo\",\n            address_proof: \"Address Proof\"\n        };\n        return names[type];\n    }\n    /**\n   * Get document type requirements\n   */ static getDocumentTypeRequirements(type) {\n        const requirements = {\n            id_front: \"Clear photo of the front side of your government-issued ID (National ID, Passport, or Driving License)\",\n            id_back: \"Clear photo of the back side of your government-issued ID\",\n            selfie: \"Clear selfie photo holding your ID document next to your face\",\n            address_proof: \"Recent utility bill, bank statement, or official document showing your address (PDF or image)\"\n        };\n        return requirements[type];\n    }\n}\nKYCStorageService.BUCKET_NAME = \"kyc-documents\";\nKYCStorageService.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for KYC documents\n;\nKYCStorageService.ALLOWED_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/webp\",\n    \"application/pdf\" // Allow PDF for address proof documents\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kycStorage.ts\n"));

/***/ })

});