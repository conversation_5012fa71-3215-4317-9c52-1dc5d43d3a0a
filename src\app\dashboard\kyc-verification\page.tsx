'use client'

import { Suspense } from 'react'
import NewKYCVerificationPage from '@/components/kyc/NewKYCVerificationPage'
import { Loader2 } from 'lucide-react'

function LoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary-blue mx-auto mb-4" />
        <p className="text-gray-600">Loading KYC verification...</p>
      </div>
    </div>
  )
}

export default function KYCVerificationPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <NewKYCVerificationPage />
    </Suspense>
  )
}
