-- Complete KYC System Setup - RLS Policies and Helper Functions
-- Execute this in Supabase SQL Editor after creating the tables

-- =====================================================
-- 1. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE kyc_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_document_types ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 2. CREATE RLS POLICIES
-- =====================================================

-- KYC submissions policies
DROP POLICY IF EXISTS "Users can view own KYC submissions" ON kyc_submissions;
CREATE POLICY "Users can view own KYC submissions" ON kyc_submissions
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own KYC submissions" ON kyc_submissions;
CREATE POLICY "Users can insert own KYC submissions" ON kyc_submissions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all KYC submissions" ON kyc_submissions;
CREATE POLICY "Admins can view all KYC submissions" ON kyc_submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND (
                is_super_admin = true OR
                email LIKE '%@okdoi.com' OR 
                email IN ('<EMAIL>', '<EMAIL>')
            )
        )
    );

DROP POLICY IF EXISTS "Admins can update KYC submissions" ON kyc_submissions;
CREATE POLICY "Admins can update KYC submissions" ON kyc_submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND (
                is_super_admin = true OR
                email LIKE '%@okdoi.com' OR 
                email IN ('<EMAIL>', '<EMAIL>')
            )
        )
    );

-- KYC status history policies
DROP POLICY IF EXISTS "Users can view their own KYC status history" ON kyc_status_history;
CREATE POLICY "Users can view their own KYC status history" ON kyc_status_history
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "System can insert KYC status history" ON kyc_status_history;
CREATE POLICY "System can insert KYC status history" ON kyc_status_history
    FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "Admins can view all KYC status history" ON kyc_status_history;
CREATE POLICY "Admins can view all KYC status history" ON kyc_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND (
                is_super_admin = true OR
                email LIKE '%@okdoi.com' OR 
                email IN ('<EMAIL>', '<EMAIL>')
            )
        )
    );

-- KYC document types policies
DROP POLICY IF EXISTS "Anyone can view KYC document types" ON kyc_document_types;
CREATE POLICY "Anyone can view KYC document types" ON kyc_document_types
    FOR SELECT USING (is_active = true);

-- =====================================================
-- 3. CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to check if user is KYC verified
CREATE OR REPLACE FUNCTION is_kyc_verified(user_uuid uuid)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users
        WHERE id = user_uuid AND kyc_status = 'approved'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user KYC status when submission status changes
CREATE OR REPLACE FUNCTION update_user_kyc_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Update user's KYC status based on submission status
    UPDATE users 
    SET 
        kyc_status = NEW.status,
        kyc_submitted_at = CASE 
            WHEN NEW.status = 'pending' AND OLD.status IS NULL THEN NEW.created_at
            ELSE kyc_submitted_at
        END,
        kyc_approved_at = CASE 
            WHEN NEW.status = 'approved' THEN NEW.reviewed_at
            ELSE NULL
        END
    WHERE id = NEW.user_id;

    -- Insert status history record
    INSERT INTO kyc_status_history (
        kyc_submission_id,
        user_id,
        previous_status,
        new_status,
        changed_by,
        change_reason,
        admin_notes
    ) VALUES (
        NEW.id,
        NEW.user_id,
        OLD.status,
        NEW.status,
        NEW.reviewed_by,
        NEW.rejection_reason,
        NEW.admin_notes
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for KYC status updates
DROP TRIGGER IF EXISTS trigger_update_user_kyc_status ON kyc_submissions;
CREATE TRIGGER trigger_update_user_kyc_status
    AFTER INSERT OR UPDATE ON kyc_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_user_kyc_status();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_kyc_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS trigger_kyc_submissions_updated_at ON kyc_submissions;
CREATE TRIGGER trigger_kyc_submissions_updated_at
    BEFORE UPDATE ON kyc_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_kyc_updated_at();

-- =====================================================
-- 4. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE kyc_submissions IS 'Stores KYC verification submissions with document references';
COMMENT ON TABLE kyc_status_history IS 'Audit trail for KYC status changes';
COMMENT ON TABLE kyc_document_types IS 'Reference table for supported KYC document types';
COMMENT ON COLUMN users.kyc_status IS 'Current KYC verification status of the user';
COMMENT ON FUNCTION is_kyc_verified(uuid) IS 'Helper function to check if user has completed KYC verification';

-- =====================================================
-- 5. CREATE STORAGE BUCKET FOR KYC DOCUMENTS
-- =====================================================

-- Note: This needs to be done via the Supabase Dashboard or Storage API
-- The bucket should be named 'kyc-documents' with appropriate policies
