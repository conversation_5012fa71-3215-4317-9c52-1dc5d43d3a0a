const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
})

async function checkKYCSchema() {
  console.log('🔍 Checking KYC database schema...\n')
  
  // Check if kyc_status column exists in users table
  console.log('1. Checking users.kyc_status column...')
  try {
    const { data, error } = await supabase
      .from('users')
      .select('kyc_status, kyc_submitted_at, kyc_approved_at')
      .limit(1)
    
    if (error) {
      console.log('❌ users KYC columns:', error.message)
    } else {
      console.log('✅ users KYC columns exist')
    }
  } catch (e) {
    console.log('❌ users KYC columns error:', e.message)
  }
  
  // Check kyc_submissions table
  console.log('\n2. Checking kyc_submissions table...')
  try {
    const { data, error } = await supabase
      .from('kyc_submissions')
      .select('*')
      .limit(1)
    
    if (error) {
      console.log('❌ kyc_submissions table:', error.message)
    } else {
      console.log('✅ kyc_submissions table exists')
    }
  } catch (e) {
    console.log('❌ kyc_submissions table error:', e.message)
  }
  
  // Check kyc_document_types table
  console.log('\n3. Checking kyc_document_types table...')
  try {
    const { data, error } = await supabase
      .from('kyc_document_types')
      .select('*')
      .limit(1)
    
    if (error) {
      console.log('❌ kyc_document_types table:', error.message)
    } else {
      console.log('✅ kyc_document_types table exists')
      console.log('   Document types:', data)
    }
  } catch (e) {
    console.log('❌ kyc_document_types table error:', e.message)
  }
  
  // Check kyc_status_history table
  console.log('\n4. Checking kyc_status_history table...')
  try {
    const { data, error } = await supabase
      .from('kyc_status_history')
      .select('*')
      .limit(1)
    
    if (error) {
      console.log('❌ kyc_status_history table:', error.message)
    } else {
      console.log('✅ kyc_status_history table exists')
    }
  } catch (e) {
    console.log('❌ kyc_status_history table error:', e.message)
  }
  
  console.log('\n🔍 Schema check complete!')
}

checkKYCSchema().catch(console.error)
