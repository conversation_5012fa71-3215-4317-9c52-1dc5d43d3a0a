-- Update KYC Schema to Support Flexible Document Requirements
-- This allows different document types to have different requirements

-- First, make document URLs nullable to support different document types
ALTER TABLE kyc_submissions 
ALTER COLUMN id_document_back_url DROP NOT NULL;

-- Add comments to clarify document requirements
COMMENT ON COLUMN kyc_submissions.id_document_front_url IS 'Required for all document types';
COMMENT ON COLUMN kyc_submissions.id_document_back_url IS 'Required for national_id and driving_license, optional for passport';
COMMENT ON COLUMN kyc_submissions.selfie_photo_url IS 'Required for all document types';
COMMENT ON COLUMN kyc_submissions.address_proof_url IS 'Required for all document types';

-- Update document types with requirements information
UPDATE kyc_document_types SET 
    description = 'Sri Lankan National Identity Card - Requires front and back photos'
WHERE id = 'national_id';

UPDATE kyc_document_types SET 
    description = 'Valid passport document - Requires front photo only'
WHERE id = 'passport';

UPDATE kyc_document_types SET 
    description = 'Valid driving license - Requires front and back photos'
WHERE id = 'driving_license';

-- Add a function to validate document requirements based on type
CREATE OR REPLACE FUNCTION validate_kyc_documents(
    doc_type varchar(50),
    front_url text,
    back_url text,
    selfie_url text,
    address_url text
) RETURNS boolean AS $$
BEGIN
    -- All documents need front, selfie, and address proof
    IF front_url IS NULL OR selfie_url IS NULL OR address_url IS NULL THEN
        RETURN false;
    END IF;
    
    -- National ID and Driving License need back photo
    IF doc_type IN ('national_id', 'driving_license') AND back_url IS NULL THEN
        RETURN false;
    END IF;
    
    -- Passport doesn't need back photo
    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- Add a check constraint using the validation function
ALTER TABLE kyc_submissions 
ADD CONSTRAINT check_document_requirements 
CHECK (validate_kyc_documents(id_document_type, id_document_front_url, id_document_back_url, selfie_photo_url, address_proof_url));

-- Create a view for document requirements
CREATE OR REPLACE VIEW kyc_document_requirements AS
SELECT 
    id,
    name,
    description,
    CASE 
        WHEN id = 'passport' THEN false
        ELSE true
    END as requires_back_photo,
    true as requires_front_photo,
    true as requires_selfie,
    true as requires_address_proof,
    is_active
FROM kyc_document_types
WHERE is_active = true;
