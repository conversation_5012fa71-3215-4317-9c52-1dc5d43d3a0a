-- =====================================================
-- MANUAL DATABASE FIXES FOR KYC SYSTEM
-- Run these commands in Supabase SQL Editor
-- =====================================================

-- STEP 1: Fix Foreign Key References
-- Copy and paste this section first:

-- Drop existing foreign key constraints that reference auth.users
ALTER TABLE kyc_submissions DROP CONSTRAINT IF EXISTS kyc_submissions_user_id_fkey;
ALTER TABLE kyc_submissions DROP CONSTRAINT IF EXISTS kyc_submissions_reviewed_by_fkey;
ALTER TABLE kyc_status_history DROP CONSTRAINT IF EXISTS kyc_status_history_user_id_fkey;
ALTER TABLE kyc_status_history DROP CONSTRAINT IF EXISTS kyc_status_history_changed_by_fkey;

-- Add correct foreign key constraints referencing public.users
ALTER TABLE kyc_submissions 
ADD CONSTRAINT kyc_submissions_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE kyc_submissions 
ADD CONSTRAINT kyc_submissions_reviewed_by_fkey 
FOREIGN KEY (reviewed_by) REFERENCES public.users(id) ON DELETE SET NULL;

ALTER TABLE kyc_status_history 
ADD CONSTRAINT kyc_status_history_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE kyc_status_history 
ADD CONSTRAINT kyc_status_history_changed_by_fkey 
FOREIGN KEY (changed_by) REFERENCES public.users(id) ON DELETE SET NULL;

-- =====================================================
-- STEP 2: Enable RLS and Create Policies
-- Copy and paste this section after Step 1 completes:

-- Enable RLS on all KYC tables
ALTER TABLE kyc_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_document_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_status_history ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can insert own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can update own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Admins can view all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Admins can update all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Everyone can read KYC document types" ON kyc_document_types;
DROP POLICY IF EXISTS "Admins can modify KYC document types" ON kyc_document_types;
DROP POLICY IF EXISTS "Users can view own KYC status history" ON kyc_status_history;
DROP POLICY IF EXISTS "Admins can view all KYC status history" ON kyc_status_history;
DROP POLICY IF EXISTS "System can insert KYC status history" ON kyc_status_history;

-- KYC Submissions Policies
CREATE POLICY "Users can view own KYC submissions" ON kyc_submissions
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own KYC submissions" ON kyc_submissions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own KYC submissions" ON kyc_submissions
    FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Admins can view all KYC submissions" ON kyc_submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

CREATE POLICY "Admins can update all KYC submissions" ON kyc_submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- KYC Document Types Policies
CREATE POLICY "Everyone can read KYC document types" ON kyc_document_types
    FOR SELECT USING (true);

CREATE POLICY "Admins can modify KYC document types" ON kyc_document_types
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- KYC Status History Policies
CREATE POLICY "Users can view own KYC status history" ON kyc_status_history
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Admins can view all KYC status history" ON kyc_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

CREATE POLICY "System can insert KYC status history" ON kyc_status_history
    FOR INSERT WITH CHECK (true);

-- =====================================================
-- STEP 3: Grant Permissions
-- Copy and paste this section after Step 2 completes:

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON kyc_submissions TO authenticated;
GRANT SELECT ON kyc_document_types TO authenticated;
GRANT SELECT, INSERT ON kyc_status_history TO authenticated;

-- Grant permissions to service role (for admin operations)
GRANT ALL ON kyc_submissions TO service_role;
GRANT ALL ON kyc_document_types TO service_role;
GRANT ALL ON kyc_status_history TO service_role;

-- =====================================================
-- STEP 4: Create Storage Bucket (if not exists)
-- Go to Supabase Dashboard > Storage > Buckets
-- If 'kyc-documents' bucket doesn't exist, create it with:
-- - Name: kyc-documents
-- - Private: true (not public)
-- - Allowed MIME types: image/jpeg, image/png, image/webp, application/pdf
-- - File size limit: 10MB

-- =====================================================
-- STEP 5: Verification Query
-- Run this to verify everything is working:

SELECT 'KYC Tables Check' as check_type, 
       COUNT(*) as kyc_submissions_count 
FROM kyc_submissions
UNION ALL
SELECT 'Document Types Check' as check_type, 
       COUNT(*) as document_types_count 
FROM kyc_document_types
UNION ALL
SELECT 'Status History Check' as check_type, 
       COUNT(*) as status_history_count 
FROM kyc_status_history;

-- Check foreign key constraints
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('kyc_submissions', 'kyc_status_history')
ORDER BY tc.table_name, tc.constraint_name;

SELECT 'Database fixes completed successfully!' as status;
