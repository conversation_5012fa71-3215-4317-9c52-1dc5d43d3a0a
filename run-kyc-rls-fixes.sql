-- STEP 2: Enable RLS and Create Policies (Run this after step 1)

-- Enable RLS on all KYC tables
ALTER TABLE kyc_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_document_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_status_history ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can insert own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can update own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "<PERSON><PERSON> can update all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Everyone can read KYC document types" ON kyc_document_types;
DROP POLICY IF EXISTS "Ad<PERSON> can modify KYC document types" ON kyc_document_types;
DROP POLICY IF EXISTS "Users can view own KYC status history" ON kyc_status_history;
DROP POLICY IF EXISTS "Admins can view all KYC status history" ON kyc_status_history;
DROP POLICY IF EXISTS "System can insert KYC status history" ON kyc_status_history;

-- KYC Submissions Policies
CREATE POLICY "Users can view own KYC submissions" ON kyc_submissions
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own KYC submissions" ON kyc_submissions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own KYC submissions" ON kyc_submissions
    FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Admins can view all KYC submissions" ON kyc_submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

CREATE POLICY "Admins can update all KYC submissions" ON kyc_submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- KYC Document Types Policies
CREATE POLICY "Everyone can read KYC document types" ON kyc_document_types
    FOR SELECT USING (true);

CREATE POLICY "Admins can modify KYC document types" ON kyc_document_types
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

-- KYC Status History Policies
CREATE POLICY "Users can view own KYC status history" ON kyc_status_history
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Admins can view all KYC status history" ON kyc_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id::text = auth.uid()::text 
            AND (role = 'admin' OR is_super_admin = true)
        )
    );

CREATE POLICY "System can insert KYC status history" ON kyc_status_history
    FOR INSERT WITH CHECK (true);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON kyc_submissions TO authenticated;
GRANT SELECT ON kyc_document_types TO authenticated;
GRANT SELECT, INSERT ON kyc_status_history TO authenticated;
GRANT ALL ON kyc_submissions TO service_role;
GRANT ALL ON kyc_document_types TO service_role;
GRANT ALL ON kyc_status_history TO service_role;
