"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/create-profile/route";
exports.ids = ["app/api/auth/create-profile/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fcreate-profile%2Froute&page=%2Fapi%2Fauth%2Fcreate-profile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcreate-profile%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fcreate-profile%2Froute&page=%2Fapi%2Fauth%2Fcreate-profile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcreate-profile%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_auth_create_profile_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/create-profile/route.ts */ \"(rsc)/./src/app/api/auth/create-profile/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/create-profile/route\",\n        pathname: \"/api/auth/create-profile\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/create-profile/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\auth\\\\create-profile\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_auth_create_profile_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/create-profile/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fcreate-profile%2Froute&page=%2Fapi%2Fauth%2Fcreate-profile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcreate-profile%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/create-profile/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/auth/create-profile/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-admin */ \"(rsc)/./src/lib/supabase-admin.ts\");\n\n\nasync function POST(request) {\n    let supabaseAdmin = null;\n    try {\n        const body = await request.json();\n        const { userId, email, userData } = body;\n        // Validate required fields\n        if (!userId || !email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields: userId and email\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid email format\"\n            }, {\n                status: 400\n            });\n        }\n        // Get admin client with retry mechanism\n        let retryCount = 0;\n        const maxRetries = 3;\n        while(retryCount < maxRetries){\n            try {\n                supabaseAdmin = (0,_lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.getSupabaseAdmin)();\n                break;\n            } catch (adminError) {\n                retryCount++;\n                console.error(`Failed to get admin client (attempt ${retryCount}):`, adminError);\n                if (retryCount >= maxRetries) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Admin client initialization failed after multiple attempts\"\n                    }, {\n                        status: 500\n                    });\n                }\n                // Wait before retry\n                await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n            }\n        }\n        if (!supabaseAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Admin client not available\"\n            }, {\n                status: 500\n            });\n        }\n        // Check if user profile already exists\n        const { data: existingUser, error: checkError } = await supabaseAdmin.from(\"users\").select(\"id\").eq(\"id\", userId).single();\n        if (checkError && checkError.code !== \"PGRST116\") {\n            console.error(\"Error checking existing user:\", checkError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to check existing user profile\"\n            }, {\n                status: 500\n            });\n        }\n        if (existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: \"User profile already exists\"\n            }, {\n                status: 200\n            });\n        }\n        // Create user profile using admin client to bypass RLS\n        const { error: profileError } = await supabaseAdmin.from(\"users\").insert({\n            id: userId,\n            email: email,\n            full_name: userData?.full_name || null,\n            phone: userData?.phone || null,\n            location: userData?.location || null,\n            user_type: \"user\",\n            role: \"user\",\n            is_super_admin: false,\n            referral_level: userData?.referral_level || 0,\n            referred_by_id: userData?.referred_by_id || null\n        });\n        if (profileError) {\n            console.error(\"Profile creation error:\", profileError);\n            // Handle specific error cases\n            if (profileError.code === \"23505\") {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: \"User profile already exists\"\n                }, {\n                    status: 200\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to create user profile: ${profileError.message}`\n            }, {\n                status: 500\n            });\n        }\n        // Create user wallet (check if exists first to prevent duplicates)\n        let walletCreated = false;\n        try {\n            // First check if wallet already exists\n            const { data: existingWallet, error: checkError } = await supabaseAdmin.from(\"user_wallets\").select(\"id\").eq(\"user_id\", userId).single();\n            if (checkError && checkError.code !== \"PGRST116\") {\n                console.error(\"Error checking existing wallet:\", checkError);\n            } else if (existingWallet) {\n                // Wallet already exists\n                walletCreated = true;\n                console.log(\"✅ User wallet already exists:\", existingWallet.id);\n            } else {\n                // Create new wallet\n                const { data: newWallet, error: walletError } = await supabaseAdmin.from(\"user_wallets\").insert({\n                    user_id: userId,\n                    balance: 0.00,\n                    currency: \"LKR\"\n                }).select(\"id\").single();\n                if (walletError) {\n                    // Handle duplicate key error gracefully\n                    if (walletError.code === \"23505\") {\n                        console.log(\"✅ Wallet already exists (race condition handled)\");\n                        walletCreated = true;\n                    } else {\n                        console.error(\"Wallet creation error:\", walletError);\n                        console.warn(\"User profile created but wallet creation failed:\", walletError.message);\n                    }\n                } else {\n                    walletCreated = true;\n                    console.log(\"✅ User wallet created with ID:\", newWallet.id);\n                }\n            }\n        } catch (walletError) {\n            console.error(\"Wallet creation exception:\", walletError);\n            console.warn(\"User profile created but wallet creation failed with exception\");\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"User profile created successfully\",\n            walletCreated: walletCreated\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(\"API error in create-profile:\", error);\n        // Provide more specific error messages\n        let errorMessage = \"Internal server error\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"SUPABASE_SERVICE_ROLE_KEY\")) {\n                errorMessage = \"Server configuration error - admin access not available\";\n            } else if (error.message.includes(\"network\") || error.message.includes(\"fetch\")) {\n                errorMessage = \"Network error - please try again\";\n            } else {\n                errorMessage = error.message;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/create-profile/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase-admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase-admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSupabaseAdmin: () => (/* binding */ getSupabaseAdmin),\n/* harmony export */   resetSupabaseAdmin: () => (/* binding */ resetSupabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Server-side admin client - only available in server environments\nlet adminClient = null;\n/**\n * Get or create the admin Supabase client\n * This should only be used in server-side code (API routes, server components)\n */ function getSupabaseAdmin() {\n    // Return existing client if already created\n    if (adminClient) {\n        return adminClient;\n    }\n    // Check if we're in a server environment\n    if (false) {}\n    const supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\n    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl) {\n        throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    }\n    if (!supabaseServiceRoleKey) {\n        throw new Error(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable\");\n    }\n    // Create and cache the admin client\n    adminClient = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n    return adminClient;\n}\n/**\n * Reset the admin client (useful for testing)\n */ function resetSupabaseAdmin() {\n    adminClient = null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase-admin.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fcreate-profile%2Froute&page=%2Fapi%2Fauth%2Fcreate-profile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcreate-profile%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();