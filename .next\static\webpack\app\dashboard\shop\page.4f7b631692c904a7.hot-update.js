"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/shop/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/shop/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/shop/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MyShopPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useKYCVerification */ \"(app-pages-browser)/./src/hooks/useKYCVerification.tsx\");\n/* harmony import */ var _contexts_AlertContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AlertContext */ \"(app-pages-browser)/./src/contexts/AlertContext.tsx\");\n/* harmony import */ var _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/vendorShops */ \"(app-pages-browser)/./src/lib/services/vendorShops.ts\");\n/* harmony import */ var _lib_services_shopProducts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/services/shopProducts */ \"(app-pages-browser)/./src/lib/services/shopProducts.ts\");\n/* harmony import */ var _lib_services_orders__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/orders */ \"(app-pages-browser)/./src/lib/services/orders.ts\");\n/* harmony import */ var _lib_services_storage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/services/storage */ \"(app-pages-browser)/./src/lib/services/storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx\");\n/* harmony import */ var _components_orders_TrackingModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/orders/TrackingModal */ \"(app-pages-browser)/./src/components/orders/TrackingModal.tsx\");\n/* harmony import */ var _components_dashboard_MerchantWallet__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dashboard/MerchantWallet */ \"(app-pages-browser)/./src/components/dashboard/MerchantWallet.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction MyShopPage() {\n    var _selectedShop_rating, _selectedOrder_buyer, _selectedOrder_order_items;\n    _s();\n    const { user, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { isKYCVerified } = (0,_hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_5__.useKYCCheck)();\n    const { showAlert } = (0,_contexts_AlertContext__WEBPACK_IMPORTED_MODULE_6__.useAlert)();\n    const [shops, setShops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedShop, setSelectedShop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [shopProducts, setShopProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [shopOrders, setShopOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [shopStats, setShopStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalProducts: 0,\n        totalViews: 0,\n        totalFollowers: 0,\n        totalSales: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrderDetails, setShowOrderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTrackingModal, setShowTrackingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderToShip, setOrderToShip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [trackingLoading, setTrackingLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingBanner, setUploadingBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadingLogo, setUploadingLogo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchUserShops = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id)) {\n            setError(\"User not authenticated\");\n            setLoading(false);\n            return;\n        }\n        try {\n            setLoading(true);\n            const userShops = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_7__.VendorShopService.getUserShops(user.id);\n            setShops(userShops);\n            if (userShops.length > 0) {\n                setSelectedShop(userShops[0]);\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load shops\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchShopProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id)) {\n            setShopProducts([]);\n            return;\n        }\n        try {\n            const { products } = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_7__.VendorShopService.getShopProducts(selectedShop.id, 1, 20);\n            setShopProducts(products);\n        } catch (err) {\n            console.error(\"Error fetching shop products:\", err);\n            // Don't throw error, just log it and continue with empty products\n            setShopProducts([]);\n        }\n    }, [\n        selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id\n    ]);\n    const fetchShopOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id) || !(user === null || user === void 0 ? void 0 : user.id)) {\n            setShopOrders([]);\n            return;\n        }\n        try {\n            const { orders } = await _lib_services_orders__WEBPACK_IMPORTED_MODULE_9__.OrderService.getSellerOrders(user.id, selectedShop.id, 1, 20);\n            setShopOrders(orders);\n        } catch (err) {\n            console.error(\"Error fetching shop orders:\", err);\n            // Don't throw error, just log it and continue with empty orders\n            setShopOrders([]);\n        }\n    }, [\n        selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    const fetchShopStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id)) {\n            setShopStats({\n                totalProducts: 0,\n                totalViews: 0,\n                totalFollowers: 0,\n                totalSales: 0\n            });\n            return;\n        }\n        try {\n            // Refresh shop statistics from database\n            const refreshedShop = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_7__.VendorShopService.refreshShopStatistics(selectedShop.id);\n            // Get followers count with error handling\n            let followersCount = 0;\n            try {\n                followersCount = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_7__.VendorShopService.getShopFollowersCount(selectedShop.id);\n            } catch (followersError) {\n                console.warn(\"Failed to get followers count, using 0:\", followersError);\n            }\n            // Update only the statistics state\n            setShopStats({\n                totalProducts: refreshedShop.total_products || 0,\n                totalViews: refreshedShop.total_views || 0,\n                totalFollowers: followersCount,\n                totalSales: refreshedShop.total_sales || 0\n            });\n        } catch (err) {\n            console.error(\"Error fetching shop stats:\", err);\n            // Fallback to existing data if refresh fails\n            let followersCount = 0;\n            try {\n                followersCount = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_7__.VendorShopService.getShopFollowersCount(selectedShop.id);\n            } catch (followersError) {\n                console.warn(\"Failed to get followers count in fallback, using 0:\", followersError);\n            }\n            setShopStats({\n                totalProducts: selectedShop.total_products || 0,\n                totalViews: selectedShop.total_views || 0,\n                totalFollowers: followersCount,\n                totalSales: selectedShop.total_sales || 0\n            });\n        }\n    }, [\n        selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id\n    ]) // Only depend on shop ID, not the entire shop object\n    ;\n    // useEffect hooks - placed after all function declarations to avoid hoisting issues\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchUserShops();\n        }\n    }, [\n        user\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedShop) {\n            fetchShopProducts();\n            fetchShopOrders();\n            fetchShopStats();\n        }\n    }, [\n        selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id,\n        fetchShopProducts,\n        fetchShopOrders,\n        fetchShopStats\n    ]) // Only trigger when shop ID changes\n    ;\n    const handleViewProduct = (productId)=>{\n        window.open(\"/product/\".concat(productId), \"_blank\");\n    };\n    const handleEditProduct = (productId)=>{\n        // TODO: Create edit product page\n        window.open(\"/dashboard/shop/products/edit/\".concat(productId), \"_blank\");\n    };\n    const handleDeleteProduct = async (productId, productTitle)=>{\n        const confirmed = await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_12__.showConfirmation)({\n            title: \"Delete Product\",\n            message: 'Are you sure you want to delete \"'.concat(productTitle, '\"? This action cannot be undone.'),\n            variant: \"danger\",\n            confirmText: \"Delete\"\n        });\n        if (confirmed) {\n            try {\n                await _lib_services_shopProducts__WEBPACK_IMPORTED_MODULE_8__.ShopProductService.deleteProduct(productId);\n                await showAlert({\n                    title: \"Success\",\n                    message: \"Product deleted successfully!\",\n                    variant: \"success\"\n                });\n                // Refresh the products list\n                fetchShopProducts();\n            } catch (error) {\n                await showAlert({\n                    title: \"Error\",\n                    message: error instanceof Error ? error.message : \"Failed to delete product\",\n                    variant: \"danger\"\n                });\n            }\n        }\n    };\n    const handleViewOrder = (order)=>{\n        setSelectedOrder(order);\n        setShowOrderDetails(true);\n    };\n    const handleUpdateOrderStatus = async (orderId, newStatus)=>{\n        // If marking as shipped, show tracking modal\n        if (newStatus === \"shipped\") {\n            const order = shopOrders.find((o)=>o.id === orderId);\n            if (order) {\n                setOrderToShip(order);\n                setShowTrackingModal(true);\n                return;\n            }\n        }\n        // Special handling for cancellation\n        if (newStatus === \"cancelled\") {\n            const confirmed = await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_12__.showConfirmation)({\n                title: \"Cancel Order\",\n                message: \"Are you sure you want to cancel this order? The payment will be refunded to the buyer's wallet.\",\n                confirmText: \"Cancel Order\",\n                cancelText: \"Keep Order\",\n                variant: \"danger\"\n            });\n            if (!confirmed) return;\n            try {\n                await _lib_services_orders__WEBPACK_IMPORTED_MODULE_9__.OrderService.cancelOrderWithRefund(orderId, (user === null || user === void 0 ? void 0 : user.id) || \"\");\n                await showAlert({\n                    title: \"Success\",\n                    message: \"Order cancelled and refund processed successfully!\",\n                    variant: \"success\"\n                });\n                fetchShopOrders();\n            } catch (error) {\n                await showAlert({\n                    title: \"Error\",\n                    message: error instanceof Error ? error.message : \"Failed to cancel order\",\n                    variant: \"danger\"\n                });\n            }\n            return;\n        }\n        const confirmed = await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_12__.showConfirmation)({\n            title: \"Update Order Status\",\n            message: 'Are you sure you want to update the order status to \"'.concat(newStatus.replace(\"_\", \" \"), '\"?'),\n            confirmText: \"Update\",\n            cancelText: \"Cancel\",\n            variant: \"warning\"\n        });\n        if (!confirmed) return;\n        try {\n            await _lib_services_orders__WEBPACK_IMPORTED_MODULE_9__.OrderService.updateOrderStatus(orderId, newStatus, undefined, undefined, undefined, user === null || user === void 0 ? void 0 : user.id);\n            await showAlert({\n                title: \"Success\",\n                message: \"Order status updated successfully!\",\n                variant: \"success\"\n            });\n            fetchShopOrders();\n        } catch (error) {\n            await showAlert({\n                title: \"Error\",\n                message: error instanceof Error ? error.message : \"Failed to update order status\",\n                variant: \"danger\"\n            });\n        }\n    };\n    const handleAddTracking = async (trackingData)=>{\n        if (!orderToShip) return;\n        try {\n            setTrackingLoading(true);\n            await _lib_services_orders__WEBPACK_IMPORTED_MODULE_9__.OrderService.updateOrderStatus(orderToShip.id, \"shipped\", trackingData.trackingNumber, trackingData.trackingUrl, \"Shipped via \".concat(trackingData.courierService), user === null || user === void 0 ? void 0 : user.id, trackingData.courierService);\n            await showAlert({\n                title: \"Success\",\n                message: \"Order marked as shipped with tracking information!\",\n                variant: \"success\"\n            });\n            setShowTrackingModal(false);\n            setOrderToShip(null);\n            fetchShopOrders();\n        } catch (error) {\n            await showAlert({\n                title: \"Error\",\n                message: error instanceof Error ? error.message : \"Failed to update order status\",\n                variant: \"danger\"\n            });\n        } finally{\n            setTrackingLoading(false);\n        }\n    };\n    const getOrderStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"pending_shipment\":\n                return \"text-orange-600 bg-orange-100\";\n            case \"confirmed\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"processing\":\n                return \"text-purple-600 bg-purple-100\";\n            case \"shipped\":\n                return \"text-indigo-600 bg-indigo-100\";\n            case \"delivered\":\n                return \"text-green-600 bg-green-100\";\n            case \"cancelled\":\n                return \"text-red-600 bg-red-100\";\n            case \"refunded\":\n                return \"text-gray-600 bg-gray-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"approved\":\n                return \"text-green-600 bg-green-100\";\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"rejected\":\n                return \"text-red-600 bg-red-100\";\n            case \"suspended\":\n                return \"text-gray-600 bg-gray-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 16\n                }, this);\n            case \"suspended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const handleBannerUpload = async (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file || !selectedShop || !(user === null || user === void 0 ? void 0 : user.id)) return;\n        // Validate file type and size\n        if (!file.type.startsWith(\"image/\")) {\n            await showAlert({\n                title: \"Invalid File\",\n                message: \"Please select an image file.\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        if (file.size > 5 * 1024 * 1024) {\n            await showAlert({\n                title: \"File Too Large\",\n                message: \"Image size must be less than 5MB.\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        try {\n            setUploadingBanner(true);\n            const bannerUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_10__.StorageService.uploadImage(file, user.id);\n            await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_7__.VendorShopService.updateShop(selectedShop.id, {\n                banner_url: bannerUrl\n            });\n            // Update the selected shop with new banner\n            setSelectedShop((prev)=>prev ? {\n                    ...prev,\n                    banner_url: bannerUrl\n                } : null);\n            // Update shops list\n            setShops((prev)=>prev.map((shop)=>shop.id === selectedShop.id ? {\n                        ...shop,\n                        banner_url: bannerUrl\n                    } : shop));\n            await showAlert({\n                title: \"Success\",\n                message: \"Shop banner updated successfully!\",\n                variant: \"success\"\n            });\n        } catch (error) {\n            await showAlert({\n                title: \"Error\",\n                message: error instanceof Error ? error.message : \"Failed to upload banner\",\n                variant: \"danger\"\n            });\n        } finally{\n            setUploadingBanner(false);\n        }\n    };\n    const handleLogoUpload = async (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file || !selectedShop || !(user === null || user === void 0 ? void 0 : user.id)) return;\n        // Validate file type and size\n        if (!file.type.startsWith(\"image/\")) {\n            await showAlert({\n                title: \"Invalid File\",\n                message: \"Please select an image file.\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        if (file.size > 2 * 1024 * 1024) {\n            await showAlert({\n                title: \"File Too Large\",\n                message: \"Logo size must be less than 2MB.\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        try {\n            setUploadingLogo(true);\n            const logoUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_10__.StorageService.uploadImage(file, user.id);\n            await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_7__.VendorShopService.updateShop(selectedShop.id, {\n                logo_url: logoUrl\n            });\n            // Update the selected shop with new logo\n            setSelectedShop((prev)=>prev ? {\n                    ...prev,\n                    logo_url: logoUrl\n                } : null);\n            // Update shops list\n            setShops((prev)=>prev.map((shop)=>shop.id === selectedShop.id ? {\n                        ...shop,\n                        logo_url: logoUrl\n                    } : shop));\n            await showAlert({\n                title: \"Success\",\n                message: \"Shop logo updated successfully!\",\n                variant: \"success\"\n            });\n        } catch (error) {\n            await showAlert({\n                title: \"Error\",\n                message: error instanceof Error ? error.message : \"Failed to upload logo\",\n                variant: \"danger\"\n            });\n        } finally{\n            setUploadingLogo(false);\n        }\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n            lineNumber: 502,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-600 mb-4\",\n                children: \"Please sign in to access your shop\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 511,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n            lineNumber: 510,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600 mb-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchUserShops,\n                    className: \"px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 520,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n            lineNumber: 518,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"My Shop\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage your vendor shop and products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedShop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-primary-blue\",\n                                                children: selectedShop.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(selectedShop.status)),\n                                                children: [\n                                                    getStatusIcon(selectedShop.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 capitalize\",\n                                                        children: selectedShop.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, this),\n                            shops.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: isKYCVerified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/create-shop\",\n                                    className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Shop\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        showAlert({\n                                            title: \"KYC Verification Required\",\n                                            message: \"You must complete KYC verification before creating a shop. Please go to Profile > Identity Verification to submit your documents.\",\n                                            variant: \"warning\"\n                                        });\n                                    },\n                                    className: \"flex items-center px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed\",\n                                    title: \"KYC verification required\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Shop\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this),\n                    shops.length === 0 ? /* No Shops State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"No shops yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Create your first shop to start selling products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, this),\n                            isKYCVerified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/create-shop\",\n                                className: \"inline-flex items-center px-6 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Create Your First Shop\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            showAlert({\n                                                title: \"KYC Verification Required\",\n                                                message: \"You must complete KYC verification before creating a shop. Please go to Profile > Identity Verification to submit your documents.\",\n                                                variant: \"warning\"\n                                            });\n                                        },\n                                        className: \"inline-flex items-center px-6 py-3 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed\",\n                                        title: \"KYC verification required\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Create Your First Shop\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Complete KYC verification to create your shop\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: selectedShop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex space-x-8 px-6\",\n                                            \"aria-label\": \"Tabs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(\"overview\"),\n                                                    className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"overview\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: \"Overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(\"products\"),\n                                                    className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"products\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: [\n                                                        \"Products (\",\n                                                        shopProducts.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(\"orders\"),\n                                                    className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"orders\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: [\n                                                        \"Orders (\",\n                                                        shopOrders.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(\"wallet\"),\n                                                    className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"wallet\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: \"Merchant Wallet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 17\n                                }, this),\n                                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 bg-gradient-to-r from-primary-blue to-secondary-blue\",\n                                                children: [\n                                                    selectedShop.banner_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: selectedShop.banner_url,\n                                                        alt: \"\".concat(selectedShop.name, \" banner\"),\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-75\",\n                                                                    children: \"No banner image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"cursor-pointer\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: handleBannerUpload,\n                                                                    className: \"hidden\",\n                                                                    disabled: uploadingBanner\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center px-3 py-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors\",\n                                                                    children: [\n                                                                        uploadingBanner ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 703,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 705,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: uploadingBanner ? \"Uploading...\" : \"Change Banner\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-20 h-20 rounded-lg overflow-hidden bg-gray-200 border-4 border-white shadow-lg\",\n                                                                                children: selectedShop.logo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: selectedShop.logo_url,\n                                                                                    alt: \"\".concat(selectedShop.name, \" logo\"),\n                                                                                    className: \"w-full h-full object-cover\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 722,\n                                                                                    columnNumber: 31\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-full h-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"h-8 w-8 text-gray-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                        lineNumber: 729,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 728,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                lineNumber: 720,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"absolute -bottom-2 -right-2 cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"file\",\n                                                                                        accept: \"image/*\",\n                                                                                        onChange: handleLogoUpload,\n                                                                                        className: \"hidden\",\n                                                                                        disabled: uploadingLogo\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                        lineNumber: 736,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-8 h-8 bg-primary-blue text-white rounded-full flex items-center justify-center hover:bg-primary-blue/90 transition-colors shadow-lg\",\n                                                                                        children: uploadingLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-3 w-3 border border-white border-t-transparent\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 745,\n                                                                                            columnNumber: 33\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"h-3 w-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 747,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                        lineNumber: 743,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                lineNumber: 735,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                                children: selectedShop.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                lineNumber: 754,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: selectedShop.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                lineNumber: 755,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Created\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: new Date(selectedShop.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-blue-700\",\n                                                                                    children: \"Products\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 773,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-2xl font-bold text-blue-900\",\n                                                                                    children: shopStats.totalProducts\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 774,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-green-700\",\n                                                                                    children: \"Views\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 782,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-2xl font-bold text-green-900\",\n                                                                                    children: shopStats.totalViews\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 783,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 781,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-purple-700\",\n                                                                                    children: \"Sales\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 791,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-2xl font-bold text-purple-900\",\n                                                                                    children: shopStats.totalSales\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 792,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-yellow-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 798,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-yellow-700\",\n                                                                                    children: \"Rating\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 800,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-2xl font-bold text-yellow-900\",\n                                                                                    children: ((_selectedShop_rating = selectedShop.rating) === null || _selectedShop_rating === void 0 ? void 0 : _selectedShop_rating.toFixed(1)) || \"0.0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 801,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 797,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false),\n                                activeTab === \"products\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Products\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 819,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 mt-1\",\n                                                                    children: \"Manage your shop products\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/dashboard/shop/products/add\",\n                                                            className: \"flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 826,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Add Product\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 19\n                                            }, this),\n                                            shopProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                        children: \"No products yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6\",\n                                                        children: \"Start adding products to your shop to begin selling\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/dashboard/shop/products/add\",\n                                                        className: \"inline-flex items-center px-6 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Add Your First Product\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"divide-y divide-gray-200\",\n                                                children: shopProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0\",\n                                                                    children: product.images && product.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: product.images[0].image_url,\n                                                                        alt: product.title,\n                                                                        className: \"w-full h-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 853,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full h-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 860,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 859,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                                            children: product.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 869,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-gray-600 text-sm line-clamp-2 mb-2\",\n                                                                                            children: product.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 872,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: [\n                                                                                                        \"SKU: \",\n                                                                                                        product.sku || \"N/A\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 876,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"•\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 877,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"capitalize\",\n                                                                                                    children: product.condition\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 878,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"•\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 879,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: [\n                                                                                                        \"Stock: \",\n                                                                                                        product.stock_quantity\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 880,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"•\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 881,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: [\n                                                                                                        \"Views: \",\n                                                                                                        product.views || 0\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 882,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 875,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 868,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-right ml-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-2xl font-bold text-primary-blue mb-1\",\n                                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatCurrency)(product.price || 0)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 886,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(product.status === \"active\" ? \"bg-green-100 text-green-800\" : product.status === \"inactive\" ? \"bg-gray-100 text-gray-800\" : product.status === \"out_of_stock\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                                            children: product.status.replace(\"_\", \" \")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 889,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 885,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 867,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleViewProduct(product.id),\n                                                                                    className: \"flex items-center px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                                                    title: \"View Product\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 910,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"View\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 905,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleEditProduct(product.id),\n                                                                                    className: \"flex items-center px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors\",\n                                                                                    title: \"Edit Product\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 918,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"Edit\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 913,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDeleteProduct(product.id, product.title),\n                                                                                    className: \"flex items-center px-3 py-1.5 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors\",\n                                                                                    title: \"Delete Product\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 926,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"Delete\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 921,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 904,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, product.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false),\n                                activeTab === \"orders\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900\",\n                                                            children: \"Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mt-1\",\n                                                            children: \"Manage your shop orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 945,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 21\n                                        }, this),\n                                        shopOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                    children: \"No orders yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 955,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Orders will appear here when customers purchase your products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"divide-y divide-gray-200\",\n                                            children: shopOrders.map((order)=>{\n                                                var _order_buyer, _order_order_items;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                \"Order #\",\n                                                                                order.order_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 964,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                (_order_buyer = order.buyer) === null || _order_buyer === void 0 ? void 0 : _order_buyer.full_name,\n                                                                                \" • \",\n                                                                                new Date(order.created_at).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 967,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 963,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-lg font-bold text-gray-900\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatCurrency)(order.total_amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 972,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium \".concat(getOrderStatusColor(order.status)),\n                                                                            children: order.status.replace(\"_\", \" \").charAt(0).toUpperCase() + order.status.replace(\"_\", \" \").slice(1)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 971,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                ((_order_order_items = order.order_items) === null || _order_order_items === void 0 ? void 0 : _order_order_items.length) || 0,\n                                                                                \" item(s)\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 983,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 984,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: order.payment_method.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 985,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        order.tracking_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 988,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Tracking: \",\n                                                                                        order.tracking_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 989,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 982,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleViewOrder(order),\n                                                                            className: \"flex items-center px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 998,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"View\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 994,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        order.status !== \"delivered\" && order.status !== \"cancelled\" && order.status !== \"refunded\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: order.status,\n                                                                            onChange: (e)=>handleUpdateOrderStatus(order.id, e.target.value),\n                                                                            className: \"text-sm border border-gray-300 rounded-lg px-2 py-1.5 focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                                            children: [\n                                                                                order.status === \"pending_shipment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"pending_shipment\",\n                                                                                            children: \"Pending Shipment\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 1009,\n                                                                                            columnNumber: 41\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"shipped\",\n                                                                                            children: \"Ship Order\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 1010,\n                                                                                            columnNumber: 41\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"cancelled\",\n                                                                                            children: \"Cancel Order\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 1011,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true),\n                                                                                order.status === \"shipped\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"shipped\",\n                                                                                    children: \"Shipped\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 1015,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 1002,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 993,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, order.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 942,\n                                    columnNumber: 19\n                                }, this),\n                                activeTab === \"wallet\" && selectedShop && (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MerchantWallet__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    shopId: selectedShop.id,\n                                    userId: user.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, this),\n            showOrderDetails && selectedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: [\n                                                \"Order #\",\n                                                selectedOrder.order_number\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                (_selectedOrder_buyer = selectedOrder.buyer) === null || _selectedOrder_buyer === void 0 ? void 0 : _selectedOrder_buyer.full_name,\n                                                \" • \",\n                                                new Date(selectedOrder.created_at).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1046,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowOrderDetails(false),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 1058,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                            lineNumber: 1045,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 overflow-y-auto max-h-[calc(90vh-120px)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Order Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1069,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getOrderStatusColor(selectedOrder.status)),\n                                                                    children: selectedOrder.status.replace(\"_\", \" \").charAt(0).toUpperCase() + selectedOrder.status.replace(\"_\", \" \").slice(1)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1070,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Payment:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1075,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: selectedOrder.payment_method.replace(\"_\", \" \")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1076,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1074,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Total:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1079,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatCurrency)(selectedOrder.total_amount)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1080,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1078,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1065,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Customer Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1087,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1090,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: selectedOrder.shipping_address.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1091,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1089,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1094,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: selectedOrder.shipping_address.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1095,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1093,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Address:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1098,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2 mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: selectedOrder.shipping_address.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 1100,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                selectedOrder.shipping_address.city,\n                                                                                \", \",\n                                                                                selectedOrder.shipping_address.district\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 1101,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedOrder.shipping_address.postal_code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: selectedOrder.shipping_address.postal_code\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 1103,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1099,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1097,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1088,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Order Items\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: (_selectedOrder_order_items = selectedOrder.order_items) === null || _selectedOrder_order_items === void 0 ? void 0 : _selectedOrder_order_items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 p-3 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: item.product_title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1119,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"Qty: \",\n                                                                        item.quantity,\n                                                                        \" \\xd7 \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatCurrency)(item.unit_price)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1120,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatCurrency)(item.total_price)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 1125,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1124,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1116,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1114,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 15\n                                }, this),\n                                selectedOrder.buyer_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Customer Notes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1137,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 bg-gray-50 rounded-lg p-3\",\n                                            children: selectedOrder.buyer_notes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1138,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1136,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                            lineNumber: 1062,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 1044,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 1043,\n                columnNumber: 9\n            }, this),\n            showTrackingModal && orderToShip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_orders_TrackingModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: showTrackingModal,\n                onClose: ()=>{\n                    setShowTrackingModal(false);\n                    setOrderToShip(null);\n                },\n                onSubmit: handleAddTracking,\n                loading: trackingLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 1150,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n        lineNumber: 531,\n        columnNumber: 5\n    }, this);\n}\n_s(MyShopPage, \"Gee4dKKR8PH4dYaUa+0RgDRN/rk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_5__.useKYCCheck,\n        _contexts_AlertContext__WEBPACK_IMPORTED_MODULE_6__.useAlert\n    ];\n});\n_c = MyShopPage;\nvar _c;\n$RefreshReg$(_c, \"MyShopPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3Nob3AvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRStEO0FBa0IxQztBQUNPO0FBQ3dDO0FBQ3BCO0FBQ1E7QUFDTjtBQUNZO0FBQ0U7QUFDWjtBQUNHO0FBRVg7QUFDb0M7QUFDbkI7QUFDSztBQVNuRCxTQUFTZ0M7UUFxdkJ1REMsc0JBMFA1REMsc0JBZ0VBQTs7SUE5aUNqQixNQUFNLEVBQUVDLElBQUksRUFBRUMsU0FBU0MsV0FBVyxFQUFFLEdBQUdoQiw4REFBT0E7SUFDOUMsTUFBTSxFQUFFaUIsYUFBYSxFQUFFLEdBQUdoQixzRUFBV0E7SUFDckMsTUFBTSxFQUFFaUIsU0FBUyxFQUFFLEdBQUdoQixnRUFBUUE7SUFDOUIsTUFBTSxDQUFDaUIsT0FBT0MsU0FBUyxHQUFHeEMsK0NBQVFBLENBQWUsRUFBRTtJQUNuRCxNQUFNLENBQUNnQyxjQUFjUyxnQkFBZ0IsR0FBR3pDLCtDQUFRQSxDQUFvQjtJQUNwRSxNQUFNLENBQUMwQyxjQUFjQyxnQkFBZ0IsR0FBRzNDLCtDQUFRQSxDQUFnQixFQUFFO0lBQ2xFLE1BQU0sQ0FBQzRDLFlBQVlDLGNBQWMsR0FBRzdDLCtDQUFRQSxDQUFjLEVBQUU7SUFDNUQsTUFBTSxDQUFDOEMsV0FBV0MsYUFBYSxHQUFHL0MsK0NBQVFBLENBQVk7UUFDcERnRCxlQUFlO1FBQ2ZDLFlBQVk7UUFDWkMsZ0JBQWdCO1FBQ2hCQyxZQUFZO0lBQ2Q7SUFDQSxNQUFNLENBQUNoQixTQUFTaUIsV0FBVyxHQUFHcEQsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDcUQsV0FBV0MsYUFBYSxHQUFHdEQsK0NBQVFBLENBQWdEO0lBQzFGLE1BQU0sQ0FBQ2lDLGVBQWVzQixpQkFBaUIsR0FBR3ZELCtDQUFRQSxDQUFtQjtJQUNyRSxNQUFNLENBQUN3RCxrQkFBa0JDLG9CQUFvQixHQUFHekQsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDMEQsbUJBQW1CQyxxQkFBcUIsR0FBRzNELCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzRELGFBQWFDLGVBQWUsR0FBRzdELCtDQUFRQSxDQUFtQjtJQUNqRSxNQUFNLENBQUM4RCxpQkFBaUJDLG1CQUFtQixHQUFHL0QsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDZ0UsT0FBT0MsU0FBUyxHQUFHakUsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ2tFLGlCQUFpQkMsbUJBQW1CLEdBQUduRSwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNvRSxlQUFlQyxpQkFBaUIsR0FBR3JFLCtDQUFRQSxDQUFDO0lBRW5ELE1BQU1zRSxpQkFBaUI7UUFDckIsSUFBSSxFQUFDcEMsaUJBQUFBLDJCQUFBQSxLQUFNcUMsRUFBRSxHQUFFO1lBQ2JOLFNBQVM7WUFDVGIsV0FBVztZQUNYO1FBQ0Y7UUFFQSxJQUFJO1lBQ0ZBLFdBQVc7WUFDWCxNQUFNb0IsWUFBWSxNQUFNakQsd0VBQWlCQSxDQUFDa0QsWUFBWSxDQUFDdkMsS0FBS3FDLEVBQUU7WUFDOUQvQixTQUFTZ0M7WUFFVCxJQUFJQSxVQUFVRSxNQUFNLEdBQUcsR0FBRztnQkFDeEJqQyxnQkFBZ0IrQixTQUFTLENBQUMsRUFBRTtZQUM5QjtRQUNGLEVBQUUsT0FBT0csS0FBSztZQUNaVixTQUFTVSxlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSekIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNMEIsb0JBQW9CNUUsa0RBQVdBLENBQUM7UUFDcEMsSUFBSSxFQUFDOEIseUJBQUFBLG1DQUFBQSxhQUFjdUMsRUFBRSxHQUFFO1lBQ3JCNUIsZ0JBQWdCLEVBQUU7WUFDbEI7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNLEVBQUVvQyxRQUFRLEVBQUUsR0FBRyxNQUFNeEQsd0VBQWlCQSxDQUFDeUQsZUFBZSxDQUFDaEQsYUFBYXVDLEVBQUUsRUFBRSxHQUFHO1lBQ2pGNUIsZ0JBQWdCb0M7UUFDbEIsRUFBRSxPQUFPSixLQUFLO1lBQ1pNLFFBQVFqQixLQUFLLENBQUMsaUNBQWlDVztZQUMvQyxrRUFBa0U7WUFDbEVoQyxnQkFBZ0IsRUFBRTtRQUNwQjtJQUNGLEdBQUc7UUFBQ1gseUJBQUFBLG1DQUFBQSxhQUFjdUMsRUFBRTtLQUFDO0lBRXJCLE1BQU1XLGtCQUFrQmhGLGtEQUFXQSxDQUFDO1FBQ2xDLElBQUksRUFBQzhCLHlCQUFBQSxtQ0FBQUEsYUFBY3VDLEVBQUUsS0FBSSxFQUFDckMsaUJBQUFBLDJCQUFBQSxLQUFNcUMsRUFBRSxHQUFFO1lBQ2xDMUIsY0FBYyxFQUFFO1lBQ2hCO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTSxFQUFFc0MsTUFBTSxFQUFFLEdBQUcsTUFBTTFELDhEQUFZQSxDQUFDMkQsZUFBZSxDQUFDbEQsS0FBS3FDLEVBQUUsRUFBRXZDLGFBQWF1QyxFQUFFLEVBQUUsR0FBRztZQUNuRjFCLGNBQWNzQztRQUNoQixFQUFFLE9BQU9SLEtBQUs7WUFDWk0sUUFBUWpCLEtBQUssQ0FBQywrQkFBK0JXO1lBQzdDLGdFQUFnRTtZQUNoRTlCLGNBQWMsRUFBRTtRQUNsQjtJQUNGLEdBQUc7UUFBQ2IseUJBQUFBLG1DQUFBQSxhQUFjdUMsRUFBRTtRQUFFckMsaUJBQUFBLDJCQUFBQSxLQUFNcUMsRUFBRTtLQUFDO0lBRS9CLE1BQU1jLGlCQUFpQm5GLGtEQUFXQSxDQUFDO1FBQ2pDLElBQUksRUFBQzhCLHlCQUFBQSxtQ0FBQUEsYUFBY3VDLEVBQUUsR0FBRTtZQUNyQnhCLGFBQWE7Z0JBQ1hDLGVBQWU7Z0JBQ2ZDLFlBQVk7Z0JBQ1pDLGdCQUFnQjtnQkFDaEJDLFlBQVk7WUFDZDtZQUNBO1FBQ0Y7UUFFQSxJQUFJO1lBQ0Ysd0NBQXdDO1lBQ3hDLE1BQU1tQyxnQkFBZ0IsTUFBTS9ELHdFQUFpQkEsQ0FBQ2dFLHFCQUFxQixDQUFDdkQsYUFBYXVDLEVBQUU7WUFFbkYsMENBQTBDO1lBQzFDLElBQUlpQixpQkFBaUI7WUFDckIsSUFBSTtnQkFDRkEsaUJBQWlCLE1BQU1qRSx3RUFBaUJBLENBQUNrRSxxQkFBcUIsQ0FBQ3pELGFBQWF1QyxFQUFFO1lBQ2hGLEVBQUUsT0FBT21CLGdCQUFnQjtnQkFDdkJULFFBQVFVLElBQUksQ0FBQywyQ0FBMkNEO1lBQzFEO1lBRUEsbUNBQW1DO1lBQ25DM0MsYUFBYTtnQkFDWEMsZUFBZXNDLGNBQWNNLGNBQWMsSUFBSTtnQkFDL0MzQyxZQUFZcUMsY0FBY08sV0FBVyxJQUFJO2dCQUN6QzNDLGdCQUFnQnNDO2dCQUNoQnJDLFlBQVltQyxjQUFjUSxXQUFXLElBQUk7WUFDM0M7UUFFRixFQUFFLE9BQU9uQixLQUFLO1lBQ1pNLFFBQVFqQixLQUFLLENBQUMsOEJBQThCVztZQUU1Qyw2Q0FBNkM7WUFDN0MsSUFBSWEsaUJBQWlCO1lBQ3JCLElBQUk7Z0JBQ0ZBLGlCQUFpQixNQUFNakUsd0VBQWlCQSxDQUFDa0UscUJBQXFCLENBQUN6RCxhQUFhdUMsRUFBRTtZQUNoRixFQUFFLE9BQU9tQixnQkFBZ0I7Z0JBQ3ZCVCxRQUFRVSxJQUFJLENBQUMsdURBQXVERDtZQUN0RTtZQUVBM0MsYUFBYTtnQkFDWEMsZUFBZWhCLGFBQWE0RCxjQUFjLElBQUk7Z0JBQzlDM0MsWUFBWWpCLGFBQWE2RCxXQUFXLElBQUk7Z0JBQ3hDM0MsZ0JBQWdCc0M7Z0JBQ2hCckMsWUFBWW5CLGFBQWE4RCxXQUFXLElBQUk7WUFDMUM7UUFDRjtJQUNGLEdBQUc7UUFBQzlELHlCQUFBQSxtQ0FBQUEsYUFBY3VDLEVBQUU7S0FBQyxFQUFFLHFEQUFxRDs7SUFFNUUsb0ZBQW9GO0lBQ3BGdEUsZ0RBQVNBLENBQUM7UUFDUixJQUFJaUMsTUFBTTtZQUNSb0M7UUFDRjtJQUNGLEdBQUc7UUFBQ3BDO0tBQUs7SUFFVGpDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSStCLGNBQWM7WUFDaEI4QztZQUNBSTtZQUNBRztRQUNGO0lBQ0YsR0FBRztRQUFDckQseUJBQUFBLG1DQUFBQSxhQUFjdUMsRUFBRTtRQUFFTztRQUFtQkk7UUFBaUJHO0tBQWUsRUFBRSxvQ0FBb0M7O0lBRS9HLE1BQU1VLG9CQUFvQixDQUFDQztRQUN6QkMsT0FBT0MsSUFBSSxDQUFDLFlBQXNCLE9BQVZGLFlBQWE7SUFDdkM7SUFFQSxNQUFNRyxvQkFBb0IsQ0FBQ0g7UUFDekIsaUNBQWlDO1FBQ2pDQyxPQUFPQyxJQUFJLENBQUMsaUNBQTJDLE9BQVZGLFlBQWE7SUFDNUQ7SUFFQSxNQUFNSSxzQkFBc0IsT0FBT0osV0FBbUJLO1FBQ3BELE1BQU1DLFlBQVksTUFBTTFFLG9GQUFnQkEsQ0FBQztZQUN2QzJFLE9BQU87WUFDUDFCLFNBQVMsb0NBQWlELE9BQWJ3QixjQUFhO1lBQzFERyxTQUFTO1lBQ1RDLGFBQWE7UUFDZjtRQUVBLElBQUlILFdBQVc7WUFDYixJQUFJO2dCQUNGLE1BQU05RSwwRUFBa0JBLENBQUNrRixhQUFhLENBQUNWO2dCQUN2QyxNQUFNMUQsVUFBVTtvQkFDZGlFLE9BQU87b0JBQ1AxQixTQUFTO29CQUNUMkIsU0FBUztnQkFDWDtnQkFDQSw0QkFBNEI7Z0JBQzVCMUI7WUFDRixFQUFFLE9BQU9kLE9BQU87Z0JBQ2QsTUFBTTFCLFVBQVU7b0JBQ2RpRSxPQUFPO29CQUNQMUIsU0FBU2IsaUJBQWlCWSxRQUFRWixNQUFNYSxPQUFPLEdBQUc7b0JBQ2xEMkIsU0FBUztnQkFDWDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLE1BQU1HLGtCQUFrQixDQUFDQztRQUN2QnJELGlCQUFpQnFEO1FBQ2pCbkQsb0JBQW9CO0lBQ3RCO0lBRUEsTUFBTW9ELDBCQUEwQixPQUFPQyxTQUFpQkM7UUFDdEQsNkNBQTZDO1FBQzdDLElBQUlBLGNBQWMsV0FBVztZQUMzQixNQUFNSCxRQUFRaEUsV0FBV29FLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRTFDLEVBQUUsS0FBS3VDO1lBQzVDLElBQUlGLE9BQU87Z0JBQ1QvQyxlQUFlK0M7Z0JBQ2ZqRCxxQkFBcUI7Z0JBQ3JCO1lBQ0Y7UUFDRjtRQUVBLG9DQUFvQztRQUNwQyxJQUFJb0QsY0FBYyxhQUFhO1lBQzdCLE1BQU1ULFlBQVksTUFBTTFFLG9GQUFnQkEsQ0FBQztnQkFDdkMyRSxPQUFPO2dCQUNQMUIsU0FBUztnQkFDVDRCLGFBQWE7Z0JBQ2JTLFlBQVk7Z0JBQ1pWLFNBQVM7WUFDWDtZQUVBLElBQUksQ0FBQ0YsV0FBVztZQUVoQixJQUFJO2dCQUNGLE1BQU03RSw4REFBWUEsQ0FBQzBGLHFCQUFxQixDQUFDTCxTQUFTNUUsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNcUMsRUFBRSxLQUFJO2dCQUM5RCxNQUFNakMsVUFBVTtvQkFDZGlFLE9BQU87b0JBQ1AxQixTQUFTO29CQUNUMkIsU0FBUztnQkFDWDtnQkFDQXRCO1lBQ0YsRUFBRSxPQUFPbEIsT0FBTztnQkFDZCxNQUFNMUIsVUFBVTtvQkFDZGlFLE9BQU87b0JBQ1AxQixTQUFTYixpQkFBaUJZLFFBQVFaLE1BQU1hLE9BQU8sR0FBRztvQkFDbEQyQixTQUFTO2dCQUNYO1lBQ0Y7WUFDQTtRQUNGO1FBRUEsTUFBTUYsWUFBWSxNQUFNMUUsb0ZBQWdCQSxDQUFDO1lBQ3ZDMkUsT0FBTztZQUNQMUIsU0FBUyx3REFBb0YsT0FBNUJrQyxVQUFVSyxPQUFPLENBQUMsS0FBSyxNQUFLO1lBQzdGWCxhQUFhO1lBQ2JTLFlBQVk7WUFDWlYsU0FBUztRQUNYO1FBRUEsSUFBSSxDQUFDRixXQUFXO1FBRWhCLElBQUk7WUFDRixNQUFNN0UsOERBQVlBLENBQUM0RixpQkFBaUIsQ0FBQ1AsU0FBU0MsV0FBV08sV0FBV0EsV0FBV0EsV0FBV3BGLGlCQUFBQSwyQkFBQUEsS0FBTXFDLEVBQUU7WUFDbEcsTUFBTWpDLFVBQVU7Z0JBQ2RpRSxPQUFPO2dCQUNQMUIsU0FBUztnQkFDVDJCLFNBQVM7WUFDWDtZQUNBdEI7UUFDRixFQUFFLE9BQU9sQixPQUFPO1lBQ2QsTUFBTTFCLFVBQVU7Z0JBQ2RpRSxPQUFPO2dCQUNQMUIsU0FBU2IsaUJBQWlCWSxRQUFRWixNQUFNYSxPQUFPLEdBQUc7Z0JBQ2xEMkIsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU1lLG9CQUFvQixPQUFPQztRQUsvQixJQUFJLENBQUM1RCxhQUFhO1FBRWxCLElBQUk7WUFDRkcsbUJBQW1CO1lBRW5CLE1BQU10Qyw4REFBWUEsQ0FBQzRGLGlCQUFpQixDQUNsQ3pELFlBQVlXLEVBQUUsRUFDZCxXQUNBaUQsYUFBYUMsY0FBYyxFQUMzQkQsYUFBYUUsV0FBVyxFQUN4QixlQUEyQyxPQUE1QkYsYUFBYUcsY0FBYyxHQUMxQ3pGLGlCQUFBQSwyQkFBQUEsS0FBTXFDLEVBQUUsRUFDUmlELGFBQWFHLGNBQWM7WUFHN0IsTUFBTXJGLFVBQVU7Z0JBQ2RpRSxPQUFPO2dCQUNQMUIsU0FBUztnQkFDVDJCLFNBQVM7WUFDWDtZQUVBN0MscUJBQXFCO1lBQ3JCRSxlQUFlO1lBQ2ZxQjtRQUNGLEVBQUUsT0FBT2xCLE9BQU87WUFDZCxNQUFNMUIsVUFBVTtnQkFDZGlFLE9BQU87Z0JBQ1AxQixTQUFTYixpQkFBaUJZLFFBQVFaLE1BQU1hLE9BQU8sR0FBRztnQkFDbEQyQixTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1J6QyxtQkFBbUI7UUFDckI7SUFDRjtJQUVBLE1BQU02RCxzQkFBc0IsQ0FBQ0M7UUFDM0IsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUlBLE1BQU1DLGlCQUFpQixDQUFDRDtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTUUsZ0JBQWdCLENBQUNGO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQ2pILGtMQUFXQTtvQkFBQ29ILFdBQVU7Ozs7OztZQUNoQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDckgsa0xBQUtBO29CQUFDcUgsV0FBVTs7Ozs7O1lBQzFCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNuSCxrTEFBT0E7b0JBQUNtSCxXQUFVOzs7Ozs7WUFDNUIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ2xILGtMQUFXQTtvQkFBQ2tILFdBQVU7Ozs7OztZQUNoQztnQkFDRSxxQkFBTyw4REFBQ3JILGtMQUFLQTtvQkFBQ3FILFdBQVU7Ozs7OztRQUM1QjtJQUNGO0lBRUEsTUFBTUMscUJBQXFCLE9BQU9DO1lBQ25CQTtRQUFiLE1BQU1DLFFBQU9ELGtCQUFBQSxFQUFFRSxNQUFNLENBQUNDLEtBQUssY0FBZEgsc0NBQUFBLGVBQWdCLENBQUMsRUFBRTtRQUNoQyxJQUFJLENBQUNDLFFBQVEsQ0FBQ25HLGdCQUFnQixFQUFDRSxpQkFBQUEsMkJBQUFBLEtBQU1xQyxFQUFFLEdBQUU7UUFFekMsOEJBQThCO1FBQzlCLElBQUksQ0FBQzRELEtBQUtHLElBQUksQ0FBQ0MsVUFBVSxDQUFDLFdBQVc7WUFDbkMsTUFBTWpHLFVBQVU7Z0JBQ2RpRSxPQUFPO2dCQUNQMUIsU0FBUztnQkFDVDJCLFNBQVM7WUFDWDtZQUNBO1FBQ0Y7UUFFQSxJQUFJMkIsS0FBS0ssSUFBSSxHQUFHLElBQUksT0FBTyxNQUFNO1lBQy9CLE1BQU1sRyxVQUFVO2dCQUNkaUUsT0FBTztnQkFDUDFCLFNBQVM7Z0JBQ1QyQixTQUFTO1lBQ1g7WUFDQTtRQUNGO1FBRUEsSUFBSTtZQUNGckMsbUJBQW1CO1lBQ25CLE1BQU1zRSxZQUFZLE1BQU0vRyxrRUFBY0EsQ0FBQ2dILFdBQVcsQ0FBQ1AsTUFBTWpHLEtBQUtxQyxFQUFFO1lBRWhFLE1BQU1oRCx3RUFBaUJBLENBQUNvSCxVQUFVLENBQUMzRyxhQUFhdUMsRUFBRSxFQUFFO2dCQUFFcUUsWUFBWUg7WUFBVTtZQUU1RSwyQ0FBMkM7WUFDM0NoRyxnQkFBZ0JvRyxDQUFBQSxPQUFRQSxPQUFPO29CQUFFLEdBQUdBLElBQUk7b0JBQUVELFlBQVlIO2dCQUFVLElBQUk7WUFFcEUsb0JBQW9CO1lBQ3BCakcsU0FBU3FHLENBQUFBLE9BQVFBLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUEsT0FDeEJBLEtBQUt4RSxFQUFFLEtBQUt2QyxhQUFhdUMsRUFBRSxHQUFHO3dCQUFFLEdBQUd3RSxJQUFJO3dCQUFFSCxZQUFZSDtvQkFBVSxJQUFJTTtZQUdyRSxNQUFNekcsVUFBVTtnQkFDZGlFLE9BQU87Z0JBQ1AxQixTQUFTO2dCQUNUMkIsU0FBUztZQUNYO1FBQ0YsRUFBRSxPQUFPeEMsT0FBTztZQUNkLE1BQU0xQixVQUFVO2dCQUNkaUUsT0FBTztnQkFDUDFCLFNBQVNiLGlCQUFpQlksUUFBUVosTUFBTWEsT0FBTyxHQUFHO2dCQUNsRDJCLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUnJDLG1CQUFtQjtRQUNyQjtJQUNGO0lBRUEsTUFBTTZFLG1CQUFtQixPQUFPZDtZQUNqQkE7UUFBYixNQUFNQyxRQUFPRCxrQkFBQUEsRUFBRUUsTUFBTSxDQUFDQyxLQUFLLGNBQWRILHNDQUFBQSxlQUFnQixDQUFDLEVBQUU7UUFDaEMsSUFBSSxDQUFDQyxRQUFRLENBQUNuRyxnQkFBZ0IsRUFBQ0UsaUJBQUFBLDJCQUFBQSxLQUFNcUMsRUFBRSxHQUFFO1FBRXpDLDhCQUE4QjtRQUM5QixJQUFJLENBQUM0RCxLQUFLRyxJQUFJLENBQUNDLFVBQVUsQ0FBQyxXQUFXO1lBQ25DLE1BQU1qRyxVQUFVO2dCQUNkaUUsT0FBTztnQkFDUDFCLFNBQVM7Z0JBQ1QyQixTQUFTO1lBQ1g7WUFDQTtRQUNGO1FBRUEsSUFBSTJCLEtBQUtLLElBQUksR0FBRyxJQUFJLE9BQU8sTUFBTTtZQUMvQixNQUFNbEcsVUFBVTtnQkFDZGlFLE9BQU87Z0JBQ1AxQixTQUFTO2dCQUNUMkIsU0FBUztZQUNYO1lBQ0E7UUFDRjtRQUVBLElBQUk7WUFDRm5DLGlCQUFpQjtZQUNqQixNQUFNNEUsVUFBVSxNQUFNdkgsa0VBQWNBLENBQUNnSCxXQUFXLENBQUNQLE1BQU1qRyxLQUFLcUMsRUFBRTtZQUU5RCxNQUFNaEQsd0VBQWlCQSxDQUFDb0gsVUFBVSxDQUFDM0csYUFBYXVDLEVBQUUsRUFBRTtnQkFBRTJFLFVBQVVEO1lBQVE7WUFFeEUseUNBQXlDO1lBQ3pDeEcsZ0JBQWdCb0csQ0FBQUEsT0FBUUEsT0FBTztvQkFBRSxHQUFHQSxJQUFJO29CQUFFSyxVQUFVRDtnQkFBUSxJQUFJO1lBRWhFLG9CQUFvQjtZQUNwQnpHLFNBQVNxRyxDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNDLENBQUFBLE9BQ3hCQSxLQUFLeEUsRUFBRSxLQUFLdkMsYUFBYXVDLEVBQUUsR0FBRzt3QkFBRSxHQUFHd0UsSUFBSTt3QkFBRUcsVUFBVUQ7b0JBQVEsSUFBSUY7WUFHakUsTUFBTXpHLFVBQVU7Z0JBQ2RpRSxPQUFPO2dCQUNQMUIsU0FBUztnQkFDVDJCLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBT3hDLE9BQU87WUFDZCxNQUFNMUIsVUFBVTtnQkFDZGlFLE9BQU87Z0JBQ1AxQixTQUFTYixpQkFBaUJZLFFBQVFaLE1BQU1hLE9BQU8sR0FBRztnQkFDbEQyQixTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1JuQyxpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLElBQUlqQyxlQUFlRCxTQUFTO1FBQzFCLHFCQUNFLDhEQUFDZ0g7WUFBSW5CLFdBQVU7c0JBQ1gsNEVBQUNtQjtnQkFBSW5CLFdBQVU7Ozs7Ozs7Ozs7O0lBR3ZCO0lBRUEsSUFBSSxDQUFDOUYsTUFBTTtRQUNULHFCQUNFLDhEQUFDaUg7WUFBSW5CLFdBQVU7c0JBQ1gsNEVBQUNtQjtnQkFBSW5CLFdBQVU7MEJBQW9COzs7Ozs7Ozs7OztJQUczQztJQUVBLElBQUloRSxPQUFPO1FBQ1QscUJBQ0UsOERBQUNtRjtZQUFJbkIsV0FBVTs7OEJBQ1gsOERBQUNtQjtvQkFBSW5CLFdBQVU7OEJBQXFCaEU7Ozs7Ozs4QkFDcEMsOERBQUNvRjtvQkFDQ0MsU0FBUy9FO29CQUNUMEQsV0FBVTs4QkFDWDs7Ozs7Ozs7Ozs7O0lBS1Q7SUFFQSxxQkFDRSw4REFBQzdHLDZFQUFlQTs7MEJBQ2QsOERBQUNnSTtnQkFBSW5CLFdBQVU7O2tDQUViLDhEQUFDbUI7d0JBQUluQixXQUFVOzswQ0FDYiw4REFBQ21COztrREFDQyw4REFBQ0c7d0NBQUd0QixXQUFVO2tEQUFtQzs7Ozs7O2tEQUNqRCw4REFBQ3VCO3dDQUFFdkIsV0FBVTtrREFBZ0I7Ozs7OztvQ0FDNUJoRyw4QkFDQyw4REFBQ21IO3dDQUFJbkIsV0FBVTs7MERBQ2IsOERBQUN3QjtnREFBR3hCLFdBQVU7MERBQTJDaEcsYUFBYXlILElBQUk7Ozs7OzswREFDMUUsOERBQUNDO2dEQUFLMUIsV0FBVyx1RUFBMkcsT0FBcENGLGVBQWU5RixhQUFhNkYsTUFBTTs7b0RBQ3ZIRSxjQUFjL0YsYUFBYTZGLE1BQU07a0VBQ2xDLDhEQUFDNkI7d0RBQUsxQixXQUFVO2tFQUFtQmhHLGFBQWE2RixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBSzdEdEYsTUFBTW1DLE1BQU0sS0FBSyxtQkFDaEIsOERBQUN5RTtnQ0FBSW5CLFdBQVU7MENBQ1ozRiw4QkFDQyw4REFBQ25CLGlEQUFJQTtvQ0FDSHlJLE1BQUs7b0NBQ0wzQixXQUFVOztzREFFViw4REFBQzVILGtMQUFJQTs0Q0FBQzRILFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozt5REFJbkMsOERBQUNvQjtvQ0FDQ0MsU0FBUzt3Q0FDUC9HLFVBQVU7NENBQ1JpRSxPQUFPOzRDQUNQMUIsU0FBUzs0Q0FDVDJCLFNBQVM7d0NBQ1g7b0NBQ0Y7b0NBQ0F3QixXQUFVO29DQUNWekIsT0FBTTs7c0RBRU4sOERBQUNuRyxrTEFBSUE7NENBQUM0SCxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBUTFDekYsTUFBTW1DLE1BQU0sS0FBSyxJQUNoQixrQkFBa0IsaUJBQ2xCLDhEQUFDeUU7d0JBQUluQixXQUFVOzswQ0FDYiw4REFBQzdILGtMQUFLQTtnQ0FBQzZILFdBQVU7Ozs7OzswQ0FDakIsOERBQUM0QjtnQ0FBRzVCLFdBQVU7MENBQTJDOzs7Ozs7MENBQ3pELDhEQUFDdUI7Z0NBQUV2QixXQUFVOzBDQUFxQjs7Ozs7OzRCQUNqQzNGLDhCQUNDLDhEQUFDbkIsaURBQUlBO2dDQUNIeUksTUFBSztnQ0FDTDNCLFdBQVU7O2tEQUVWLDhEQUFDNUgsa0xBQUlBO3dDQUFDNEgsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7O3FEQUluQyw4REFBQ21CO2dDQUFJbkIsV0FBVTs7a0RBQ2IsOERBQUNvQjt3Q0FDQ0MsU0FBUzs0Q0FDUC9HLFVBQVU7Z0RBQ1JpRSxPQUFPO2dEQUNQMUIsU0FBUztnREFDVDJCLFNBQVM7NENBQ1g7d0NBQ0Y7d0NBQ0F3QixXQUFVO3dDQUNWekIsT0FBTTs7MERBRU4sOERBQUNuRyxrTEFBSUE7Z0RBQUM0SCxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7O2tEQUduQyw4REFBQ3VCO3dDQUFFdkIsV0FBVTs7MERBQ1gsOERBQUNsSCxrTEFBV0E7Z0RBQUNrSCxXQUFVOzs7Ozs7NENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NkNBT3ZELDhEQUFDbUI7d0JBQUluQixXQUFVO2tDQUdaaEcsOEJBQ0M7OzhDQUVFLDhEQUFDbUg7b0NBQUluQixXQUFVOzhDQUNiLDRFQUFDbUI7d0NBQUluQixXQUFVO2tEQUNiLDRFQUFDNkI7NENBQUk3QixXQUFVOzRDQUFzQjhCLGNBQVc7OzhEQUM5Qyw4REFBQ1Y7b0RBQ0NDLFNBQVMsSUFBTS9GLGFBQWE7b0RBQzVCMEUsV0FBVyw0Q0FJVixPQUhDM0UsY0FBYyxhQUNWLDBDQUNBOzhEQUVQOzs7Ozs7OERBR0QsOERBQUMrRjtvREFDQ0MsU0FBUyxJQUFNL0YsYUFBYTtvREFDNUIwRSxXQUFXLDRDQUlWLE9BSEMzRSxjQUFjLGFBQ1YsMENBQ0E7O3dEQUVQO3dEQUNZWCxhQUFhZ0MsTUFBTTt3REFBQzs7Ozs7Ozs4REFFakMsOERBQUMwRTtvREFDQ0MsU0FBUyxJQUFNL0YsYUFBYTtvREFDNUIwRSxXQUFXLDRDQUlWLE9BSEMzRSxjQUFjLFdBQ1YsMENBQ0E7O3dEQUVQO3dEQUNVVCxXQUFXOEIsTUFBTTt3REFBQzs7Ozs7Ozs4REFFN0IsOERBQUMwRTtvREFDQ0MsU0FBUyxJQUFNL0YsYUFBYTtvREFDNUIwRSxXQUFXLDRDQUlWLE9BSEMzRSxjQUFjLFdBQ1YsMENBQ0E7OERBRVA7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBUU5BLGNBQWMsNEJBQ2I7OENBRUYsNEVBQUM4Rjt3Q0FBSW5CLFdBQVU7OzBEQUViLDhEQUFDbUI7Z0RBQUluQixXQUFVOztvREFDWmhHLGFBQWE0RyxVQUFVLGlCQUN0Qiw4REFBQ21CO3dEQUNDQyxLQUFLaEksYUFBYTRHLFVBQVU7d0RBQzVCcUIsS0FBSyxHQUFxQixPQUFsQmpJLGFBQWF5SCxJQUFJLEVBQUM7d0RBQzFCekIsV0FBVTs7Ozs7NkVBR1osOERBQUNtQjt3REFBSW5CLFdBQVU7a0VBQ2IsNEVBQUNtQjs0REFBSW5CLFdBQVU7OzhFQUNiLDhEQUFDL0csa0xBQVNBO29FQUFDK0csV0FBVTs7Ozs7OzhFQUNyQiw4REFBQ3VCO29FQUFFdkIsV0FBVTs4RUFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU14Qyw4REFBQ21CO3dEQUFJbkIsV0FBVTtrRUFDYiw0RUFBQ2tDOzREQUFNbEMsV0FBVTs7OEVBQ2YsOERBQUNtQztvRUFDQzdCLE1BQUs7b0VBQ0w4QixRQUFPO29FQUNQQyxVQUFVcEM7b0VBQ1ZELFdBQVU7b0VBQ1ZzQyxVQUFVcEc7Ozs7Ozs4RUFFWiw4REFBQ2lGO29FQUFJbkIsV0FBVTs7d0VBQ1o5RCxnQ0FDQyw4REFBQ2lGOzRFQUFJbkIsV0FBVTs7Ozs7aUdBRWYsOERBQUNqSCxrTEFBTUE7NEVBQUNpSCxXQUFVOzs7Ozs7c0ZBRXBCLDhEQUFDMEI7NEVBQUsxQixXQUFVO3NGQUNiOUQsa0JBQWtCLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTzlDLDhEQUFDaUY7Z0RBQUluQixXQUFVOztrRUFDYiw4REFBQ21CO3dEQUFJbkIsV0FBVTs7MEVBQ2IsOERBQUNtQjtnRUFBSW5CLFdBQVU7O2tGQUViLDhEQUFDbUI7d0VBQUluQixXQUFVOzswRkFDYiw4REFBQ21CO2dGQUFJbkIsV0FBVTswRkFDWmhHLGFBQWFrSCxRQUFRLGlCQUNwQiw4REFBQ2E7b0ZBQ0NDLEtBQUtoSSxhQUFha0gsUUFBUTtvRkFDMUJlLEtBQUssR0FBcUIsT0FBbEJqSSxhQUFheUgsSUFBSSxFQUFDO29GQUMxQnpCLFdBQVU7Ozs7O3lHQUdaLDhEQUFDbUI7b0ZBQUluQixXQUFVOzhGQUNiLDRFQUFDN0gsa0xBQUtBO3dGQUFDNkgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OzswRkFNdkIsOERBQUNrQztnRkFBTWxDLFdBQVU7O2tHQUNmLDhEQUFDbUM7d0ZBQ0M3QixNQUFLO3dGQUNMOEIsUUFBTzt3RkFDUEMsVUFBVXJCO3dGQUNWaEIsV0FBVTt3RkFDVnNDLFVBQVVsRzs7Ozs7O2tHQUVaLDhEQUFDK0U7d0ZBQUluQixXQUFVO2tHQUNaNUQsOEJBQ0MsOERBQUMrRTs0RkFBSW5CLFdBQVU7Ozs7O2lIQUVmLDhEQUFDakgsa0xBQU1BOzRGQUFDaUgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBTTFCLDhEQUFDbUI7OzBGQUNDLDhEQUFDSztnRkFBR3hCLFdBQVU7MEZBQW9DaEcsYUFBYXlILElBQUk7Ozs7OzswRkFDbkUsOERBQUNGO2dGQUFFdkIsV0FBVTswRkFBc0JoRyxhQUFhdUksV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUkvRCw4REFBQ3BCO2dFQUFJbkIsV0FBVTs7a0ZBQ2IsOERBQUNtQjt3RUFBSW5CLFdBQVU7a0ZBQXdCOzs7Ozs7a0ZBQ3ZDLDhEQUFDbUI7d0VBQUluQixXQUFVO2tGQUNaLElBQUl3QyxLQUFLeEksYUFBYXlJLFVBQVUsRUFBRUMsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBTTNELDhEQUFDdkI7d0RBQUluQixXQUFVOzswRUFDYiw4REFBQ21CO2dFQUFJbkIsV0FBVTswRUFDYiw0RUFBQ21CO29FQUFJbkIsV0FBVTs7c0ZBQ2IsOERBQUN4SCxrTEFBT0E7NEVBQUN3SCxXQUFVOzs7Ozs7c0ZBQ25CLDhEQUFDbUI7NEVBQUluQixXQUFVOzs4RkFDYiw4REFBQ3VCO29GQUFFdkIsV0FBVTs4RkFBb0M7Ozs7Ozs4RkFDakQsOERBQUN1QjtvRkFBRXZCLFdBQVU7OEZBQW9DbEYsVUFBVUUsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBSTlFLDhEQUFDbUc7Z0VBQUluQixXQUFVOzBFQUNiLDRFQUFDbUI7b0VBQUluQixXQUFVOztzRkFDYiw4REFBQ3pILGtMQUFHQTs0RUFBQ3lILFdBQVU7Ozs7OztzRkFDZiw4REFBQ21COzRFQUFJbkIsV0FBVTs7OEZBQ2IsOERBQUN1QjtvRkFBRXZCLFdBQVU7OEZBQXFDOzs7Ozs7OEZBQ2xELDhEQUFDdUI7b0ZBQUV2QixXQUFVOzhGQUFxQ2xGLFVBQVVHLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUk1RSw4REFBQ2tHO2dFQUFJbkIsV0FBVTswRUFDYiw0RUFBQ21CO29FQUFJbkIsV0FBVTs7c0ZBQ2IsOERBQUN0SCxrTEFBVUE7NEVBQUNzSCxXQUFVOzs7Ozs7c0ZBQ3RCLDhEQUFDbUI7NEVBQUluQixXQUFVOzs4RkFDYiw4REFBQ3VCO29GQUFFdkIsV0FBVTs4RkFBc0M7Ozs7Ozs4RkFDbkQsOERBQUN1QjtvRkFBRXZCLFdBQVU7OEZBQXNDbEYsVUFBVUssVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBSTdFLDhEQUFDZ0c7Z0VBQUluQixXQUFVOzBFQUNiLDRFQUFDbUI7b0VBQUluQixXQUFVOztzRkFDYiw4REFBQ3ZILGtMQUFJQTs0RUFBQ3VILFdBQVU7Ozs7OztzRkFDaEIsOERBQUNtQjs0RUFBSW5CLFdBQVU7OzhGQUNiLDhEQUFDdUI7b0ZBQUV2QixXQUFVOzhGQUFzQzs7Ozs7OzhGQUNuRCw4REFBQ3VCO29GQUFFdkIsV0FBVTs4RkFBc0NoRyxFQUFBQSx1QkFBQUEsYUFBYTJJLE1BQU0sY0FBbkIzSSwyQ0FBQUEscUJBQXFCNEksT0FBTyxDQUFDLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FXakd2SCxjQUFjLDRCQUNiOzhDQUVGLDRFQUFDOEY7d0NBQUluQixXQUFVOzswREFDYiw4REFBQ21CO2dEQUFJbkIsV0FBVTswREFDYiw0RUFBQ21CO29EQUFJbkIsV0FBVTs7c0VBQ2IsOERBQUNtQjs7OEVBQ0MsOERBQUNTO29FQUFHNUIsV0FBVTs4RUFBc0M7Ozs7Ozs4RUFDcEQsOERBQUN1QjtvRUFBRXZCLFdBQVU7OEVBQXFCOzs7Ozs7Ozs7Ozs7c0VBRXBDLDhEQUFDOUcsaURBQUlBOzREQUNIeUksTUFBSzs0REFDTDNCLFdBQVU7OzhFQUVWLDhEQUFDNUgsa0xBQUlBO29FQUFDNEgsV0FBVTs7Ozs7O2dFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQU10Q3RGLGFBQWFnQyxNQUFNLEtBQUssa0JBQ3ZCLDhEQUFDeUU7Z0RBQUluQixXQUFVOztrRUFDYiw4REFBQ3hILGtMQUFPQTt3REFBQ3dILFdBQVU7Ozs7OztrRUFDbkIsOERBQUM2Qzt3REFBRzdDLFdBQVU7a0VBQTJDOzs7Ozs7a0VBQ3pELDhEQUFDdUI7d0RBQUV2QixXQUFVO2tFQUFxQjs7Ozs7O2tFQUNsQyw4REFBQzlHLGlEQUFJQTt3REFDSHlJLE1BQUs7d0RBQ0wzQixXQUFVOzswRUFFViw4REFBQzVILGtMQUFJQTtnRUFBQzRILFdBQVU7Ozs7Ozs0REFBaUI7Ozs7Ozs7Ozs7OztxRUFLckMsOERBQUNtQjtnREFBSW5CLFdBQVU7MERBQ1p0RixhQUFhb0csR0FBRyxDQUFDLENBQUNnQyx3QkFDakIsOERBQUMzQjt3REFBcUJuQixXQUFVO2tFQUM5Qiw0RUFBQ21COzREQUFJbkIsV0FBVTs7OEVBRWIsOERBQUNtQjtvRUFBSW5CLFdBQVU7OEVBQ1o4QyxRQUFRQyxNQUFNLElBQUlELFFBQVFDLE1BQU0sQ0FBQ3JHLE1BQU0sR0FBRyxrQkFDekMsOERBQUNxRjt3RUFDQ0MsS0FBS2MsUUFBUUMsTUFBTSxDQUFDLEVBQUUsQ0FBQ0MsU0FBUzt3RUFDaENmLEtBQUthLFFBQVF2RSxLQUFLO3dFQUNsQnlCLFdBQVU7Ozs7OzZGQUdaLDhEQUFDbUI7d0VBQUluQixXQUFVO2tGQUNiLDRFQUFDeEgsa0xBQU9BOzRFQUFDd0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFNekIsOERBQUNtQjtvRUFBSW5CLFdBQVU7O3NGQUNiLDhEQUFDbUI7NEVBQUluQixXQUFVOzs4RkFDYiw4REFBQ21CO29GQUFJbkIsV0FBVTs7c0dBQ2IsOERBQUM2Qzs0RkFBRzdDLFdBQVU7c0dBQ1g4QyxRQUFRdkUsS0FBSzs7Ozs7O3NHQUVoQiw4REFBQ2dEOzRGQUFFdkIsV0FBVTtzR0FDVjhDLFFBQVFQLFdBQVc7Ozs7OztzR0FFdEIsOERBQUNwQjs0RkFBSW5CLFdBQVU7OzhHQUNiLDhEQUFDMEI7O3dHQUFLO3dHQUFNb0IsUUFBUUcsR0FBRyxJQUFJOzs7Ozs7OzhHQUMzQiw4REFBQ3ZCOzhHQUFLOzs7Ozs7OEdBQ04sOERBQUNBO29HQUFLMUIsV0FBVTs4R0FBYzhDLFFBQVFJLFNBQVM7Ozs7Ozs4R0FDL0MsOERBQUN4Qjs4R0FBSzs7Ozs7OzhHQUNOLDhEQUFDQTs7d0dBQUs7d0dBQVFvQixRQUFRSyxjQUFjOzs7Ozs7OzhHQUNwQyw4REFBQ3pCOzhHQUFLOzs7Ozs7OEdBQ04sOERBQUNBOzt3R0FBSzt3R0FBUW9CLFFBQVFNLEtBQUssSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RkFHbkMsOERBQUNqQztvRkFBSW5CLFdBQVU7O3NHQUNiLDhEQUFDbUI7NEZBQUluQixXQUFVO3NHQUNackcsMkRBQWNBLENBQUNtSixRQUFRTyxLQUFLLElBQUk7Ozs7OztzR0FFbkMsOERBQUMzQjs0RkFBSzFCLFdBQVcsdUVBUWhCLE9BUEM4QyxRQUFRakQsTUFBTSxLQUFLLFdBQ2YsZ0NBQ0FpRCxRQUFRakQsTUFBTSxLQUFLLGFBQ25CLDhCQUNBaUQsUUFBUWpELE1BQU0sS0FBSyxpQkFDbkIsNEJBQ0E7c0dBRUhpRCxRQUFRakQsTUFBTSxDQUFDVCxPQUFPLENBQUMsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQU1uQyw4REFBQytCOzRFQUFJbkIsV0FBVTs7OEZBQ2IsOERBQUNvQjtvRkFDQ0MsU0FBUyxJQUFNdEQsa0JBQWtCK0UsUUFBUXZHLEVBQUU7b0ZBQzNDeUQsV0FBVTtvRkFDVnpCLE9BQU07O3NHQUVOLDhEQUFDaEcsa0xBQUdBOzRGQUFDeUgsV0FBVTs7Ozs7O3dGQUFpQjs7Ozs7Ozs4RkFHbEMsOERBQUNvQjtvRkFDQ0MsU0FBUyxJQUFNbEQsa0JBQWtCMkUsUUFBUXZHLEVBQUU7b0ZBQzNDeUQsV0FBVTtvRkFDVnpCLE9BQU07O3NHQUVOLDhEQUFDbEcsa0xBQUlBOzRGQUFDMkgsV0FBVTs7Ozs7O3dGQUFpQjs7Ozs7Ozs4RkFHbkMsOERBQUNvQjtvRkFDQ0MsU0FBUyxJQUFNakQsb0JBQW9CMEUsUUFBUXZHLEVBQUUsRUFBRXVHLFFBQVF2RSxLQUFLO29GQUM1RHlCLFdBQVU7b0ZBQ1Z6QixPQUFNOztzR0FFTiw4REFBQ2pHLGtMQUFNQTs0RkFBQzBILFdBQVU7Ozs7Ozt3RkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBOUVuQzhDLFFBQVF2RyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztnQ0E2RjNCbEIsY0FBYywwQkFDYiw4REFBQzhGO29DQUFJbkIsV0FBVTs7c0RBQ2IsOERBQUNtQjs0Q0FBSW5CLFdBQVU7c0RBQ2IsNEVBQUNtQjtnREFBSW5CLFdBQVU7MERBQ2IsNEVBQUNtQjs7c0VBQ0MsOERBQUNTOzREQUFHNUIsV0FBVTtzRUFBc0M7Ozs7OztzRUFDcEQsOERBQUN1Qjs0REFBRXZCLFdBQVU7c0VBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQUt2Q3BGLFdBQVc4QixNQUFNLEtBQUssa0JBQ3JCLDhEQUFDeUU7NENBQUluQixXQUFVOzs4REFDYiw4REFBQ3hILGtMQUFPQTtvREFBQ3dILFdBQVU7Ozs7Ozs4REFDbkIsOERBQUM2QztvREFBRzdDLFdBQVU7OERBQTJDOzs7Ozs7OERBQ3pELDhEQUFDdUI7b0RBQUV2QixXQUFVOzhEQUFnQjs7Ozs7Ozs7Ozs7aUVBRy9CLDhEQUFDbUI7NENBQUluQixXQUFVO3NEQUNacEYsV0FBV2tHLEdBQUcsQ0FBQyxDQUFDbEM7b0RBUU5BLGNBZUlBO3FFQXRCYiw4REFBQ3VDO29EQUFtQm5CLFdBQVU7O3NFQUM1Qiw4REFBQ21COzREQUFJbkIsV0FBVTs7OEVBQ2IsOERBQUNtQjs7c0ZBQ0MsOERBQUMwQjs0RUFBRzdDLFdBQVU7O2dGQUFzQztnRkFDMUNwQixNQUFNMEUsWUFBWTs7Ozs7OztzRkFFNUIsOERBQUMvQjs0RUFBRXZCLFdBQVU7O2lGQUNWcEIsZUFBQUEsTUFBTTJFLEtBQUssY0FBWDNFLG1DQUFBQSxhQUFhNEUsU0FBUztnRkFBQztnRkFBSSxJQUFJaEIsS0FBSzVELE1BQU02RCxVQUFVLEVBQUVDLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs4RUFHN0UsOERBQUN2QjtvRUFBSW5CLFdBQVU7O3NGQUNiLDhEQUFDdUI7NEVBQUV2QixXQUFVO3NGQUNWckcsMkRBQWNBLENBQUNpRixNQUFNNkUsWUFBWTs7Ozs7O3NGQUVwQyw4REFBQy9COzRFQUFLMUIsV0FBVyx1RUFBeUcsT0FBbENKLG9CQUFvQmhCLE1BQU1pQixNQUFNO3NGQUNySGpCLE1BQU1pQixNQUFNLENBQUNULE9BQU8sQ0FBQyxLQUFLLEtBQUtzRSxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLL0UsTUFBTWlCLE1BQU0sQ0FBQ1QsT0FBTyxDQUFDLEtBQUssS0FBS3dFLEtBQUssQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUtyRyw4REFBQ3pDOzREQUFJbkIsV0FBVTs7OEVBQ2IsOERBQUNtQjtvRUFBSW5CLFdBQVU7O3NGQUNiLDhEQUFDMEI7O2dGQUFNOUMsRUFBQUEscUJBQUFBLE1BQU1pRixXQUFXLGNBQWpCakYseUNBQUFBLG1CQUFtQmxDLE1BQU0sS0FBSTtnRkFBRTs7Ozs7OztzRkFDdEMsOERBQUNnRjtzRkFBSzs7Ozs7O3NGQUNOLDhEQUFDQTtzRkFBTTlDLE1BQU1rRixjQUFjLENBQUMxRSxPQUFPLENBQUMsS0FBSzs7Ozs7O3dFQUN4Q1IsTUFBTW1GLGVBQWUsa0JBQ3BCOzs4RkFDRSw4REFBQ3JDOzhGQUFLOzs7Ozs7OEZBQ04sOERBQUNBOzt3RkFBSzt3RkFBVzlDLE1BQU1tRixlQUFlOzs7Ozs7Ozs7Ozs7Ozs7OEVBSTVDLDhEQUFDNUM7b0VBQUluQixXQUFVOztzRkFDYiw4REFBQ29COzRFQUNDQyxTQUFTLElBQU0xQyxnQkFBZ0JDOzRFQUMvQm9CLFdBQVU7OzhGQUVWLDhEQUFDekgsa0xBQUdBO29GQUFDeUgsV0FBVTs7Ozs7O2dGQUFpQjs7Ozs7Ozt3RUFHakNwQixNQUFNaUIsTUFBTSxLQUFLLGVBQWVqQixNQUFNaUIsTUFBTSxLQUFLLGVBQWVqQixNQUFNaUIsTUFBTSxLQUFLLDRCQUNoRiw4REFBQ21FOzRFQUNDQyxPQUFPckYsTUFBTWlCLE1BQU07NEVBQ25Cd0MsVUFBVSxDQUFDbkMsSUFBTXJCLHdCQUF3QkQsTUFBTXJDLEVBQUUsRUFBRTJELEVBQUVFLE1BQU0sQ0FBQzZELEtBQUs7NEVBQ2pFakUsV0FBVTs7Z0ZBRVRwQixNQUFNaUIsTUFBTSxLQUFLLG9DQUNoQjs7c0dBQ0UsOERBQUNxRTs0RkFBT0QsT0FBTTtzR0FBbUI7Ozs7OztzR0FDakMsOERBQUNDOzRGQUFPRCxPQUFNO3NHQUFVOzs7Ozs7c0dBQ3hCLDhEQUFDQzs0RkFBT0QsT0FBTTtzR0FBWTs7Ozs7Ozs7Z0ZBRzdCckYsTUFBTWlCLE1BQU0sS0FBSywyQkFDaEIsOERBQUNxRTtvRkFBT0QsT0FBTTs4RkFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttREF0RDFCckYsTUFBTXJDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQW9FM0JsQixjQUFjLFlBQVlyQixpQkFBZ0JFLGlCQUFBQSwyQkFBQUEsS0FBTXFDLEVBQUUsbUJBQ2pELDhEQUFDekMsNkVBQWNBO29DQUNicUssUUFBUW5LLGFBQWF1QyxFQUFFO29DQUN2QjZILFFBQVFsSyxLQUFLcUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVU1QmYsb0JBQW9CdkIsK0JBQ25CLDhEQUFDa0g7Z0JBQUluQixXQUFVOzBCQUNiLDRFQUFDbUI7b0JBQUluQixXQUFVOztzQ0FDYiw4REFBQ21COzRCQUFJbkIsV0FBVTs7OENBQ2IsOERBQUNtQjs7c0RBQ0MsOERBQUNLOzRDQUFHeEIsV0FBVTs7Z0RBQW1DO2dEQUN2Qy9GLGNBQWNxSixZQUFZOzs7Ozs7O3NEQUVwQyw4REFBQy9COzRDQUFFdkIsV0FBVTs7aURBQ1YvRix1QkFBQUEsY0FBY3NKLEtBQUssY0FBbkJ0SiwyQ0FBQUEscUJBQXFCdUosU0FBUztnREFBQztnREFBSSxJQUFJaEIsS0FBS3ZJLGNBQWN3SSxVQUFVLEVBQUVDLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs4Q0FHN0YsOERBQUN0QjtvQ0FDQ0MsU0FBUyxJQUFNNUYsb0JBQW9CO29DQUNuQ3VFLFdBQVU7OENBRVYsNEVBQUNuSCxrTEFBT0E7d0NBQUNtSCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJdkIsOERBQUNtQjs0QkFBSW5CLFdBQVU7OzhDQUNiLDhEQUFDbUI7b0NBQUluQixXQUFVOztzREFFYiw4REFBQ21COzRDQUFJbkIsV0FBVTs7OERBQ2IsOERBQUM0QjtvREFBRzVCLFdBQVU7OERBQXNDOzs7Ozs7OERBQ3BELDhEQUFDbUI7b0RBQUluQixXQUFVOztzRUFDYiw4REFBQ21COzREQUFJbkIsV0FBVTs7OEVBQ2IsOERBQUMwQjtvRUFBSzFCLFdBQVU7OEVBQWdCOzs7Ozs7OEVBQ2hDLDhEQUFDMEI7b0VBQUsxQixXQUFXLDhDQUF3RixPQUExQ0osb0JBQW9CM0YsY0FBYzRGLE1BQU07OEVBQ3BHNUYsY0FBYzRGLE1BQU0sQ0FBQ1QsT0FBTyxDQUFDLEtBQUssS0FBS3NFLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUsxSixjQUFjNEYsTUFBTSxDQUFDVCxPQUFPLENBQUMsS0FBSyxLQUFLd0UsS0FBSyxDQUFDOzs7Ozs7Ozs7Ozs7c0VBR25ILDhEQUFDekM7NERBQUluQixXQUFVOzs4RUFDYiw4REFBQzBCO29FQUFLMUIsV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUMwQjs4RUFBTXpILGNBQWM2SixjQUFjLENBQUMxRSxPQUFPLENBQUMsS0FBSzs7Ozs7Ozs7Ozs7O3NFQUVuRCw4REFBQytCOzREQUFJbkIsV0FBVTs7OEVBQ2IsOERBQUMwQjtvRUFBSzFCLFdBQVU7OEVBQWdCOzs7Ozs7OEVBQ2hDLDhEQUFDMEI7b0VBQUsxQixXQUFVOzhFQUFpQnJHLDJEQUFjQSxDQUFDTSxjQUFjd0osWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU1oRiw4REFBQ3RDOzRDQUFJbkIsV0FBVTs7OERBQ2IsOERBQUM0QjtvREFBRzVCLFdBQVU7OERBQXNDOzs7Ozs7OERBQ3BELDhEQUFDbUI7b0RBQUluQixXQUFVOztzRUFDYiw4REFBQ21COzs4RUFDQyw4REFBQ087b0VBQUsxQixXQUFVOzhFQUFnQjs7Ozs7OzhFQUNoQyw4REFBQzBCO29FQUFLMUIsV0FBVTs4RUFBUS9GLGNBQWNvSyxnQkFBZ0IsQ0FBQzVDLElBQUk7Ozs7Ozs7Ozs7OztzRUFFN0QsOERBQUNOOzs4RUFDQyw4REFBQ087b0VBQUsxQixXQUFVOzhFQUFnQjs7Ozs7OzhFQUNoQyw4REFBQzBCO29FQUFLMUIsV0FBVTs4RUFBUS9GLGNBQWNvSyxnQkFBZ0IsQ0FBQ0MsS0FBSzs7Ozs7Ozs7Ozs7O3NFQUU5RCw4REFBQ25EOzs4RUFDQyw4REFBQ087b0VBQUsxQixXQUFVOzhFQUFnQjs7Ozs7OzhFQUNoQyw4REFBQ21CO29FQUFJbkIsV0FBVTs7c0ZBQ2IsOERBQUN1QjtzRkFBR3RILGNBQWNvSyxnQkFBZ0IsQ0FBQ0UsT0FBTzs7Ozs7O3NGQUMxQyw4REFBQ2hEOztnRkFBR3RILGNBQWNvSyxnQkFBZ0IsQ0FBQ0csSUFBSTtnRkFBQztnRkFBR3ZLLGNBQWNvSyxnQkFBZ0IsQ0FBQ0ksUUFBUTs7Ozs7Ozt3RUFDakZ4SyxjQUFjb0ssZ0JBQWdCLENBQUNLLFdBQVcsa0JBQ3pDLDhEQUFDbkQ7c0ZBQUd0SCxjQUFjb0ssZ0JBQWdCLENBQUNLLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FTMUQsOERBQUN2RDtvQ0FBSW5CLFdBQVU7O3NEQUNiLDhEQUFDNEI7NENBQUc1QixXQUFVO3NEQUEyQzs7Ozs7O3NEQUN6RCw4REFBQ21COzRDQUFJbkIsV0FBVTt1REFDWi9GLDZCQUFBQSxjQUFjNEosV0FBVyxjQUF6QjVKLGlEQUFBQSwyQkFBMkI2RyxHQUFHLENBQUMsQ0FBQzZELHFCQUMvQiw4REFBQ3hEO29EQUFrQm5CLFdBQVU7O3NFQUMzQiw4REFBQ21COzREQUFJbkIsV0FBVTs7Ozs7O3NFQUNmLDhEQUFDbUI7NERBQUluQixXQUFVOzs4RUFDYiw4REFBQzZDO29FQUFHN0MsV0FBVTs4RUFBNkIyRSxLQUFLQyxhQUFhOzs7Ozs7OEVBQzdELDhEQUFDckQ7b0VBQUV2QixXQUFVOzt3RUFBd0I7d0VBQzdCMkUsS0FBS0UsUUFBUTt3RUFBQzt3RUFBSWxMLDJEQUFjQSxDQUFDZ0wsS0FBS0csVUFBVTs7Ozs7Ozs7Ozs7OztzRUFHMUQsOERBQUMzRDs0REFBSW5CLFdBQVU7c0VBQ2IsNEVBQUN1QjtnRUFBRXZCLFdBQVU7MEVBQ1ZyRywyREFBY0EsQ0FBQ2dMLEtBQUtJLFdBQVc7Ozs7Ozs7Ozs7OzttREFWNUJKLEtBQUtwSSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2dDQW1CdEJ0QyxjQUFjK0ssV0FBVyxrQkFDeEIsOERBQUM3RDtvQ0FBSW5CLFdBQVU7O3NEQUNiLDhEQUFDNEI7NENBQUc1QixXQUFVO3NEQUEyQzs7Ozs7O3NEQUN6RCw4REFBQ3VCOzRDQUFFdkIsV0FBVTtzREFDVi9GLGNBQWMrSyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVV2Q3RKLHFCQUFxQkUsNkJBQ3BCLDhEQUFDL0IseUVBQWFBO2dCQUNab0wsUUFBUXZKO2dCQUNSd0osU0FBUztvQkFDUHZKLHFCQUFxQjtvQkFDckJFLGVBQWU7Z0JBQ2pCO2dCQUNBc0osVUFBVTVGO2dCQUNWcEYsU0FBUzJCOzs7Ozs7Ozs7Ozs7QUFLbkI7R0E5bEN3Qi9COztRQUNpQlgsMERBQU9BO1FBQ3BCQyxrRUFBV0E7UUFDZkMsNERBQVFBOzs7S0FIUlMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9kYXNoYm9hcmQvc2hvcC9wYWdlLnRzeD9jN2YzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFN0b3JlLFxuICBQbHVzLFxuICBFZGl0LFxuICBUcmFzaDIsXG4gIEV5ZSxcbiAgUGFja2FnZSxcbiAgU3RhcixcbiAgVXNlcnMsXG4gIFRyZW5kaW5nVXAsXG4gIENsb2NrLFxuICBDaGVja0NpcmNsZSxcbiAgWENpcmNsZSxcbiAgQWxlcnRDaXJjbGUsXG4gIENhbWVyYSxcbiAgVXBsb2FkLFxuICBJbWFnZSBhcyBJbWFnZUljb25cbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IERhc2hib2FyZExheW91dCBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL0Rhc2hib2FyZExheW91dCdcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHRzL0F1dGhDb250ZXh0J1xuaW1wb3J0IHsgdXNlS1lDQ2hlY2sgfSBmcm9tICdAL2hvb2tzL3VzZUtZQ1ZlcmlmaWNhdGlvbidcbmltcG9ydCB7IHVzZUFsZXJ0IH0gZnJvbSAnQC9jb250ZXh0cy9BbGVydENvbnRleHQnXG5pbXBvcnQgeyBWZW5kb3JTaG9wU2VydmljZSB9IGZyb20gJ0AvbGliL3NlcnZpY2VzL3ZlbmRvclNob3BzJ1xuaW1wb3J0IHsgU2hvcFByb2R1Y3RTZXJ2aWNlIH0gZnJvbSAnQC9saWIvc2VydmljZXMvc2hvcFByb2R1Y3RzJ1xuaW1wb3J0IHsgT3JkZXJTZXJ2aWNlIH0gZnJvbSAnQC9saWIvc2VydmljZXMvb3JkZXJzJ1xuaW1wb3J0IHsgU3RvcmFnZVNlcnZpY2UgfSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9zdG9yYWdlJ1xuaW1wb3J0IHsgVmVuZG9yU2hvcCwgU2hvcFByb2R1Y3QsIFNob3BPcmRlciB9IGZyb20gJ0AvdHlwZXMnXG5pbXBvcnQgeyBmb3JtYXRDdXJyZW5jeSB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuaW1wb3J0IHsgc2hvd0FsZXJ0LCBzaG93Q29uZmlybWF0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0NvbmZpcm1hdGlvbkRpYWxvZydcbmltcG9ydCBUcmFja2luZ01vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9vcmRlcnMvVHJhY2tpbmdNb2RhbCdcbmltcG9ydCBNZXJjaGFudFdhbGxldCBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL01lcmNoYW50V2FsbGV0J1xuXG5pbnRlcmZhY2UgU2hvcFN0YXRzIHtcbiAgdG90YWxQcm9kdWN0czogbnVtYmVyXG4gIHRvdGFsVmlld3M6IG51bWJlclxuICB0b3RhbEZvbGxvd2VyczogbnVtYmVyXG4gIHRvdGFsU2FsZXM6IG51bWJlclxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNeVNob3BQYWdlKCkge1xuICBjb25zdCB7IHVzZXIsIGxvYWRpbmc6IGF1dGhMb2FkaW5nIH0gPSB1c2VBdXRoKClcbiAgY29uc3QgeyBpc0tZQ1ZlcmlmaWVkIH0gPSB1c2VLWUNDaGVjaygpXG4gIGNvbnN0IHsgc2hvd0FsZXJ0IH0gPSB1c2VBbGVydCgpXG4gIGNvbnN0IFtzaG9wcywgc2V0U2hvcHNdID0gdXNlU3RhdGU8VmVuZG9yU2hvcFtdPihbXSlcbiAgY29uc3QgW3NlbGVjdGVkU2hvcCwgc2V0U2VsZWN0ZWRTaG9wXSA9IHVzZVN0YXRlPFZlbmRvclNob3AgfCBudWxsPihudWxsKVxuICBjb25zdCBbc2hvcFByb2R1Y3RzLCBzZXRTaG9wUHJvZHVjdHNdID0gdXNlU3RhdGU8U2hvcFByb2R1Y3RbXT4oW10pXG4gIGNvbnN0IFtzaG9wT3JkZXJzLCBzZXRTaG9wT3JkZXJzXSA9IHVzZVN0YXRlPFNob3BPcmRlcltdPihbXSlcbiAgY29uc3QgW3Nob3BTdGF0cywgc2V0U2hvcFN0YXRzXSA9IHVzZVN0YXRlPFNob3BTdGF0cz4oe1xuICAgIHRvdGFsUHJvZHVjdHM6IDAsXG4gICAgdG90YWxWaWV3czogMCxcbiAgICB0b3RhbEZvbGxvd2VyczogMCxcbiAgICB0b3RhbFNhbGVzOiAwXG4gIH0pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTwnb3ZlcnZpZXcnIHwgJ3Byb2R1Y3RzJyB8ICdvcmRlcnMnIHwgJ3dhbGxldCc+KCdvdmVydmlldycpXG4gIGNvbnN0IFtzZWxlY3RlZE9yZGVyLCBzZXRTZWxlY3RlZE9yZGVyXSA9IHVzZVN0YXRlPFNob3BPcmRlciB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzaG93T3JkZXJEZXRhaWxzLCBzZXRTaG93T3JkZXJEZXRhaWxzXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd1RyYWNraW5nTW9kYWwsIHNldFNob3dUcmFja2luZ01vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbb3JkZXJUb1NoaXAsIHNldE9yZGVyVG9TaGlwXSA9IHVzZVN0YXRlPFNob3BPcmRlciB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFt0cmFja2luZ0xvYWRpbmcsIHNldFRyYWNraW5nTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbdXBsb2FkaW5nQmFubmVyLCBzZXRVcGxvYWRpbmdCYW5uZXJdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFt1cGxvYWRpbmdMb2dvLCBzZXRVcGxvYWRpbmdMb2dvXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIGNvbnN0IGZldGNoVXNlclNob3BzID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlcj8uaWQpIHtcbiAgICAgIHNldEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJylcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKVxuICAgICAgY29uc3QgdXNlclNob3BzID0gYXdhaXQgVmVuZG9yU2hvcFNlcnZpY2UuZ2V0VXNlclNob3BzKHVzZXIuaWQpXG4gICAgICBzZXRTaG9wcyh1c2VyU2hvcHMpXG5cbiAgICAgIGlmICh1c2VyU2hvcHMubGVuZ3RoID4gMCkge1xuICAgICAgICBzZXRTZWxlY3RlZFNob3AodXNlclNob3BzWzBdKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCBzaG9wcycpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZmV0Y2hTaG9wUHJvZHVjdHMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZFNob3A/LmlkKSB7XG4gICAgICBzZXRTaG9wUHJvZHVjdHMoW10pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBwcm9kdWN0cyB9ID0gYXdhaXQgVmVuZG9yU2hvcFNlcnZpY2UuZ2V0U2hvcFByb2R1Y3RzKHNlbGVjdGVkU2hvcC5pZCwgMSwgMjApXG4gICAgICBzZXRTaG9wUHJvZHVjdHMocHJvZHVjdHMpXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzaG9wIHByb2R1Y3RzOicsIGVycilcbiAgICAgIC8vIERvbid0IHRocm93IGVycm9yLCBqdXN0IGxvZyBpdCBhbmQgY29udGludWUgd2l0aCBlbXB0eSBwcm9kdWN0c1xuICAgICAgc2V0U2hvcFByb2R1Y3RzKFtdKVxuICAgIH1cbiAgfSwgW3NlbGVjdGVkU2hvcD8uaWRdKVxuXG4gIGNvbnN0IGZldGNoU2hvcE9yZGVycyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkU2hvcD8uaWQgfHwgIXVzZXI/LmlkKSB7XG4gICAgICBzZXRTaG9wT3JkZXJzKFtdKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgb3JkZXJzIH0gPSBhd2FpdCBPcmRlclNlcnZpY2UuZ2V0U2VsbGVyT3JkZXJzKHVzZXIuaWQsIHNlbGVjdGVkU2hvcC5pZCwgMSwgMjApXG4gICAgICBzZXRTaG9wT3JkZXJzKG9yZGVycylcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHNob3Agb3JkZXJzOicsIGVycilcbiAgICAgIC8vIERvbid0IHRocm93IGVycm9yLCBqdXN0IGxvZyBpdCBhbmQgY29udGludWUgd2l0aCBlbXB0eSBvcmRlcnNcbiAgICAgIHNldFNob3BPcmRlcnMoW10pXG4gICAgfVxuICB9LCBbc2VsZWN0ZWRTaG9wPy5pZCwgdXNlcj8uaWRdKVxuXG4gIGNvbnN0IGZldGNoU2hvcFN0YXRzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWRTaG9wPy5pZCkge1xuICAgICAgc2V0U2hvcFN0YXRzKHtcbiAgICAgICAgdG90YWxQcm9kdWN0czogMCxcbiAgICAgICAgdG90YWxWaWV3czogMCxcbiAgICAgICAgdG90YWxGb2xsb3dlcnM6IDAsXG4gICAgICAgIHRvdGFsU2FsZXM6IDBcbiAgICAgIH0pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gUmVmcmVzaCBzaG9wIHN0YXRpc3RpY3MgZnJvbSBkYXRhYmFzZVxuICAgICAgY29uc3QgcmVmcmVzaGVkU2hvcCA9IGF3YWl0IFZlbmRvclNob3BTZXJ2aWNlLnJlZnJlc2hTaG9wU3RhdGlzdGljcyhzZWxlY3RlZFNob3AuaWQpXG5cbiAgICAgIC8vIEdldCBmb2xsb3dlcnMgY291bnQgd2l0aCBlcnJvciBoYW5kbGluZ1xuICAgICAgbGV0IGZvbGxvd2Vyc0NvdW50ID0gMFxuICAgICAgdHJ5IHtcbiAgICAgICAgZm9sbG93ZXJzQ291bnQgPSBhd2FpdCBWZW5kb3JTaG9wU2VydmljZS5nZXRTaG9wRm9sbG93ZXJzQ291bnQoc2VsZWN0ZWRTaG9wLmlkKVxuICAgICAgfSBjYXRjaCAoZm9sbG93ZXJzRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gZ2V0IGZvbGxvd2VycyBjb3VudCwgdXNpbmcgMDonLCBmb2xsb3dlcnNFcnJvcilcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIG9ubHkgdGhlIHN0YXRpc3RpY3Mgc3RhdGVcbiAgICAgIHNldFNob3BTdGF0cyh7XG4gICAgICAgIHRvdGFsUHJvZHVjdHM6IHJlZnJlc2hlZFNob3AudG90YWxfcHJvZHVjdHMgfHwgMCxcbiAgICAgICAgdG90YWxWaWV3czogcmVmcmVzaGVkU2hvcC50b3RhbF92aWV3cyB8fCAwLFxuICAgICAgICB0b3RhbEZvbGxvd2VyczogZm9sbG93ZXJzQ291bnQsXG4gICAgICAgIHRvdGFsU2FsZXM6IHJlZnJlc2hlZFNob3AudG90YWxfc2FsZXMgfHwgMFxuICAgICAgfSlcblxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc2hvcCBzdGF0czonLCBlcnIpXG5cbiAgICAgIC8vIEZhbGxiYWNrIHRvIGV4aXN0aW5nIGRhdGEgaWYgcmVmcmVzaCBmYWlsc1xuICAgICAgbGV0IGZvbGxvd2Vyc0NvdW50ID0gMFxuICAgICAgdHJ5IHtcbiAgICAgICAgZm9sbG93ZXJzQ291bnQgPSBhd2FpdCBWZW5kb3JTaG9wU2VydmljZS5nZXRTaG9wRm9sbG93ZXJzQ291bnQoc2VsZWN0ZWRTaG9wLmlkKVxuICAgICAgfSBjYXRjaCAoZm9sbG93ZXJzRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gZ2V0IGZvbGxvd2VycyBjb3VudCBpbiBmYWxsYmFjaywgdXNpbmcgMDonLCBmb2xsb3dlcnNFcnJvcilcbiAgICAgIH1cblxuICAgICAgc2V0U2hvcFN0YXRzKHtcbiAgICAgICAgdG90YWxQcm9kdWN0czogc2VsZWN0ZWRTaG9wLnRvdGFsX3Byb2R1Y3RzIHx8IDAsXG4gICAgICAgIHRvdGFsVmlld3M6IHNlbGVjdGVkU2hvcC50b3RhbF92aWV3cyB8fCAwLFxuICAgICAgICB0b3RhbEZvbGxvd2VyczogZm9sbG93ZXJzQ291bnQsXG4gICAgICAgIHRvdGFsU2FsZXM6IHNlbGVjdGVkU2hvcC50b3RhbF9zYWxlcyB8fCAwXG4gICAgICB9KVxuICAgIH1cbiAgfSwgW3NlbGVjdGVkU2hvcD8uaWRdKSAvLyBPbmx5IGRlcGVuZCBvbiBzaG9wIElELCBub3QgdGhlIGVudGlyZSBzaG9wIG9iamVjdFxuXG4gIC8vIHVzZUVmZmVjdCBob29rcyAtIHBsYWNlZCBhZnRlciBhbGwgZnVuY3Rpb24gZGVjbGFyYXRpb25zIHRvIGF2b2lkIGhvaXN0aW5nIGlzc3Vlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyKSB7XG4gICAgICBmZXRjaFVzZXJTaG9wcygpXG4gICAgfVxuICB9LCBbdXNlcl0pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRTaG9wKSB7XG4gICAgICBmZXRjaFNob3BQcm9kdWN0cygpXG4gICAgICBmZXRjaFNob3BPcmRlcnMoKVxuICAgICAgZmV0Y2hTaG9wU3RhdHMoKVxuICAgIH1cbiAgfSwgW3NlbGVjdGVkU2hvcD8uaWQsIGZldGNoU2hvcFByb2R1Y3RzLCBmZXRjaFNob3BPcmRlcnMsIGZldGNoU2hvcFN0YXRzXSkgLy8gT25seSB0cmlnZ2VyIHdoZW4gc2hvcCBJRCBjaGFuZ2VzXG5cbiAgY29uc3QgaGFuZGxlVmlld1Byb2R1Y3QgPSAocHJvZHVjdElkOiBzdHJpbmcpID0+IHtcbiAgICB3aW5kb3cub3BlbihgL3Byb2R1Y3QvJHtwcm9kdWN0SWR9YCwgJ19ibGFuaycpXG4gIH1cblxuICBjb25zdCBoYW5kbGVFZGl0UHJvZHVjdCA9IChwcm9kdWN0SWQ6IHN0cmluZykgPT4ge1xuICAgIC8vIFRPRE86IENyZWF0ZSBlZGl0IHByb2R1Y3QgcGFnZVxuICAgIHdpbmRvdy5vcGVuKGAvZGFzaGJvYXJkL3Nob3AvcHJvZHVjdHMvZWRpdC8ke3Byb2R1Y3RJZH1gLCAnX2JsYW5rJylcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZVByb2R1Y3QgPSBhc3luYyAocHJvZHVjdElkOiBzdHJpbmcsIHByb2R1Y3RUaXRsZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgY29uZmlybWVkID0gYXdhaXQgc2hvd0NvbmZpcm1hdGlvbih7XG4gICAgICB0aXRsZTogJ0RlbGV0ZSBQcm9kdWN0JyxcbiAgICAgIG1lc3NhZ2U6IGBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIFwiJHtwcm9kdWN0VGl0bGV9XCI/IFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUuYCxcbiAgICAgIHZhcmlhbnQ6ICdkYW5nZXInLFxuICAgICAgY29uZmlybVRleHQ6ICdEZWxldGUnXG4gICAgfSlcblxuICAgIGlmIChjb25maXJtZWQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IFNob3BQcm9kdWN0U2VydmljZS5kZWxldGVQcm9kdWN0KHByb2R1Y3RJZClcbiAgICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxuICAgICAgICAgIG1lc3NhZ2U6ICdQcm9kdWN0IGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5IScsXG4gICAgICAgICAgdmFyaWFudDogJ3N1Y2Nlc3MnXG4gICAgICAgIH0pXG4gICAgICAgIC8vIFJlZnJlc2ggdGhlIHByb2R1Y3RzIGxpc3RcbiAgICAgICAgZmV0Y2hTaG9wUHJvZHVjdHMoKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcbiAgICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdGYWlsZWQgdG8gZGVsZXRlIHByb2R1Y3QnLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVmlld09yZGVyID0gKG9yZGVyOiBTaG9wT3JkZXIpID0+IHtcbiAgICBzZXRTZWxlY3RlZE9yZGVyKG9yZGVyKVxuICAgIHNldFNob3dPcmRlckRldGFpbHModHJ1ZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVVwZGF0ZU9yZGVyU3RhdHVzID0gYXN5bmMgKG9yZGVySWQ6IHN0cmluZywgbmV3U3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICAvLyBJZiBtYXJraW5nIGFzIHNoaXBwZWQsIHNob3cgdHJhY2tpbmcgbW9kYWxcbiAgICBpZiAobmV3U3RhdHVzID09PSAnc2hpcHBlZCcpIHtcbiAgICAgIGNvbnN0IG9yZGVyID0gc2hvcE9yZGVycy5maW5kKG8gPT4gby5pZCA9PT0gb3JkZXJJZClcbiAgICAgIGlmIChvcmRlcikge1xuICAgICAgICBzZXRPcmRlclRvU2hpcChvcmRlcilcbiAgICAgICAgc2V0U2hvd1RyYWNraW5nTW9kYWwodHJ1ZSlcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gU3BlY2lhbCBoYW5kbGluZyBmb3IgY2FuY2VsbGF0aW9uXG4gICAgaWYgKG5ld1N0YXR1cyA9PT0gJ2NhbmNlbGxlZCcpIHtcbiAgICAgIGNvbnN0IGNvbmZpcm1lZCA9IGF3YWl0IHNob3dDb25maXJtYXRpb24oe1xuICAgICAgICB0aXRsZTogJ0NhbmNlbCBPcmRlcicsXG4gICAgICAgIG1lc3NhZ2U6ICdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gY2FuY2VsIHRoaXMgb3JkZXI/IFRoZSBwYXltZW50IHdpbGwgYmUgcmVmdW5kZWQgdG8gdGhlIGJ1eWVyXFwncyB3YWxsZXQuJyxcbiAgICAgICAgY29uZmlybVRleHQ6ICdDYW5jZWwgT3JkZXInLFxuICAgICAgICBjYW5jZWxUZXh0OiAnS2VlcCBPcmRlcicsXG4gICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICB9KVxuXG4gICAgICBpZiAoIWNvbmZpcm1lZCkgcmV0dXJuXG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IE9yZGVyU2VydmljZS5jYW5jZWxPcmRlcldpdGhSZWZ1bmQob3JkZXJJZCwgdXNlcj8uaWQgfHwgJycpXG4gICAgICAgIGF3YWl0IHNob3dBbGVydCh7XG4gICAgICAgICAgdGl0bGU6ICdTdWNjZXNzJyxcbiAgICAgICAgICBtZXNzYWdlOiAnT3JkZXIgY2FuY2VsbGVkIGFuZCByZWZ1bmQgcHJvY2Vzc2VkIHN1Y2Nlc3NmdWxseSEnLFxuICAgICAgICAgIHZhcmlhbnQ6ICdzdWNjZXNzJ1xuICAgICAgICB9KVxuICAgICAgICBmZXRjaFNob3BPcmRlcnMoKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcbiAgICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdGYWlsZWQgdG8gY2FuY2VsIG9yZGVyJyxcbiAgICAgICAgICB2YXJpYW50OiAnZGFuZ2VyJ1xuICAgICAgICB9KVxuICAgICAgfVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgY29uc3QgY29uZmlybWVkID0gYXdhaXQgc2hvd0NvbmZpcm1hdGlvbih7XG4gICAgICB0aXRsZTogJ1VwZGF0ZSBPcmRlciBTdGF0dXMnLFxuICAgICAgbWVzc2FnZTogYEFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byB1cGRhdGUgdGhlIG9yZGVyIHN0YXR1cyB0byBcIiR7bmV3U3RhdHVzLnJlcGxhY2UoJ18nLCAnICcpfVwiP2AsXG4gICAgICBjb25maXJtVGV4dDogJ1VwZGF0ZScsXG4gICAgICBjYW5jZWxUZXh0OiAnQ2FuY2VsJyxcbiAgICAgIHZhcmlhbnQ6ICd3YXJuaW5nJ1xuICAgIH0pXG5cbiAgICBpZiAoIWNvbmZpcm1lZCkgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgYXdhaXQgT3JkZXJTZXJ2aWNlLnVwZGF0ZU9yZGVyU3RhdHVzKG9yZGVySWQsIG5ld1N0YXR1cywgdW5kZWZpbmVkLCB1bmRlZmluZWQsIHVuZGVmaW5lZCwgdXNlcj8uaWQpXG4gICAgICBhd2FpdCBzaG93QWxlcnQoe1xuICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxuICAgICAgICBtZXNzYWdlOiAnT3JkZXIgc3RhdHVzIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5IScsXG4gICAgICAgIHZhcmlhbnQ6ICdzdWNjZXNzJ1xuICAgICAgfSlcbiAgICAgIGZldGNoU2hvcE9yZGVycygpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGF3YWl0IHNob3dBbGVydCh7XG4gICAgICAgIHRpdGxlOiAnRXJyb3InLFxuICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdGYWlsZWQgdG8gdXBkYXRlIG9yZGVyIHN0YXR1cycsXG4gICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUFkZFRyYWNraW5nID0gYXN5bmMgKHRyYWNraW5nRGF0YToge1xuICAgIHRyYWNraW5nTnVtYmVyOiBzdHJpbmdcbiAgICBjb3VyaWVyU2VydmljZTogc3RyaW5nXG4gICAgdHJhY2tpbmdVcmw/OiBzdHJpbmdcbiAgfSkgPT4ge1xuICAgIGlmICghb3JkZXJUb1NoaXApIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIHNldFRyYWNraW5nTG9hZGluZyh0cnVlKVxuXG4gICAgICBhd2FpdCBPcmRlclNlcnZpY2UudXBkYXRlT3JkZXJTdGF0dXMoXG4gICAgICAgIG9yZGVyVG9TaGlwLmlkLFxuICAgICAgICAnc2hpcHBlZCcsXG4gICAgICAgIHRyYWNraW5nRGF0YS50cmFja2luZ051bWJlcixcbiAgICAgICAgdHJhY2tpbmdEYXRhLnRyYWNraW5nVXJsLFxuICAgICAgICBgU2hpcHBlZCB2aWEgJHt0cmFja2luZ0RhdGEuY291cmllclNlcnZpY2V9YCxcbiAgICAgICAgdXNlcj8uaWQsXG4gICAgICAgIHRyYWNraW5nRGF0YS5jb3VyaWVyU2VydmljZVxuICAgICAgKVxuXG4gICAgICBhd2FpdCBzaG93QWxlcnQoe1xuICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxuICAgICAgICBtZXNzYWdlOiAnT3JkZXIgbWFya2VkIGFzIHNoaXBwZWQgd2l0aCB0cmFja2luZyBpbmZvcm1hdGlvbiEnLFxuICAgICAgICB2YXJpYW50OiAnc3VjY2VzcydcbiAgICAgIH0pXG5cbiAgICAgIHNldFNob3dUcmFja2luZ01vZGFsKGZhbHNlKVxuICAgICAgc2V0T3JkZXJUb1NoaXAobnVsbClcbiAgICAgIGZldGNoU2hvcE9yZGVycygpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGF3YWl0IHNob3dBbGVydCh7XG4gICAgICAgIHRpdGxlOiAnRXJyb3InLFxuICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdGYWlsZWQgdG8gdXBkYXRlIG9yZGVyIHN0YXR1cycsXG4gICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRUcmFja2luZ0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0T3JkZXJTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdwZW5kaW5nJzpcbiAgICAgICAgcmV0dXJuICd0ZXh0LXllbGxvdy02MDAgYmcteWVsbG93LTEwMCdcbiAgICAgIGNhc2UgJ3BlbmRpbmdfc2hpcG1lbnQnOlxuICAgICAgICByZXR1cm4gJ3RleHQtb3JhbmdlLTYwMCBiZy1vcmFuZ2UtMTAwJ1xuICAgICAgY2FzZSAnY29uZmlybWVkJzpcbiAgICAgICAgcmV0dXJuICd0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtMTAwJ1xuICAgICAgY2FzZSAncHJvY2Vzc2luZyc6XG4gICAgICAgIHJldHVybiAndGV4dC1wdXJwbGUtNjAwIGJnLXB1cnBsZS0xMDAnXG4gICAgICBjYXNlICdzaGlwcGVkJzpcbiAgICAgICAgcmV0dXJuICd0ZXh0LWluZGlnby02MDAgYmctaW5kaWdvLTEwMCdcbiAgICAgIGNhc2UgJ2RlbGl2ZXJlZCc6XG4gICAgICAgIHJldHVybiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tMTAwJ1xuICAgICAgY2FzZSAnY2FuY2VsbGVkJzpcbiAgICAgICAgcmV0dXJuICd0ZXh0LXJlZC02MDAgYmctcmVkLTEwMCdcbiAgICAgIGNhc2UgJ3JlZnVuZGVkJzpcbiAgICAgICAgcmV0dXJuICd0ZXh0LWdyYXktNjAwIGJnLWdyYXktMTAwJ1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICd0ZXh0LWdyYXktNjAwIGJnLWdyYXktMTAwJ1xuICAgIH1cbiAgfVxuXG5cblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdhcHByb3ZlZCc6XG4gICAgICAgIHJldHVybiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tMTAwJ1xuICAgICAgY2FzZSAncGVuZGluZyc6XG4gICAgICAgIHJldHVybiAndGV4dC15ZWxsb3ctNjAwIGJnLXllbGxvdy0xMDAnXG4gICAgICBjYXNlICdyZWplY3RlZCc6XG4gICAgICAgIHJldHVybiAndGV4dC1yZWQtNjAwIGJnLXJlZC0xMDAnXG4gICAgICBjYXNlICdzdXNwZW5kZWQnOlxuICAgICAgICByZXR1cm4gJ3RleHQtZ3JheS02MDAgYmctZ3JheS0xMDAnXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ3RleHQtZ3JheS02MDAgYmctZ3JheS0xMDAnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzSWNvbiA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdhcHByb3ZlZCc6XG4gICAgICAgIHJldHVybiA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICBjYXNlICdwZW5kaW5nJzpcbiAgICAgICAgcmV0dXJuIDxDbG9jayBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgIGNhc2UgJ3JlamVjdGVkJzpcbiAgICAgICAgcmV0dXJuIDxYQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgY2FzZSAnc3VzcGVuZGVkJzpcbiAgICAgICAgcmV0dXJuIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiA8Q2xvY2sgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQmFubmVyVXBsb2FkID0gYXN5bmMgKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGUudGFyZ2V0LmZpbGVzPy5bMF1cbiAgICBpZiAoIWZpbGUgfHwgIXNlbGVjdGVkU2hvcCB8fCAhdXNlcj8uaWQpIHJldHVyblxuXG4gICAgLy8gVmFsaWRhdGUgZmlsZSB0eXBlIGFuZCBzaXplXG4gICAgaWYgKCFmaWxlLnR5cGUuc3RhcnRzV2l0aCgnaW1hZ2UvJykpIHtcbiAgICAgIGF3YWl0IHNob3dBbGVydCh7XG4gICAgICAgIHRpdGxlOiAnSW52YWxpZCBGaWxlJyxcbiAgICAgICAgbWVzc2FnZTogJ1BsZWFzZSBzZWxlY3QgYW4gaW1hZ2UgZmlsZS4nLFxuICAgICAgICB2YXJpYW50OiAnZGFuZ2VyJ1xuICAgICAgfSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmIChmaWxlLnNpemUgPiA1ICogMTAyNCAqIDEwMjQpIHsgLy8gNU1CIGxpbWl0XG4gICAgICBhd2FpdCBzaG93QWxlcnQoe1xuICAgICAgICB0aXRsZTogJ0ZpbGUgVG9vIExhcmdlJyxcbiAgICAgICAgbWVzc2FnZTogJ0ltYWdlIHNpemUgbXVzdCBiZSBsZXNzIHRoYW4gNU1CLicsXG4gICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldFVwbG9hZGluZ0Jhbm5lcih0cnVlKVxuICAgICAgY29uc3QgYmFubmVyVXJsID0gYXdhaXQgU3RvcmFnZVNlcnZpY2UudXBsb2FkSW1hZ2UoZmlsZSwgdXNlci5pZClcblxuICAgICAgYXdhaXQgVmVuZG9yU2hvcFNlcnZpY2UudXBkYXRlU2hvcChzZWxlY3RlZFNob3AuaWQsIHsgYmFubmVyX3VybDogYmFubmVyVXJsIH0pXG5cbiAgICAgIC8vIFVwZGF0ZSB0aGUgc2VsZWN0ZWQgc2hvcCB3aXRoIG5ldyBiYW5uZXJcbiAgICAgIHNldFNlbGVjdGVkU2hvcChwcmV2ID0+IHByZXYgPyB7IC4uLnByZXYsIGJhbm5lcl91cmw6IGJhbm5lclVybCB9IDogbnVsbClcblxuICAgICAgLy8gVXBkYXRlIHNob3BzIGxpc3RcbiAgICAgIHNldFNob3BzKHByZXYgPT4gcHJldi5tYXAoc2hvcCA9PlxuICAgICAgICBzaG9wLmlkID09PSBzZWxlY3RlZFNob3AuaWQgPyB7IC4uLnNob3AsIGJhbm5lcl91cmw6IGJhbm5lclVybCB9IDogc2hvcFxuICAgICAgKSlcblxuICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgdGl0bGU6ICdTdWNjZXNzJyxcbiAgICAgICAgbWVzc2FnZTogJ1Nob3AgYmFubmVyIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5IScsXG4gICAgICAgIHZhcmlhbnQ6ICdzdWNjZXNzJ1xuICAgICAgfSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0ZhaWxlZCB0byB1cGxvYWQgYmFubmVyJyxcbiAgICAgICAgdmFyaWFudDogJ2RhbmdlcidcbiAgICAgIH0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFVwbG9hZGluZ0Jhbm5lcihmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVMb2dvVXBsb2FkID0gYXN5bmMgKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGUudGFyZ2V0LmZpbGVzPy5bMF1cbiAgICBpZiAoIWZpbGUgfHwgIXNlbGVjdGVkU2hvcCB8fCAhdXNlcj8uaWQpIHJldHVyblxuXG4gICAgLy8gVmFsaWRhdGUgZmlsZSB0eXBlIGFuZCBzaXplXG4gICAgaWYgKCFmaWxlLnR5cGUuc3RhcnRzV2l0aCgnaW1hZ2UvJykpIHtcbiAgICAgIGF3YWl0IHNob3dBbGVydCh7XG4gICAgICAgIHRpdGxlOiAnSW52YWxpZCBGaWxlJyxcbiAgICAgICAgbWVzc2FnZTogJ1BsZWFzZSBzZWxlY3QgYW4gaW1hZ2UgZmlsZS4nLFxuICAgICAgICB2YXJpYW50OiAnZGFuZ2VyJ1xuICAgICAgfSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmIChmaWxlLnNpemUgPiAyICogMTAyNCAqIDEwMjQpIHsgLy8gMk1CIGxpbWl0IGZvciBsb2dvXG4gICAgICBhd2FpdCBzaG93QWxlcnQoe1xuICAgICAgICB0aXRsZTogJ0ZpbGUgVG9vIExhcmdlJyxcbiAgICAgICAgbWVzc2FnZTogJ0xvZ28gc2l6ZSBtdXN0IGJlIGxlc3MgdGhhbiAyTUIuJyxcbiAgICAgICAgdmFyaWFudDogJ2RhbmdlcidcbiAgICAgIH0pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0VXBsb2FkaW5nTG9nbyh0cnVlKVxuICAgICAgY29uc3QgbG9nb1VybCA9IGF3YWl0IFN0b3JhZ2VTZXJ2aWNlLnVwbG9hZEltYWdlKGZpbGUsIHVzZXIuaWQpXG5cbiAgICAgIGF3YWl0IFZlbmRvclNob3BTZXJ2aWNlLnVwZGF0ZVNob3Aoc2VsZWN0ZWRTaG9wLmlkLCB7IGxvZ29fdXJsOiBsb2dvVXJsIH0pXG5cbiAgICAgIC8vIFVwZGF0ZSB0aGUgc2VsZWN0ZWQgc2hvcCB3aXRoIG5ldyBsb2dvXG4gICAgICBzZXRTZWxlY3RlZFNob3AocHJldiA9PiBwcmV2ID8geyAuLi5wcmV2LCBsb2dvX3VybDogbG9nb1VybCB9IDogbnVsbClcblxuICAgICAgLy8gVXBkYXRlIHNob3BzIGxpc3RcbiAgICAgIHNldFNob3BzKHByZXYgPT4gcHJldi5tYXAoc2hvcCA9PlxuICAgICAgICBzaG9wLmlkID09PSBzZWxlY3RlZFNob3AuaWQgPyB7IC4uLnNob3AsIGxvZ29fdXJsOiBsb2dvVXJsIH0gOiBzaG9wXG4gICAgICApKVxuXG4gICAgICBhd2FpdCBzaG93QWxlcnQoe1xuICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxuICAgICAgICBtZXNzYWdlOiAnU2hvcCBsb2dvIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5IScsXG4gICAgICAgIHZhcmlhbnQ6ICdzdWNjZXNzJ1xuICAgICAgfSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0ZhaWxlZCB0byB1cGxvYWQgbG9nbycsXG4gICAgICAgIHZhcmlhbnQ6ICdkYW5nZXInXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRVcGxvYWRpbmdMb2dvKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGlmIChhdXRoTG9hZGluZyB8fCBsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItcHJpbWFyeS1ibHVlXCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGlmICghdXNlcikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbWItNFwiPlBsZWFzZSBzaWduIGluIHRvIGFjY2VzcyB5b3VyIHNob3A8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+e2Vycm9yfTwvZGl2PlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoVXNlclNob3BzfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLXByaW1hcnktYmx1ZSB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctcHJpbWFyeS1ibHVlLzkwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBUcnkgQWdhaW5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8RGFzaGJvYXJkTGF5b3V0PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IHNtOml0ZW1zLWNlbnRlciBzbTpqdXN0aWZ5LWJldHdlZW4gZ2FwLTRcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+TXkgU2hvcDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TWFuYWdlIHlvdXIgdmVuZG9yIHNob3AgYW5kIHByb2R1Y3RzPC9wPlxuICAgICAgICAgICAge3NlbGVjdGVkU2hvcCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LWJsdWVcIj57c2VsZWN0ZWRTaG9wLm5hbWV9PC9oMj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7Z2V0U3RhdHVzQ29sb3Ioc2VsZWN0ZWRTaG9wLnN0YXR1cyl9YH0+XG4gICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzSWNvbihzZWxlY3RlZFNob3Auc3RhdHVzKX1cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTEgY2FwaXRhbGl6ZVwiPntzZWxlY3RlZFNob3Auc3RhdHVzfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICB7c2hvcHMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgICAgICB7aXNLWUNWZXJpZmllZCA/IChcbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgaHJlZj1cIi9jcmVhdGUtc2hvcFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgYmctZ3JlZW4tNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmVlbi03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBDcmVhdGUgU2hvcFxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHNob3dBbGVydCh7XG4gICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICdLWUMgVmVyaWZpY2F0aW9uIFJlcXVpcmVkJyxcbiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAnWW91IG11c3QgY29tcGxldGUgS1lDIHZlcmlmaWNhdGlvbiBiZWZvcmUgY3JlYXRpbmcgYSBzaG9wLiBQbGVhc2UgZ28gdG8gUHJvZmlsZSA+IElkZW50aXR5IFZlcmlmaWNhdGlvbiB0byBzdWJtaXQgeW91ciBkb2N1bWVudHMuJyxcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50OiAnd2FybmluZydcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgYmctZ3JheS0zMDAgdGV4dC1ncmF5LTUwMCByb3VuZGVkLWxnIGN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIktZQyB2ZXJpZmljYXRpb24gcmVxdWlyZWRcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBDcmVhdGUgU2hvcFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAge3Nob3BzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAvKiBObyBTaG9wcyBTdGF0ZSAqL1xuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTIgYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPFN0b3JlIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPk5vIHNob3BzIHlldDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTZcIj5DcmVhdGUgeW91ciBmaXJzdCBzaG9wIHRvIHN0YXJ0IHNlbGxpbmcgcHJvZHVjdHM8L3A+XG4gICAgICAgICAgICB7aXNLWUNWZXJpZmllZCA/IChcbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBocmVmPVwiL2NyZWF0ZS1zaG9wXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNiBweS0zIGJnLXByaW1hcnktYmx1ZSB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctcHJpbWFyeS1ibHVlLzkwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgQ3JlYXRlIFlvdXIgRmlyc3QgU2hvcFxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgc2hvd0FsZXJ0KHtcbiAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ0tZQyBWZXJpZmljYXRpb24gUmVxdWlyZWQnLFxuICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdZb3UgbXVzdCBjb21wbGV0ZSBLWUMgdmVyaWZpY2F0aW9uIGJlZm9yZSBjcmVhdGluZyBhIHNob3AuIFBsZWFzZSBnbyB0byBQcm9maWxlID4gSWRlbnRpdHkgVmVyaWZpY2F0aW9uIHRvIHN1Ym1pdCB5b3VyIGRvY3VtZW50cy4nLFxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ6ICd3YXJuaW5nJ1xuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC02IHB5LTMgYmctZ3JheS0zMDAgdGV4dC1ncmF5LTUwMCByb3VuZGVkLWxnIGN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIktZQyB2ZXJpZmljYXRpb24gcmVxdWlyZWRcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBDcmVhdGUgWW91ciBGaXJzdCBTaG9wXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWFtYmVyLTYwMCBiZy1hbWJlci01MCBib3JkZXIgYm9yZGVyLWFtYmVyLTIwMCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgaW5saW5lIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgQ29tcGxldGUgS1lDIHZlcmlmaWNhdGlvbiB0byBjcmVhdGUgeW91ciBzaG9wXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cblxuXG4gICAgICAgICAgICB7c2VsZWN0ZWRTaG9wICYmIChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICB7LyogVGFiIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTggcHgtNlwiIGFyaWEtbGFiZWw9XCJUYWJzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdvdmVydmlldycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktNCBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdvdmVydmlldydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcHJpbWFyeS1ibHVlIHRleHQtcHJpbWFyeS1ibHVlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgT3ZlcnZpZXdcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3Byb2R1Y3RzJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS00IHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ3Byb2R1Y3RzJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wcmltYXJ5LWJsdWUgdGV4dC1wcmltYXJ5LWJsdWUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBQcm9kdWN0cyAoe3Nob3BQcm9kdWN0cy5sZW5ndGh9KVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignb3JkZXJzJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS00IHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ29yZGVycydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcHJpbWFyeS1ibHVlIHRleHQtcHJpbWFyeS1ibHVlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgT3JkZXJzICh7c2hvcE9yZGVycy5sZW5ndGh9KVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignd2FsbGV0Jyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS00IHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ3dhbGxldCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcHJpbWFyeS1ibHVlIHRleHQtcHJpbWFyeS1ibHVlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgTWVyY2hhbnQgV2FsbGV0XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvbmF2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVGFiIENvbnRlbnQgKi99XG4gICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ292ZXJ2aWV3JyAmJiAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICB7LyogU2hvcCBPdmVydmlldyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICB7LyogU2hvcCBCYW5uZXIgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGgtNDggYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnktYmx1ZSB0by1zZWNvbmRhcnktYmx1ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRTaG9wLmJhbm5lcl91cmwgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtzZWxlY3RlZFNob3AuYmFubmVyX3VybH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17YCR7c2VsZWN0ZWRTaG9wLm5hbWV9IGJhbm5lcmB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2VJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTIgb3BhY2l0eS01MFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gb3BhY2l0eS03NVwiPk5vIGJhbm5lciBpbWFnZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBCYW5uZXIgVXBsb2FkIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNCByaWdodC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImZpbGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9XCJpbWFnZS8qXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUJhbm5lclVwbG9hZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaGlkZGVuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwbG9hZGluZ0Jhbm5lcn1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiBiZy1ibGFjay81MCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmxhY2svNzAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3VwbG9hZGluZ0Jhbm5lciA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItMiBib3JkZXItd2hpdGUgYm9yZGVyLXQtdHJhbnNwYXJlbnQgbXItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYW1lcmEgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt1cGxvYWRpbmdCYW5uZXIgPyAnVXBsb2FkaW5nLi4uJyA6ICdDaGFuZ2UgQmFubmVyJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFNob3AgTG9nbyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIGgtMjAgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gYmctZ3JheS0yMDAgYm9yZGVyLTQgYm9yZGVyLXdoaXRlIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFNob3AubG9nb191cmwgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17c2VsZWN0ZWRTaG9wLmxvZ29fdXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e2Ake3NlbGVjdGVkU2hvcC5uYW1lfSBsb2dvYH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTdG9yZSBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIExvZ28gVXBsb2FkIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImFic29sdXRlIC1ib3R0b20tMiAtcmlnaHQtMiBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImZpbGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlTG9nb1VwbG9hZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhpZGRlblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17dXBsb2FkaW5nTG9nb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1wcmltYXJ5LWJsdWUgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaG92ZXI6YmctcHJpbWFyeS1ibHVlLzkwIHRyYW5zaXRpb24tY29sb3JzIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3VwbG9hZGluZ0xvZ28gPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTMgdy0zIGJvcmRlciBib3JkZXItd2hpdGUgYm9yZGVyLXQtdHJhbnNwYXJlbnRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYW1lcmEgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFNob3AubmFtZX08L2gyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTFcIj57c2VsZWN0ZWRTaG9wLmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPkNyZWF0ZWQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoc2VsZWN0ZWRTaG9wLmNyZWF0ZWRfYXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBTaG9wIFN0YXRzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLWJsdWUtMTAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTcwMFwiPlByb2R1Y3RzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtOTAwXCI+e3Nob3BTdGF0cy50b3RhbFByb2R1Y3RzfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MCB0by1ncmVlbi0xMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTcwMFwiPlZpZXdzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTkwMFwiPntzaG9wU3RhdHMudG90YWxWaWV3c308L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTUwIHRvLXB1cnBsZS0xMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcHVycGxlLTcwMFwiPlNhbGVzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS05MDBcIj57c2hvcFN0YXRzLnRvdGFsU2FsZXN9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy01MCB0by15ZWxsb3ctMTAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTdGFyIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC15ZWxsb3ctNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXllbGxvdy03MDBcIj5SYXRpbmc8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQteWVsbG93LTkwMFwiPntzZWxlY3RlZFNob3AucmF0aW5nPy50b0ZpeGVkKDEpIHx8ICcwLjAnfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7LyogUHJvZHVjdHMgVGFiICovfVxuICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdwcm9kdWN0cycgJiYgKFxuICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgey8qIFByb2R1Y3RzIFNlY3Rpb24gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+UHJvZHVjdHM8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtdC0xXCI+TWFuYWdlIHlvdXIgc2hvcCBwcm9kdWN0czwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmQvc2hvcC9wcm9kdWN0cy9hZGRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJnLXByaW1hcnktYmx1ZSB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctcHJpbWFyeS1ibHVlLzkwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgQWRkIFByb2R1Y3RcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHtzaG9wUHJvZHVjdHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPk5vIHByb2R1Y3RzIHlldDwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+U3RhcnQgYWRkaW5nIHByb2R1Y3RzIHRvIHlvdXIgc2hvcCB0byBiZWdpbiBzZWxsaW5nPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9zaG9wL3Byb2R1Y3RzL2FkZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNiBweS0zIGJnLXByaW1hcnktYmx1ZSB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctcHJpbWFyeS1ibHVlLzkwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgQWRkIFlvdXIgRmlyc3QgUHJvZHVjdFxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzaG9wUHJvZHVjdHMubWFwKChwcm9kdWN0KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17cHJvZHVjdC5pZH0gY2xhc3NOYW1lPVwicC02IGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBQcm9kdWN0IEltYWdlICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMCBoLTIwIGJnLWdyYXktMjAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0LmltYWdlcyAmJiBwcm9kdWN0LmltYWdlcy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtwcm9kdWN0LmltYWdlc1swXS5pbWFnZV91cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXtwcm9kdWN0LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBQcm9kdWN0IERldGFpbHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSBsaW5lLWNsYW1wLTIgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3QuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+U0tVOiB7cHJvZHVjdC5za3UgfHwgJ04vQSd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiY2FwaXRhbGl6ZVwiPntwcm9kdWN0LmNvbmRpdGlvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5TdG9jazoge3Byb2R1Y3Quc3RvY2tfcXVhbnRpdHl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Vmlld3M6IHtwcm9kdWN0LnZpZXdzIHx8IDB9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IG1sLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnktYmx1ZSBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3kocHJvZHVjdC5wcmljZSB8fCAwKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9kdWN0LnN0YXR1cyA9PT0gJ2FjdGl2ZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHByb2R1Y3Quc3RhdHVzID09PSAnaW5hY3RpdmUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcHJvZHVjdC5zdGF0dXMgPT09ICdvdXRfb2Zfc3RvY2snXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5zdGF0dXMucmVwbGFjZSgnXycsICcgJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVWaWV3UHJvZHVjdChwcm9kdWN0LmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEuNSB0ZXh0LXNtIGJnLWdyYXktMTAwIHRleHQtZ3JheS03MDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTIwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJWaWV3IFByb2R1Y3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZpZXdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVFZGl0UHJvZHVjdChwcm9kdWN0LmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEuNSB0ZXh0LXNtIGJnLWJsdWUtMTAwIHRleHQtYmx1ZS03MDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTIwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFZGl0IFByb2R1Y3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBFZGl0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlUHJvZHVjdChwcm9kdWN0LmlkLCBwcm9kdWN0LnRpdGxlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEuNSB0ZXh0LXNtIGJnLXJlZC0xMDAgdGV4dC1yZWQtNzAwIHJvdW5kZWQtbGcgaG92ZXI6YmctcmVkLTIwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgUHJvZHVjdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRGVsZXRlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7LyogT3JkZXJzIFRhYiAqL31cbiAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnb3JkZXJzJyAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPk9yZGVyczwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMVwiPk1hbmFnZSB5b3VyIHNob3Agb3JkZXJzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHtzaG9wT3JkZXJzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTE2IHctMTYgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5ObyBvcmRlcnMgeWV0PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5PcmRlcnMgd2lsbCBhcHBlYXIgaGVyZSB3aGVuIGN1c3RvbWVycyBwdXJjaGFzZSB5b3VyIHByb2R1Y3RzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c2hvcE9yZGVycy5tYXAoKG9yZGVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtvcmRlci5pZH0gY2xhc3NOYW1lPVwicC02IGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBPcmRlciAje29yZGVyLm9yZGVyX251bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge29yZGVyLmJ1eWVyPy5mdWxsX25hbWV9IOKAoiB7bmV3IERhdGUob3JkZXIuY3JlYXRlZF9hdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3kob3JkZXIudG90YWxfYW1vdW50KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0T3JkZXJTdGF0dXNDb2xvcihvcmRlci5zdGF0dXMpfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci5zdGF0dXMucmVwbGFjZSgnXycsICcgJykuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBvcmRlci5zdGF0dXMucmVwbGFjZSgnXycsICcgJykuc2xpY2UoMSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57b3JkZXIub3JkZXJfaXRlbXM/Lmxlbmd0aCB8fCAwfSBpdGVtKHMpPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntvcmRlci5wYXltZW50X21ldGhvZC5yZXBsYWNlKCdfJywgJyAnKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci50cmFja2luZ19udW1iZXIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5UcmFja2luZzoge29yZGVyLnRyYWNraW5nX251bWJlcn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVWaWV3T3JkZXIob3JkZXIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMS41IHRleHQtc20gYmctZ3JheS0xMDAgdGV4dC1ncmF5LTcwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBWaWV3XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7b3JkZXIuc3RhdHVzICE9PSAnZGVsaXZlcmVkJyAmJiBvcmRlci5zdGF0dXMgIT09ICdjYW5jZWxsZWQnICYmIG9yZGVyLnN0YXR1cyAhPT0gJ3JlZnVuZGVkJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e29yZGVyLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlVXBkYXRlT3JkZXJTdGF0dXMob3JkZXIuaWQsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHB4LTIgcHktMS41IGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktYmx1ZSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci5zdGF0dXMgPT09ICdwZW5kaW5nX3NoaXBtZW50JyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInBlbmRpbmdfc2hpcG1lbnRcIj5QZW5kaW5nIFNoaXBtZW50PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInNoaXBwZWRcIj5TaGlwIE9yZGVyPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImNhbmNlbGxlZFwiPkNhbmNlbCBPcmRlcjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7b3JkZXIuc3RhdHVzID09PSAnc2hpcHBlZCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwic2hpcHBlZFwiPlNoaXBwZWQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7LyogTWVyY2hhbnQgV2FsbGV0IFRhYiAqL31cbiAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnd2FsbGV0JyAmJiBzZWxlY3RlZFNob3AgJiYgdXNlcj8uaWQgJiYgKFxuICAgICAgICAgICAgICAgICAgPE1lcmNoYW50V2FsbGV0XG4gICAgICAgICAgICAgICAgICAgIHNob3BJZD17c2VsZWN0ZWRTaG9wLmlkfVxuICAgICAgICAgICAgICAgICAgICB1c2VySWQ9e3VzZXIuaWR9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogT3JkZXIgRGV0YWlscyBNb2RhbCAqL31cbiAgICAgIHtzaG93T3JkZXJEZXRhaWxzICYmIHNlbGVjdGVkT3JkZXIgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTQgei01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3cteGwgbWF4LXctNHhsIHctZnVsbCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICBPcmRlciAje3NlbGVjdGVkT3JkZXIub3JkZXJfbnVtYmVyfVxuICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAge3NlbGVjdGVkT3JkZXIuYnV5ZXI/LmZ1bGxfbmFtZX0g4oCiIHtuZXcgRGF0ZShzZWxlY3RlZE9yZGVyLmNyZWF0ZWRfYXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93T3JkZXJEZXRhaWxzKGZhbHNlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFhDaXJjbGUgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IG92ZXJmbG93LXktYXV0byBtYXgtaC1bY2FsYyg5MHZoLTEyMHB4KV1cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgey8qIE9yZGVyIEluZm8gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPk9yZGVyIEluZm9ybWF0aW9uPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5TdGF0dXM6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke2dldE9yZGVyU3RhdHVzQ29sb3Ioc2VsZWN0ZWRPcmRlci5zdGF0dXMpfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkT3JkZXIuc3RhdHVzLnJlcGxhY2UoJ18nLCAnICcpLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgc2VsZWN0ZWRPcmRlci5zdGF0dXMucmVwbGFjZSgnXycsICcgJykuc2xpY2UoMSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5QYXltZW50Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57c2VsZWN0ZWRPcmRlci5wYXltZW50X21ldGhvZC5yZXBsYWNlKCdfJywgJyAnKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlRvdGFsOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e2Zvcm1hdEN1cnJlbmN5KHNlbGVjdGVkT3JkZXIudG90YWxfYW1vdW50KX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogQ3VzdG9tZXIgSW5mbyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+Q3VzdG9tZXIgSW5mb3JtYXRpb248L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5OYW1lOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yXCI+e3NlbGVjdGVkT3JkZXIuc2hpcHBpbmdfYWRkcmVzcy5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlBob25lOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yXCI+e3NlbGVjdGVkT3JkZXIuc2hpcHBpbmdfYWRkcmVzcy5waG9uZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5BZGRyZXNzOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTIgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHA+e3NlbGVjdGVkT3JkZXIuc2hpcHBpbmdfYWRkcmVzcy5hZGRyZXNzfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwPntzZWxlY3RlZE9yZGVyLnNoaXBwaW5nX2FkZHJlc3MuY2l0eX0sIHtzZWxlY3RlZE9yZGVyLnNoaXBwaW5nX2FkZHJlc3MuZGlzdHJpY3R9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkT3JkZXIuc2hpcHBpbmdfYWRkcmVzcy5wb3N0YWxfY29kZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwPntzZWxlY3RlZE9yZGVyLnNoaXBwaW5nX2FkZHJlc3MucG9zdGFsX2NvZGV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIE9yZGVyIEl0ZW1zICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPk9yZGVyIEl0ZW1zPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAge3NlbGVjdGVkT3JkZXIub3JkZXJfaXRlbXM/Lm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aXRlbS5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHAtMyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1ncmF5LTIwMCByb3VuZGVkLWxnIGZsZXgtc2hyaW5rLTBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57aXRlbS5wcm9kdWN0X3RpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUXR5OiB7aXRlbS5xdWFudGl0eX0gw5cge2Zvcm1hdEN1cnJlbmN5KGl0ZW0udW5pdF9wcmljZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShpdGVtLnRvdGFsX3ByaWNlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIE9yZGVyIE5vdGVzICovfVxuICAgICAgICAgICAgICB7c2VsZWN0ZWRPcmRlci5idXllcl9ub3RlcyAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPkN1c3RvbWVyIE5vdGVzPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZE9yZGVyLmJ1eWVyX25vdGVzfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIFRyYWNraW5nIE1vZGFsICovfVxuICAgICAge3Nob3dUcmFja2luZ01vZGFsICYmIG9yZGVyVG9TaGlwICYmIChcbiAgICAgICAgPFRyYWNraW5nTW9kYWxcbiAgICAgICAgICBpc09wZW49e3Nob3dUcmFja2luZ01vZGFsfVxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHtcbiAgICAgICAgICAgIHNldFNob3dUcmFja2luZ01vZGFsKGZhbHNlKVxuICAgICAgICAgICAgc2V0T3JkZXJUb1NoaXAobnVsbClcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uU3VibWl0PXtoYW5kbGVBZGRUcmFja2luZ31cbiAgICAgICAgICBsb2FkaW5nPXt0cmFja2luZ0xvYWRpbmd9XG4gICAgICAgIC8+XG4gICAgICApfVxuICAgIDwvRGFzaGJvYXJkTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwiU3RvcmUiLCJQbHVzIiwiRWRpdCIsIlRyYXNoMiIsIkV5ZSIsIlBhY2thZ2UiLCJTdGFyIiwiVHJlbmRpbmdVcCIsIkNsb2NrIiwiQ2hlY2tDaXJjbGUiLCJYQ2lyY2xlIiwiQWxlcnRDaXJjbGUiLCJDYW1lcmEiLCJJbWFnZSIsIkltYWdlSWNvbiIsIkxpbmsiLCJEYXNoYm9hcmRMYXlvdXQiLCJ1c2VBdXRoIiwidXNlS1lDQ2hlY2siLCJ1c2VBbGVydCIsIlZlbmRvclNob3BTZXJ2aWNlIiwiU2hvcFByb2R1Y3RTZXJ2aWNlIiwiT3JkZXJTZXJ2aWNlIiwiU3RvcmFnZVNlcnZpY2UiLCJmb3JtYXRDdXJyZW5jeSIsInNob3dDb25maXJtYXRpb24iLCJUcmFja2luZ01vZGFsIiwiTWVyY2hhbnRXYWxsZXQiLCJNeVNob3BQYWdlIiwic2VsZWN0ZWRTaG9wIiwic2VsZWN0ZWRPcmRlciIsInVzZXIiLCJsb2FkaW5nIiwiYXV0aExvYWRpbmciLCJpc0tZQ1ZlcmlmaWVkIiwic2hvd0FsZXJ0Iiwic2hvcHMiLCJzZXRTaG9wcyIsInNldFNlbGVjdGVkU2hvcCIsInNob3BQcm9kdWN0cyIsInNldFNob3BQcm9kdWN0cyIsInNob3BPcmRlcnMiLCJzZXRTaG9wT3JkZXJzIiwic2hvcFN0YXRzIiwic2V0U2hvcFN0YXRzIiwidG90YWxQcm9kdWN0cyIsInRvdGFsVmlld3MiLCJ0b3RhbEZvbGxvd2VycyIsInRvdGFsU2FsZXMiLCJzZXRMb2FkaW5nIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwic2V0U2VsZWN0ZWRPcmRlciIsInNob3dPcmRlckRldGFpbHMiLCJzZXRTaG93T3JkZXJEZXRhaWxzIiwic2hvd1RyYWNraW5nTW9kYWwiLCJzZXRTaG93VHJhY2tpbmdNb2RhbCIsIm9yZGVyVG9TaGlwIiwic2V0T3JkZXJUb1NoaXAiLCJ0cmFja2luZ0xvYWRpbmciLCJzZXRUcmFja2luZ0xvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwidXBsb2FkaW5nQmFubmVyIiwic2V0VXBsb2FkaW5nQmFubmVyIiwidXBsb2FkaW5nTG9nbyIsInNldFVwbG9hZGluZ0xvZ28iLCJmZXRjaFVzZXJTaG9wcyIsImlkIiwidXNlclNob3BzIiwiZ2V0VXNlclNob3BzIiwibGVuZ3RoIiwiZXJyIiwiRXJyb3IiLCJtZXNzYWdlIiwiZmV0Y2hTaG9wUHJvZHVjdHMiLCJwcm9kdWN0cyIsImdldFNob3BQcm9kdWN0cyIsImNvbnNvbGUiLCJmZXRjaFNob3BPcmRlcnMiLCJvcmRlcnMiLCJnZXRTZWxsZXJPcmRlcnMiLCJmZXRjaFNob3BTdGF0cyIsInJlZnJlc2hlZFNob3AiLCJyZWZyZXNoU2hvcFN0YXRpc3RpY3MiLCJmb2xsb3dlcnNDb3VudCIsImdldFNob3BGb2xsb3dlcnNDb3VudCIsImZvbGxvd2Vyc0Vycm9yIiwid2FybiIsInRvdGFsX3Byb2R1Y3RzIiwidG90YWxfdmlld3MiLCJ0b3RhbF9zYWxlcyIsImhhbmRsZVZpZXdQcm9kdWN0IiwicHJvZHVjdElkIiwid2luZG93Iiwib3BlbiIsImhhbmRsZUVkaXRQcm9kdWN0IiwiaGFuZGxlRGVsZXRlUHJvZHVjdCIsInByb2R1Y3RUaXRsZSIsImNvbmZpcm1lZCIsInRpdGxlIiwidmFyaWFudCIsImNvbmZpcm1UZXh0IiwiZGVsZXRlUHJvZHVjdCIsImhhbmRsZVZpZXdPcmRlciIsIm9yZGVyIiwiaGFuZGxlVXBkYXRlT3JkZXJTdGF0dXMiLCJvcmRlcklkIiwibmV3U3RhdHVzIiwiZmluZCIsIm8iLCJjYW5jZWxUZXh0IiwiY2FuY2VsT3JkZXJXaXRoUmVmdW5kIiwicmVwbGFjZSIsInVwZGF0ZU9yZGVyU3RhdHVzIiwidW5kZWZpbmVkIiwiaGFuZGxlQWRkVHJhY2tpbmciLCJ0cmFja2luZ0RhdGEiLCJ0cmFja2luZ051bWJlciIsInRyYWNraW5nVXJsIiwiY291cmllclNlcnZpY2UiLCJnZXRPcmRlclN0YXR1c0NvbG9yIiwic3RhdHVzIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRTdGF0dXNJY29uIiwiY2xhc3NOYW1lIiwiaGFuZGxlQmFubmVyVXBsb2FkIiwiZSIsImZpbGUiLCJ0YXJnZXQiLCJmaWxlcyIsInR5cGUiLCJzdGFydHNXaXRoIiwic2l6ZSIsImJhbm5lclVybCIsInVwbG9hZEltYWdlIiwidXBkYXRlU2hvcCIsImJhbm5lcl91cmwiLCJwcmV2IiwibWFwIiwic2hvcCIsImhhbmRsZUxvZ29VcGxvYWQiLCJsb2dvVXJsIiwibG9nb191cmwiLCJkaXYiLCJidXR0b24iLCJvbkNsaWNrIiwiaDEiLCJwIiwiaDIiLCJuYW1lIiwic3BhbiIsImhyZWYiLCJoMyIsIm5hdiIsImFyaWEtbGFiZWwiLCJpbWciLCJzcmMiLCJhbHQiLCJsYWJlbCIsImlucHV0IiwiYWNjZXB0Iiwib25DaGFuZ2UiLCJkaXNhYmxlZCIsImRlc2NyaXB0aW9uIiwiRGF0ZSIsImNyZWF0ZWRfYXQiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJyYXRpbmciLCJ0b0ZpeGVkIiwiaDQiLCJwcm9kdWN0IiwiaW1hZ2VzIiwiaW1hZ2VfdXJsIiwic2t1IiwiY29uZGl0aW9uIiwic3RvY2tfcXVhbnRpdHkiLCJ2aWV3cyIsInByaWNlIiwib3JkZXJfbnVtYmVyIiwiYnV5ZXIiLCJmdWxsX25hbWUiLCJ0b3RhbF9hbW91bnQiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsInNsaWNlIiwib3JkZXJfaXRlbXMiLCJwYXltZW50X21ldGhvZCIsInRyYWNraW5nX251bWJlciIsInNlbGVjdCIsInZhbHVlIiwib3B0aW9uIiwic2hvcElkIiwidXNlcklkIiwic2hpcHBpbmdfYWRkcmVzcyIsInBob25lIiwiYWRkcmVzcyIsImNpdHkiLCJkaXN0cmljdCIsInBvc3RhbF9jb2RlIiwiaXRlbSIsInByb2R1Y3RfdGl0bGUiLCJxdWFudGl0eSIsInVuaXRfcHJpY2UiLCJ0b3RhbF9wcmljZSIsImJ1eWVyX25vdGVzIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU3VibWl0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/shop/page.tsx\n"));

/***/ })

});