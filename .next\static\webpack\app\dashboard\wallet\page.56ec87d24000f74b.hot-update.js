"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/wallet/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/wallet/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WalletPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Download,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_wallet_CommissionBreakdownCards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/wallet/CommissionBreakdownCards */ \"(app-pages-browser)/./src/components/wallet/CommissionBreakdownCards.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_services_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/storage */ \"(app-pages-browser)/./src/lib/services/storage.ts\");\n/* harmony import */ var _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/commissionSystem */ \"(app-pages-browser)/./src/lib/services/commissionSystem.ts\");\n/* harmony import */ var _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useKYCVerification */ \"(app-pages-browser)/./src/hooks/useKYCVerification.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/AlertContext */ \"(app-pages-browser)/./src/contexts/AlertContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { showAlert } = (0,_contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__.useAlert)();\n    const { requireKYCForAction, isKYCVerified } = (0,_hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__.useKYCCheck)();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transfers, setTransfers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [depositRequests, setDepositRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [withdrawalRequests, setWithdrawalRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalBalance: 0,\n        totalDeposits: 0,\n        transfersSent: 0,\n        transfersReceived: 0,\n        totalWithdrawals: 0,\n        pendingDeposits: 0\n    });\n    const [commissionBreakdown, setCommissionBreakdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        directCommission: 0,\n        levelCommission: 0,\n        rsmBonus: 0,\n        zmBonus: 0,\n        okdoiHeadCommission: 0,\n        totalCommissions: 0\n    });\n    const [extendedCommissionData, setExtendedCommissionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        directCommission: 0,\n        levelCommission: 0,\n        voucherCommission: 0,\n        festivalBonus: 0,\n        savingCommission: 0,\n        giftCenterCommission: 0,\n        entertainmentCommission: 0,\n        medicalCommission: 0,\n        educationCommission: 0,\n        creditCommission: 0,\n        // ZM specific\n        zmBonuses: 0,\n        petralAllowanceZM: 0,\n        leasingFacilityZM: 0,\n        phoneBillZM: 0,\n        // RSM specific\n        rsmBonuses: 0,\n        petralAllowanceRSM: 0,\n        leasingFacilityRSM: 0,\n        phoneBillRSM: 0,\n        // OKDOI Head specific\n        okdoiHeadCommission: 0,\n        totalCommissions: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_transactions\");\n    // Modal states\n    const [showTransferModal, setShowTransferModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDepositModal, setShowDepositModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWithdrawalModal, setShowWithdrawalModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Transfer form\n    const [transferForm, setTransferForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        receiverEmail: \"\",\n        amount: \"\",\n        description: \"\"\n    });\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Deposit form\n    const [depositForm, setDepositForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        depositor_name: \"\",\n        notes: \"\",\n        terms_accepted: false\n    });\n    const [depositProof, setDepositProof] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [depositLoading, setDepositLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Withdrawal form\n    const [withdrawalForm, setWithdrawalForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        bank_name: \"\",\n        account_number: \"\",\n        account_holder_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        branch: \"\",\n        notes: \"\"\n    });\n    const [withdrawalLoading, setWithdrawalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchWalletData();\n        }\n    }, [\n        user\n    ]);\n    const fetchWalletData = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            setError(null);\n            console.log(\"Fetching wallet data for user:\", user.id);\n            // Fetch wallet with error handling\n            const walletData = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserWallet(user.id);\n            setWallet(walletData);\n            console.log(\"Wallet data fetched:\", walletData);\n            // Fetch recent transactions with error handling\n            const { transactions: transactionsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getWalletTransactions(user.id, 1, 10);\n            setTransactions(transactionsData || []);\n            console.log(\"Transactions fetched:\", (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.length) || 0);\n            // Fetch recent transfers with error handling\n            const { transfers: transfersData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserTransfers(user.id, 1, 10);\n            setTransfers(transfersData || []);\n            console.log(\"Transfers fetched:\", (transfersData === null || transfersData === void 0 ? void 0 : transfersData.length) || 0);\n            // Fetch deposit requests with error handling\n            const { requests: depositsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserDepositRequests(user.id, 1, 10);\n            setDepositRequests(depositsData || []);\n            console.log(\"Deposit requests fetched:\", (depositsData === null || depositsData === void 0 ? void 0 : depositsData.length) || 0);\n            // Fetch withdrawal requests with error handling\n            const { requests: withdrawalsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.getUserWithdrawalRequests(user.id, 1, 10);\n            setWithdrawalRequests(withdrawalsData || []);\n            console.log(\"Withdrawal requests fetched:\", (withdrawalsData === null || withdrawalsData === void 0 ? void 0 : withdrawalsData.length) || 0);\n            // Fetch commission breakdown\n            try {\n                const commissionData = await _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__.CommissionSystemService.getCommissionBreakdown(user.id);\n                setCommissionBreakdown(commissionData);\n                // Get real extended commission data from the database\n                const extendedData = await _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__.CommissionSystemService.getExtendedCommissionBreakdown(user.id);\n                setExtendedCommissionData(extendedData);\n                console.log(\"Commission breakdown fetched:\", commissionData);\n            } catch (commissionError) {\n                console.error(\"Error fetching commission breakdown:\", commissionError);\n            // Don't fail the entire load if commission data fails\n            }\n            // Calculate stats with null safety\n            const totalDeposits = (transactionsData || []).filter((t)=>t.transaction_type === \"deposit\" && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const transfersSent = (transfersData || []).filter((t)=>t.sender_id === user.id && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const transfersReceived = (transfersData || []).filter((t)=>t.receiver_id === user.id && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const totalWithdrawals = (transactionsData || []).filter((t)=>t.transaction_type === \"withdrawal\" && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const pendingDeposits = (depositsData || []).filter((d)=>d.status === \"pending\").reduce((sum, d)=>sum + (d.amount || 0), 0);\n            setStats({\n                totalBalance: (walletData === null || walletData === void 0 ? void 0 : walletData.balance) || 0,\n                totalDeposits,\n                transfersSent,\n                transfersReceived,\n                totalWithdrawals,\n                pendingDeposits\n            });\n            console.log(\"Wallet data fetch completed successfully\");\n        } catch (err) {\n            console.error(\"Error fetching wallet data:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load wallet data\");\n            // Reset data on error\n            setWallet(null);\n            setTransactions([]);\n            setTransfers([]);\n            setDepositRequests([]);\n            setWithdrawalRequests([]);\n            setStats({\n                totalBalance: 0,\n                totalDeposits: 0,\n                transfersSent: 0,\n                transfersReceived: 0,\n                totalWithdrawals: 0,\n                pendingDeposits: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTransfer = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Check KYC verification status\n        const canTransfer = await requireKYCForAction(\"p2p_transfer\");\n        if (!canTransfer) {\n            await showAlert({\n                title: \"KYC Verification Required\",\n                message: \"You must complete KYC verification before making P2P transfers. Please go to Profile > Identity Verification to submit your documents.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setTransferLoading(true);\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createP2PTransfer(user.id, transferForm.receiverEmail, parseFloat(transferForm.amount), transferForm.description || undefined);\n            setShowTransferModal(false);\n            setTransferForm({\n                receiverEmail: \"\",\n                amount: \"\",\n                description: \"\"\n            });\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Transfer completed successfully!\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Transfer failed\",\n                variant: \"danger\"\n            });\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    const handleDeposit = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Validation\n        if (!depositForm.terms_accepted) {\n            await showAlert({\n                title: \"Terms Required\",\n                message: \"Please accept the terms and conditions to proceed.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        if (!depositProof) {\n            await showAlert({\n                title: \"Proof Required\",\n                message: \"Please upload a deposit receipt or proof of payment.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setDepositLoading(true);\n            // Upload proof file first\n            let proofUrl = \"\";\n            if (depositProof) {\n                proofUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_6__.StorageService.uploadImage(depositProof, user.id);\n            }\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createDepositRequest(user.id, {\n                amount: parseFloat(depositForm.amount),\n                depositor_name: depositForm.depositor_name,\n                notes: depositForm.notes || undefined,\n                deposit_slip_url: proofUrl\n            });\n            setShowDepositModal(false);\n            setDepositForm({\n                amount: \"\",\n                depositor_name: \"\",\n                notes: \"\",\n                terms_accepted: false\n            });\n            setDepositProof(null);\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Deposit request submitted successfully! We will review it shortly.\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Failed to submit deposit request\",\n                variant: \"danger\"\n            });\n        } finally{\n            setDepositLoading(false);\n        }\n    };\n    const handleWithdrawal = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Check KYC verification status\n        const canWithdraw = await requireKYCForAction(\"wallet_withdrawal\");\n        if (!canWithdraw) {\n            await showAlert({\n                title: \"KYC Verification Required\",\n                message: \"You must complete KYC verification before making withdrawals. Please go to Profile > Identity Verification to submit your documents.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setWithdrawalLoading(true);\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_5__.WalletService.createWithdrawalRequest(user.id, {\n                amount: parseFloat(withdrawalForm.amount),\n                bank_name: withdrawalForm.bank_name,\n                account_number: withdrawalForm.account_number,\n                account_holder_name: withdrawalForm.account_holder_name,\n                branch: withdrawalForm.branch || undefined,\n                notes: withdrawalForm.notes || undefined\n            });\n            setShowWithdrawalModal(false);\n            setWithdrawalForm({\n                amount: \"\",\n                bank_name: \"\",\n                account_number: \"\",\n                account_holder_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n                branch: \"\",\n                notes: \"\"\n            });\n            await fetchWalletData();\n            await showAlert({\n                title: \"Success\",\n                message: \"Withdrawal request submitted successfully! We will process it shortly.\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Failed to submit withdrawal request\",\n                variant: \"danger\"\n            });\n        } finally{\n            setWithdrawalLoading(false);\n        }\n    };\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 16\n                }, this);\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 16\n                }, this);\n            case \"commission\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTransactionColor = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n            case \"commission\":\n                return \"text-green-600\";\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return \"text-red-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 456,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 455,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Error Loading Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 466,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 465,\n            columnNumber: 7\n        }, this);\n    }\n    if (!wallet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Wallet Not Found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Your wallet is being set up. Please try again in a moment.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 485,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 484,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"My Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage your funds and transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowDepositModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Funds\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTransferModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Send Money\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowWithdrawalModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Withdraw\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-blue to-secondary-blue rounded-xl p-8 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-100 mb-2\",\n                                            children: \"Available Balance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.totalDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Transfers Sent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.transfersSent)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Transfers Received\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.transfersReceived)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-6 w-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Withdrawals\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.totalWithdrawals)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Pending Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(stats.pendingDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_CommissionBreakdownCards__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        data: extendedCommissionData,\n                        userType: user.user_type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 612,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                {\n                                    id: \"all_transactions\",\n                                    name: \"All Transactions\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"]\n                                },\n                                {\n                                    id: \"fund_transfers\",\n                                    name: \"Fund Transfers\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                                },\n                                {\n                                    id: \"deposits\",\n                                    name: \"Deposits\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                                },\n                                {\n                                    id: \"withdrawals\",\n                                    name: \"Withdrawals\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n                                },\n                                {\n                                    id: \"commissions\",\n                                    name: \"Commissions\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        tab.name\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            activeTab === \"all_transactions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"All Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.map((transaction)=>{\n                                                var _transaction_p2p_transfer_receiver, _transaction_p2p_transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                                                                            children: transaction.transaction_type.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 665,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.reference_type === \"p2p_transfer\" && transaction.p2p_transfer ? transaction.transaction_type === \"transfer_out\" ? \"Transfer to \".concat(((_transaction_p2p_transfer_receiver = transaction.p2p_transfer.receiver) === null || _transaction_p2p_transfer_receiver === void 0 ? void 0 : _transaction_p2p_transfer_receiver.full_name) || \"Unknown\") : \"Transfer from \".concat(((_transaction_p2p_transfer_sender = transaction.p2p_transfer.sender) === null || _transaction_p2p_transfer_sender === void 0 ? void 0 : _transaction_p2p_transfer_sender.full_name) || \"Unknown\") : transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transaction.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transaction.reference_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transaction.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium mr-2 \".concat(getTransactionColor(transaction.transaction_type)),\n                                                                            children: [\n                                                                                transaction.transaction_type.includes(\"in\") || transaction.transaction_type === \"deposit\" || transaction.transaction_type === \"refund\" ? \"+\" : \"-\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.amount)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(transaction.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Balance: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.balance_after)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transactions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transactions found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"fund_transfers\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"P2P Transfers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transfers.map((transfer)=>{\n                                                var _transfer_receiver, _transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                    children: transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: [\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"Sent to\" : \"Received from\",\n                                                                                \" \",\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? (_transfer_receiver = transfer.receiver) === null || _transfer_receiver === void 0 ? void 0 : _transfer_receiver.full_name : (_transfer_sender = transfer.sender) === null || _transfer_sender === void 0 ? void 0 : _transfer_sender.full_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transfer.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transfer.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transfer.reference_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 733,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transfer.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 737,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium mr-2 \".concat(transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"text-red-600\" : \"text-green-600\"),\n                                                                        children: [\n                                                                            transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"-\" : \"+\",\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transfer.amount)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    getStatusIcon(transfer.status)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transfer.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transfers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transfers found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"deposits\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            depositRequests.map((deposit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 773,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                                    children: [\n                                                                                        \"Bank Deposit - \",\n                                                                                        deposit.bank_name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 776,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        deposit.account_holder_name,\n                                                                                        \" (\",\n                                                                                        deposit.account_number,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 779,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 771,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-lg font-bold text-gray-900\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(deposit.amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                getStatusIcon(deposit.status),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-1 text-xs font-medium capitalize \".concat(deposit.status === \"approved\" ? \"text-green-600\" : deposit.status === \"rejected\" ? \"text-red-600\" : \"text-yellow-600\"),\n                                                                                    children: deposit.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 790,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 788,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        new Date(deposit.created_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                deposit.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-mono\",\n                                                                    children: [\n                                                                        \"Ref: \",\n                                                                        deposit.reference_number\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.transaction_reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Bank Reference: \",\n                                                                        deposit.transaction_reference\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Notes: \",\n                                                                        deposit.notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.admin_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Admin Notes: \",\n                                                                        deposit.admin_notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 811,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, deposit.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            depositRequests.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No deposit requests found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 765,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"withdrawals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Withdrawal Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            withdrawalRequests.map((withdrawal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: [\n                                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(withdrawal.amount),\n                                                                                    \" to \",\n                                                                                    withdrawal.bank_name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 834,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Account: \",\n                                                                                    withdrawal.account_number,\n                                                                                    \" (\",\n                                                                                    withdrawal.account_holder_name,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 837,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            withdrawal.branch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Branch: \",\n                                                                                    withdrawal.branch\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            withdrawal.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400 font-mono\",\n                                                                                children: [\n                                                                                    \"Ref: \",\n                                                                                    withdrawal.reference_number\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 844,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            withdrawal.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Notes: \",\n                                                                                    withdrawal.notes\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(withdrawal.status === \"processed\" ? \"bg-green-100 text-green-800\" : withdrawal.status === \"approved\" ? \"bg-blue-100 text-blue-800\" : withdrawal.status === \"rejected\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                            children: withdrawal.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 853,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 831,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1 ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        new Date(withdrawal.created_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                withdrawal.approved_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Approved: \",\n                                                                        new Date(withdrawal.approved_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                withdrawal.processed_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Processed: \",\n                                                                        new Date(withdrawal.processed_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                withdrawal.admin_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Admin Notes: \",\n                                                                        withdrawal.admin_notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, withdrawal.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            withdrawalRequests.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No withdrawal requests found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 879,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"commissions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Commission Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.filter((t)=>t.transaction_type === \"commission\").map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: \"Commission Earned\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 896,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 899,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transaction.reference_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                transaction.reference_id.slice(-8)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 903,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transaction.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 907,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium mr-2 text-green-600\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.amount)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 914,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(transaction.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 913,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Balance: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(transaction.balance_after)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 919,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            transactions.filter((t)=>t.transaction_type === \"commission\").length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Download_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"No commission transactions found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Commissions will appear here when you earn them from referrals or other activities.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this),\n            showTransferModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Send Money\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleTransfer,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Receiver Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 945,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            required: true,\n                                            value: transferForm.receiverEmail,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        receiverEmail: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Enter receiver's email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            max: (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0,\n                                            step: \"0.01\",\n                                            value: transferForm.amount,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Available: \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 972,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Description (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: transferForm.description,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"What's this for?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 976,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowTransferModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: transferLoading,\n                                            className: \"flex-1 px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 disabled:opacity-50\",\n                                            children: transferLoading ? \"Sending...\" : \"Send Money\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 988,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 943,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 941,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 940,\n                columnNumber: 9\n            }, this),\n            showDepositModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Request Cash Deposit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1013,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleDeposit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            step: \"0.01\",\n                                            value: depositForm.amount,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1015,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Depositor Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: depositForm.depositor_name,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        depositor_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Name of person who made the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Deposit Receipt/Proof *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            required: true,\n                                            accept: \"image/*,.pdf\",\n                                            onChange: (e)=>{\n                                                var _e_target_files;\n                                                const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                if (file) {\n                                                    // Validate file size (5MB max)\n                                                    if (file.size > 5 * 1024 * 1024) {\n                                                        showAlert({\n                                                            title: \"File Too Large\",\n                                                            message: \"Please select a file smaller than 5MB.\",\n                                                            variant: \"warning\"\n                                                        });\n                                                        e.target.value = \"\";\n                                                        return;\n                                                    }\n                                                    setDepositProof(file);\n                                                }\n                                            },\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Upload bank deposit slip, receipt, or proof of payment (Max 5MB)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1074,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: depositForm.notes,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            rows: 3,\n                                            placeholder: \"Additional information about the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1077,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1073,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"terms\",\n                                            required: true,\n                                            checked: depositForm.terms_accepted,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        terms_accepted: e.target.checked\n                                                    })),\n                                            className: \"mt-1 h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"terms\",\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    target: \"_blank\",\n                                                    className: \"text-primary-blue hover:underline\",\n                                                    children: \"Terms and Conditions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"and confirm that the deposit information provided is accurate. *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1094,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowDepositModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1103,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: depositLoading,\n                                            className: \"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                            children: depositLoading ? \"Submitting...\" : \"Submit Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1014,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 1012,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 1011,\n                columnNumber: 9\n            }, this),\n            showWithdrawalModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Request Withdrawal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1127,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleWithdrawal,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            min: \"1\",\n                                            max: (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0,\n                                            required: true,\n                                            value: withdrawalForm.amount,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter amount\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Available balance: \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1144,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1129,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Bank Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1150,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.bank_name,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        bank_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter bank name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1153,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Account Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.account_number,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        account_number: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter account number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Account Holder Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: withdrawalForm.account_holder_name,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        account_holder_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter account holder name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Branch (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: withdrawalForm.branch,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        branch: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            placeholder: \"Enter branch name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1195,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: withdrawalForm.notes,\n                                            onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            rows: 3,\n                                            placeholder: \"Any additional notes...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowWithdrawalModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1218,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: withdrawalLoading,\n                                            className: \"flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50\",\n                                            children: withdrawalLoading ? \"Submitting...\" : \"Submit Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 1225,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 1217,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 1128,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 1126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 1125,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n        lineNumber: 502,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"AtW9q3+eKyNQGhLzYmuBoYBSO6M=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_AlertContext__WEBPACK_IMPORTED_MODULE_11__.useAlert,\n        _hooks_useKYCVerification__WEBPACK_IMPORTED_MODULE_8__.useKYCCheck\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/wallet/page.tsx\n"));

/***/ })

});