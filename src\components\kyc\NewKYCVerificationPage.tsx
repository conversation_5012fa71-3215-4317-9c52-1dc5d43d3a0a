'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Shield,
  Upload,
  AlertCircle,
  CheckCircle,
  Loader2,
  FileText,
  Calendar,
  MapPin,
  User,
  CreditCard,
  Camera,
  X,
  Eye
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { NewKYCService } from '@/lib/services/newKyc'

// Types based on database schema
interface KYCFormData {
  id_document_type: 'national_id' | 'passport' | 'driving_license'
  id_document_number: string
  full_name: string
  date_of_birth: string
  address: string
  submission_notes: string
}

interface KYCDocuments {
  id_front?: File
  id_back?: File
  selfie?: File
  address_proof?: File
}

interface DocumentRequirements {
  id: string
  name: string
  description: string
  requires_back_photo: boolean
  requires_front_photo: boolean
  requires_selfie: boolean
  requires_address_proof: boolean
}

interface KYCStatus {
  kyc_status: string
  kyc_submitted_at?: string
  kyc_approved_at?: string
}

interface KYCSubmission {
  id: string
  status: string
  rejection_reason?: string
  created_at: string
}

export default function NewKYCVerificationPage() {
  const { user } = useAuth()
  
  // Stable refs to prevent re-renders
  const formRef = useRef<HTMLFormElement>(null)
  const initialFormData = useRef<KYCFormData>({
    id_document_type: 'national_id',
    id_document_number: '',
    full_name: user?.full_name || '',
    date_of_birth: '',
    address: '',
    submission_notes: ''
  })

  // State management with stable initial values
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  // KYC data
  const [kycStatus, setKycStatus] = useState<KYCStatus | null>(null)
  const [kycSubmission, setKycSubmission] = useState<KYCSubmission | null>(null)
  const [documentTypes, setDocumentTypes] = useState<DocumentRequirements[]>([])
  
  // Form data - using object reference to prevent re-renders
  const [formData, setFormData] = useState<KYCFormData>(initialFormData.current)
  const [documents, setDocuments] = useState<KYCDocuments>({})
  const [documentErrors, setDocumentErrors] = useState<Record<string, string>>({})
  const [documentPreviews, setDocumentPreviews] = useState<Record<string, string>>({})

  // Get current document requirements
  const currentDocType = documentTypes.find(dt => dt.id === formData.id_document_type)

  // Load initial data
  useEffect(() => {
    if (!user) return

    const loadKYCData = async () => {
      try {
        setLoading(true)
        
        // Load all data in parallel
        const [status, submission, docTypes] = await Promise.all([
          NewKYCService.getUserKYCStatus(user.id),
          NewKYCService.getUserKYCSubmission(user.id),
          NewKYCService.getKYCDocumentTypes()
        ])

        setKycStatus(status)
        setKycSubmission(submission)
        setDocumentTypes(docTypes.map(dt => ({
          ...dt,
          requires_back_photo: dt.id !== 'passport',
          requires_front_photo: true,
          requires_selfie: true,
          requires_address_proof: true
        })))

        // Pre-fill form if submission exists
        if (submission) {
          const newFormData = {
            id_document_type: submission.id_document_type,
            id_document_number: submission.id_document_number || '',
            full_name: submission.full_name,
            date_of_birth: submission.date_of_birth || '',
            address: submission.address,
            submission_notes: submission.submission_notes || ''
          }
          setFormData(newFormData)
          initialFormData.current = newFormData
        }
      } catch (err) {
        console.error('Error loading KYC data:', err)
        setError('Failed to load KYC information')
      } finally {
        setLoading(false)
      }
    }

    loadKYCData()
  }, [user])

  // Stable input handlers
  const handleInputChange = (field: keyof KYCFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const value = e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    setError('')
  }

  // Document upload handler
  const handleDocumentUpload = (documentType: keyof KYCDocuments) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file
    const maxSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf']

    if (file.size > maxSize) {
      setDocumentErrors(prev => ({ ...prev, [documentType]: 'File size must be less than 10MB' }))
      return
    }

    if (!allowedTypes.includes(file.type)) {
      setDocumentErrors(prev => ({ ...prev, [documentType]: 'Please upload a valid image or PDF file' }))
      return
    }

    // Clear error and set file
    setDocumentErrors(prev => ({ ...prev, [documentType]: '' }))
    setDocuments(prev => ({ ...prev, [documentType]: file }))

    // Create preview for images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setDocumentPreviews(prev => ({ ...prev, [documentType]: e.target?.result as string }))
      }
      reader.readAsDataURL(file)
    } else {
      setDocumentPreviews(prev => ({ ...prev, [documentType]: '' }))
    }
  }

  // Remove document
  const removeDocument = (documentType: keyof KYCDocuments) => () => {
    setDocuments(prev => {
      const newDocs = { ...prev }
      delete newDocs[documentType]
      return newDocs
    })
    setDocumentPreviews(prev => {
      const newPreviews = { ...prev }
      delete newPreviews[documentType]
      return newPreviews
    })
    setDocumentErrors(prev => ({ ...prev, [documentType]: '' }))
  }

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}
    let isValid = true

    // Validate required fields
    if (!formData.full_name.trim()) {
      setError('Full name is required')
      isValid = false
    }

    if (!formData.address.trim()) {
      setError('Address is required')
      isValid = false
    }

    // Validate required documents based on document type
    if (!documents.id_front) {
      errors.id_front = 'ID front photo is required'
      isValid = false
    }

    if (currentDocType?.requires_back_photo && !documents.id_back) {
      errors.id_back = 'ID back photo is required'
      isValid = false
    }

    if (!documents.selfie) {
      errors.selfie = 'Selfie photo is required'
      isValid = false
    }

    if (!documents.address_proof) {
      errors.address_proof = 'Address proof document is required'
      isValid = false
    }

    setDocumentErrors(errors)

    if (!isValid && !error) {
      setError('Please fill in all required fields and upload all required documents')
    }

    return isValid
  }

  // Form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user || !validateForm()) return

    try {
      setSubmitting(true)
      setError('')

      // Prepare documents for upload
      const kycDocuments = {
        id_front: documents.id_front!,
        id_back: documents.id_back || null,
        selfie: documents.selfie!,
        address_proof: documents.address_proof!
      }

      await NewKYCService.submitKYCApplication(user.id, formData, kycDocuments as any)

      setSuccess('KYC application submitted successfully! We will review your documents and notify you of the result.')

      // Reload KYC data
      const [status, submission] = await Promise.all([
        NewKYCService.getUserKYCStatus(user.id),
        NewKYCService.getUserKYCSubmission(user.id)
      ])
      
      setKycStatus(status)
      setKycSubmission(submission)
      
      // Clear form
      setDocuments({})
      setDocumentPreviews({})
      setDocumentErrors({})
    } catch (err) {
      console.error('Error submitting KYC application:', err)
      setError(err instanceof Error ? err.message : 'Failed to submit KYC application')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary-blue mx-auto mb-4" />
          <p className="text-gray-600">Loading KYC verification...</p>
        </div>
      </div>
    )
  }

  const canSubmit = !kycSubmission || kycSubmission.status === 'rejected'
  const isVerified = kycStatus?.kyc_status === 'approved'

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-blue/10 rounded-full mb-4">
            <Shield className="h-8 w-8 text-primary-blue" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Identity Verification</h1>
          <p className="text-gray-600">Complete your KYC verification to unlock all features</p>
        </div>

        {/* Status Card */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Verification Status</h3>
              <div className="flex items-center mt-2">
                {isVerified ? (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-green-700 font-medium">Verified</span>
                  </>
                ) : kycStatus?.kyc_status === 'pending' ? (
                  <>
                    <Loader2 className="h-5 w-5 text-yellow-500 mr-2 animate-spin" />
                    <span className="text-yellow-700 font-medium">Under Review</span>
                  </>
                ) : kycStatus?.kyc_status === 'rejected' ? (
                  <>
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-red-700 font-medium">Rejected</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-5 w-5 text-gray-500 mr-2" />
                    <span className="text-gray-700 font-medium">Not Submitted</span>
                  </>
                )}
              </div>
            </div>
            {kycStatus?.kyc_submitted_at && (
              <div className="text-right text-sm text-gray-500">
                <p>Submitted: {new Date(kycStatus.kyc_submitted_at).toLocaleDateString()}</p>
                {kycStatus.kyc_approved_at && (
                  <p>Approved: {new Date(kycStatus.kyc_approved_at).toLocaleDateString()}</p>
                )}
              </div>
            )}
          </div>
          
          {kycSubmission?.rejection_reason && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800 font-medium">Rejection Reason:</p>
              <p className="text-red-700 mt-1">{kycSubmission.rejection_reason}</p>
            </div>
          )}
        </div>

        {/* KYC Form */}
        {canSubmit && (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {kycSubmission?.status === 'rejected' ? 'Resubmit Verification Documents' : 'Submit Verification Documents'}
            </h2>

            <form ref={formRef} onSubmit={handleSubmit} className="space-y-8">
              {/* Personal Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        id="full_name"
                        type="text"
                        value={formData.full_name}
                        onChange={handleInputChange('full_name')}
                        placeholder="Enter your full name as per ID"
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="date_of_birth" className="block text-sm font-medium text-gray-700 mb-2">
                      Date of Birth
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        id="date_of_birth"
                        type="date"
                        value={formData.date_of_birth}
                        onChange={handleInputChange('date_of_birth')}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="id_document_type" className="block text-sm font-medium text-gray-700 mb-2">
                      ID Document Type *
                    </label>
                    <select
                      id="id_document_type"
                      value={formData.id_document_type}
                      onChange={handleInputChange('id_document_type')}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      required
                    >
                      {documentTypes.map(type => (
                        <option key={type.id} value={type.id}>
                          {type.name}
                        </option>
                      ))}
                    </select>
                    {currentDocType && (
                      <p className="mt-1 text-sm text-gray-500">{currentDocType.description}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="id_document_number" className="block text-sm font-medium text-gray-700 mb-2">
                      ID Document Number
                    </label>
                    <div className="relative">
                      <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        id="id_document_number"
                        type="text"
                        value={formData.id_document_number}
                        onChange={handleInputChange('id_document_number')}
                        placeholder="Enter ID number"
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                    Address *
                  </label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <textarea
                      id="address"
                      value={formData.address}
                      onChange={handleInputChange('address')}
                      placeholder="Enter your full address"
                      rows={3}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Document Upload Section */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Required Documents</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                  {/* ID Front Photo */}
                  <DocumentUploadCard
                    title="ID Document (Front)"
                    description="Clear photo of the front side of your ID"
                    required={true}
                    file={documents.id_front}
                    preview={documentPreviews.id_front}
                    error={documentErrors.id_front}
                    onUpload={handleDocumentUpload('id_front')}
                    onRemove={removeDocument('id_front')}
                    accept="image/*"
                    icon={<CreditCard className="h-8 w-8" />}
                  />

                  {/* ID Back Photo - Only for National ID and Driving License */}
                  {currentDocType?.requires_back_photo && (
                    <DocumentUploadCard
                      title="ID Document (Back)"
                      description="Clear photo of the back side of your ID"
                      required={true}
                      file={documents.id_back}
                      preview={documentPreviews.id_back}
                      error={documentErrors.id_back}
                      onUpload={handleDocumentUpload('id_back')}
                      onRemove={removeDocument('id_back')}
                      accept="image/*"
                      icon={<CreditCard className="h-8 w-8" />}
                    />
                  )}

                  {/* Selfie Photo */}
                  <DocumentUploadCard
                    title="Selfie Photo"
                    description="Clear selfie holding your ID document next to your face"
                    required={true}
                    file={documents.selfie}
                    preview={documentPreviews.selfie}
                    error={documentErrors.selfie}
                    onUpload={handleDocumentUpload('selfie')}
                    onRemove={removeDocument('selfie')}
                    accept="image/*"
                    icon={<Camera className="h-8 w-8" />}
                  />

                  {/* Address Proof */}
                  <DocumentUploadCard
                    title="Address Proof"
                    description="Recent utility bill, bank statement, or official document showing your address"
                    required={true}
                    file={documents.address_proof}
                    preview={documentPreviews.address_proof}
                    error={documentErrors.address_proof}
                    onUpload={handleDocumentUpload('address_proof')}
                    onRemove={removeDocument('address_proof')}
                    accept="image/*,application/pdf"
                    icon={<FileText className="h-8 w-8" />}
                  />
                </div>
              </div>

              {/* Additional Notes */}
              <div>
                <label htmlFor="submission_notes" className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes (Optional)
                </label>
                <textarea
                  id="submission_notes"
                  value={formData.submission_notes}
                  onChange={handleInputChange('submission_notes')}
                  placeholder="Any additional information you'd like to provide"
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>

              {/* Error/Success Messages */}
              <AnimatePresence>
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center"
                  >
                    <AlertCircle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" />
                    <p className="text-red-800">{error}</p>
                  </motion.div>
                )}

                {success && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="p-4 bg-green-50 border border-green-200 rounded-lg flex items-center"
                  >
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <p className="text-green-800">{success}</p>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={submitting}
                  className="px-8 py-3 bg-primary-blue text-white font-medium rounded-lg hover:bg-primary-blue/90 focus:ring-2 focus:ring-primary-blue focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {submitting ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin mr-2" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Upload className="h-5 w-5 mr-2" />
                      Submit for Verification
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Already Verified Message */}
        {isVerified && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-900 mb-2">Identity Verified!</h3>
            <p className="text-green-700">Your identity has been successfully verified. You now have access to all features.</p>
          </div>
        )}
      </div>
    </div>
  )
}

// Document Upload Card Component
interface DocumentUploadCardProps {
  title: string
  description: string
  required: boolean
  file?: File
  preview?: string
  error?: string
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
  onRemove: () => void
  accept: string
  icon: React.ReactNode
}

function DocumentUploadCard({
  title,
  description,
  required,
  file,
  preview,
  error,
  onUpload,
  onRemove,
  accept,
  icon
}: DocumentUploadCardProps) {
  const inputRef = useRef<HTMLInputElement>(null)

  return (
    <div className={`border-2 border-dashed rounded-lg p-6 transition-colors ${
      error ? 'border-red-300 bg-red-50' :
      file ? 'border-green-300 bg-green-50' :
      'border-gray-300 hover:border-primary-blue hover:bg-gray-50'
    }`}>
      <div className="text-center">
        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${
          error ? 'bg-red-100 text-red-600' :
          file ? 'bg-green-100 text-green-600' :
          'bg-gray-100 text-gray-600'
        }`}>
          {icon}
        </div>

        <h4 className="text-sm font-medium text-gray-900 mb-1">
          {title} {required && <span className="text-red-500">*</span>}
        </h4>
        <p className="text-xs text-gray-500 mb-4">{description}</p>

        {file ? (
          <div className="space-y-3">
            {preview && (
              <div className="relative inline-block">
                <img
                  src={preview}
                  alt="Preview"
                  className="w-20 h-20 object-cover rounded-lg border"
                />
              </div>
            )}
            <div className="flex items-center justify-center space-x-2">
              <span className="text-sm text-green-700 font-medium">{file.name}</span>
              <button
                type="button"
                onClick={onRemove}
                className="p-1 text-red-500 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <button
              type="button"
              onClick={() => inputRef.current?.click()}
              className="text-sm text-primary-blue hover:text-primary-blue/80"
            >
              Change File
            </button>
          </div>
        ) : (
          <button
            type="button"
            onClick={() => inputRef.current?.click()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:ring-2 focus:ring-primary-blue focus:ring-offset-2"
          >
            <Upload className="h-4 w-4 mr-2" />
            Choose File
          </button>
        )}

        <input
          ref={inputRef}
          type="file"
          accept={accept}
          onChange={onUpload}
          className="hidden"
        />

        {error && (
          <p className="mt-2 text-sm text-red-600">{error}</p>
        )}
      </div>
    </div>
  )
}
