"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx":
/*!****************************************************!*\
  !*** ./src/components/kyc/KYCVerificationForm.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KYCVerificationForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/kyc */ \"(app-pages-browser)/./src/lib/services/kyc.ts\");\n/* harmony import */ var _KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./KYCStatusBadge */ \"(app-pages-browser)/./src/components/kyc/KYCStatusBadge.tsx\");\n/* harmony import */ var _KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./KYCDocumentUpload */ \"(app-pages-browser)/./src/components/kyc/KYCDocumentUpload.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_premium__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/premium */ \"(app-pages-browser)/./src/components/ui/premium/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCVerificationForm(param) {\n    let { onSubmissionComplete } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // KYC Status\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [kycSubmission, setKycSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Form Data\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id_document_type: \"national_id\",\n        id_document_number: \"\",\n        full_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        submission_notes: \"\"\n    });\n    // Document Files\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [documentErrors, setDocumentErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Document Types\n    const [documentTypes, setDocumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchKYCData();\n            fetchDocumentTypes();\n        }\n    }, [\n        user\n    ]);\n    const fetchKYCData = useCallback(async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [status, submission] = await Promise.all([\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCStatus(user.id),\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCSubmission(user.id)\n            ]);\n            setKycStatus(status);\n            setKycSubmission(submission);\n            // Pre-fill form with existing submission data\n            if (submission) {\n                setFormData({\n                    id_document_type: submission.id_document_type,\n                    id_document_number: submission.id_document_number || \"\",\n                    full_name: submission.full_name,\n                    date_of_birth: submission.date_of_birth || \"\",\n                    address: submission.address,\n                    submission_notes: submission.submission_notes || \"\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching KYC data:\", error);\n            setError(\"Failed to load KYC information\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        user\n    ]);\n    const fetchDocumentTypes = useCallback(async ()=>{\n        try {\n            const types = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getKYCDocumentTypes();\n            setDocumentTypes(types);\n        } catch (error) {\n            console.error(\"Error fetching document types:\", error);\n        }\n    }, []);\n    const handleInputChange = useCallback((field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setError(\"\");\n    }, []);\n    const handleDocumentSelect = (documentType, file)=>{\n        if (file) {\n            setDocuments((prev)=>({\n                    ...prev,\n                    [documentType]: file\n                }));\n            setDocumentErrors((prev)=>({\n                    ...prev,\n                    [documentType]: \"\"\n                }));\n        } else {\n            setDocuments((prev)=>{\n                const newDocs = {\n                    ...prev\n                };\n                delete newDocs[documentType];\n                return newDocs;\n            });\n        }\n    };\n    const validateForm = ()=>{\n        const errors = {};\n        let isValid = true;\n        // Validate required fields\n        if (!formData.full_name.trim()) {\n            setError(\"Full name is required\");\n            isValid = false;\n        }\n        if (!formData.address.trim()) {\n            setError(\"Address is required\");\n            isValid = false;\n        }\n        // Validate required documents\n        const requiredDocs = [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ];\n        for (const docType of requiredDocs){\n            if (!documents[docType]) {\n                errors[docType] = \"This document is required\";\n                isValid = false;\n            }\n        }\n        setDocumentErrors(errors);\n        if (!isValid && !error) {\n            setError(\"Please fill in all required fields and upload all required documents\");\n        }\n        return isValid;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!user || !validateForm()) return;\n        try {\n            setSubmitting(true);\n            setError(\"\");\n            const kycDocuments = {\n                id_front: documents.id_front,\n                id_back: documents.id_back,\n                selfie: documents.selfie,\n                address_proof: documents.address_proof\n            };\n            await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.submitKYCApplication(user.id, formData, kycDocuments);\n            setSuccess(\"KYC application submitted successfully! We will review your documents and notify you of the result.\");\n            // Refresh KYC data\n            await fetchKYCData();\n            if (onSubmissionComplete) {\n                onSubmissionComplete();\n            }\n            // Clear form\n            setDocuments({});\n            setDocumentErrors({});\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to submit KYC application\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this);\n    }\n    const canSubmit = !kycSubmission || kycSubmission.status === \"rejected\";\n    const isVerified = (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) === \"approved\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCStatusCard, {\n                    status: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\",\n                    submittedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_submitted_at,\n                    approvedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_approved_at,\n                    rejectionReason: kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.rejection_reason\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                delay: 0.1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Verification Progress\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCProgress, {\n                            currentStatus: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            canSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.SlideInUp, {\n                delay: 0.2,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-primary-blue/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-primary-blue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit KYC Documents\" : \"Submit KYC Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Complete your identity verification to unlock all features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Full Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    value: formData.full_name,\n                                                    onChange: (e)=>handleInputChange(\"full_name\", e.target.value),\n                                                    placeholder: \"Enter your full name as per ID\",\n                                                    required: true,\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Date of Birth\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"date\",\n                                                    value: formData.date_of_birth,\n                                                    onChange: (e)=>handleInputChange(\"date_of_birth\", e.target.value),\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Type *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.id_document_type,\n                                                    onChange: (e)=>handleInputChange(\"id_document_type\", e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                    required: true,\n                                                    children: documentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type.id,\n                                                            children: type.name\n                                                        }, type.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    value: formData.id_document_number,\n                                                    onChange: (e)=>handleInputChange(\"id_document_number\", e.target.value),\n                                                    placeholder: \"Enter ID number\",\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Address *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.address,\n                                            onChange: (e)=>handleInputChange(\"address\", e.target.value),\n                                            placeholder: \"Enter your full address\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Required Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_front\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_front\", file),\n                                                    selectedFile: documents.id_front,\n                                                    error: documentErrors.id_front,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_back\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_back\", file),\n                                                    selectedFile: documents.id_back,\n                                                    error: documentErrors.id_back,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"selfie\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"selfie\", file),\n                                                    selectedFile: documents.selfie,\n                                                    error: documentErrors.selfie,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"address_proof\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"address_proof\", file),\n                                                    selectedFile: documents.address_proof,\n                                                    error: documentErrors.address_proof,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.submission_notes,\n                                            onChange: (e)=>handleInputChange(\"submission_notes\", e.target.value),\n                                            placeholder: \"Any additional information you'd like to provide\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                    children: [\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, this),\n                                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-green-600 bg-green-50 border border-green-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: success\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumButton, {\n                                        type: \"submit\",\n                                        disabled: submitting,\n                                        className: \"px-8 py-3\",\n                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 23\n                                                }, this),\n                                                (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit Documents\" : \"Submit for Verification\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCVerificationForm, \"K8399oXm369bol1ByL3bxOwt+HQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = KYCVerificationForm;\nvar _c;\n$RefreshReg$(_c, \"KYCVerificationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx\n"));

/***/ })

});