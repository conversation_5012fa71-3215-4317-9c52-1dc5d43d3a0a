import { supabase, TABLES } from '@/lib/supabase'
import { KYCStorageService } from './kycStorage'

// Updated types based on flexible schema
export interface KYCSubmission {
  id: string
  user_id: string
  id_document_front_url: string
  id_document_back_url?: string // Optional for passport
  selfie_photo_url: string
  address_proof_url: string
  id_document_type: 'national_id' | 'passport' | 'driving_license'
  id_document_number?: string
  full_name: string
  date_of_birth?: string
  address: string
  status: 'pending' | 'under_review' | 'approved' | 'rejected'
  submission_notes?: string
  reviewed_by?: string
  reviewed_at?: string
  rejection_reason?: string
  admin_notes?: string
  created_at: string
  updated_at: string
}

export interface KYCSubmissionData {
  id_document_type: 'national_id' | 'passport' | 'driving_license'
  id_document_number?: string
  full_name: string
  date_of_birth?: string
  address: string
  submission_notes?: string
}

export interface KYCDocuments {
  id_front: File
  id_back?: File | null // Optional for passport
  selfie: File
  address_proof: File
}

export interface KYCDocumentType {
  id: string
  name: string
  description: string
  is_active: boolean
  created_at: string
}

export interface KYCStatus {
  kyc_status: string
  kyc_submitted_at?: string
  kyc_approved_at?: string
}

export class NewKYCService {
  /**
   * Get user's KYC status from users table
   */
  static async getUserKYCStatus(userId: string): Promise<KYCStatus | null> {
    try {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .select('kyc_status, kyc_submitted_at, kyc_approved_at')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user KYC status:', error)
        return { kyc_status: 'not_submitted' }
      }

      return data || { kyc_status: 'not_submitted' }
    } catch (error) {
      console.error('Error getting user KYC status:', error)
      return { kyc_status: 'not_submitted' }
    }
  }

  /**
   * Get user's KYC submission details
   */
  static async getUserKYCSubmission(userId: string): Promise<KYCSubmission | null> {
    try {
      const { data, error } = await supabase
        .from(TABLES.KYC_SUBMISSIONS)
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          // No submission found
          return null
        }
        throw new Error(`Failed to fetch KYC submission: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error getting user KYC submission:', error)
      return null
    }
  }

  /**
   * Get available KYC document types
   */
  static async getKYCDocumentTypes(): Promise<KYCDocumentType[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.KYC_DOCUMENT_TYPES)
        .select('*')
        .eq('is_active', true)
        .order('id')

      if (error) {
        throw new Error(`Failed to fetch document types: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error getting KYC document types:', error)
      return []
    }
  }

  /**
   * Submit KYC application with flexible document requirements
   */
  static async submitKYCApplication(
    userId: string,
    submissionData: KYCSubmissionData,
    documents: KYCDocuments
  ): Promise<KYCSubmission> {
    try {
      // Validate document requirements based on type
      this.validateDocumentRequirements(submissionData.id_document_type, documents)

      // Check if user already has a submission
      const existingSubmission = await this.getUserKYCSubmission(userId)
      if (existingSubmission && existingSubmission.status !== 'rejected') {
        throw new Error('You already have a pending or approved KYC submission')
      }

      // Upload documents to storage
      const documentUrls = await this.uploadKYCDocuments(userId, documents)

      // Prepare submission data
      const kycSubmissionData = {
        user_id: userId,
        id_document_front_url: documentUrls.id_front,
        id_document_back_url: documentUrls.id_back || null,
        selfie_photo_url: documentUrls.selfie,
        address_proof_url: documentUrls.address_proof,
        id_document_type: submissionData.id_document_type,
        id_document_number: submissionData.id_document_number || null,
        full_name: submissionData.full_name,
        date_of_birth: submissionData.date_of_birth || null,
        address: submissionData.address,
        submission_notes: submissionData.submission_notes || null,
        status: 'pending' as const
      }

      // Insert or update submission
      let result
      if (existingSubmission) {
        // Update existing rejected submission
        const { data, error } = await supabase
          .from(TABLES.KYC_SUBMISSIONS)
          .update(kycSubmissionData)
          .eq('user_id', userId)
          .select()
          .single()

        if (error) throw error
        result = data
      } else {
        // Insert new submission
        const { data, error } = await supabase
          .from(TABLES.KYC_SUBMISSIONS)
          .insert(kycSubmissionData)
          .select()
          .single()

        if (error) throw error
        result = data
      }

      console.log('KYC application submitted successfully:', result)
      return result
    } catch (error) {
      console.error('Error submitting KYC application:', error)
      throw error
    }
  }

  /**
   * Validate document requirements based on document type
   */
  private static validateDocumentRequirements(
    documentType: string,
    documents: KYCDocuments
  ): void {
    // All document types require front, selfie, and address proof
    if (!documents.id_front) {
      throw new Error('ID front photo is required')
    }
    if (!documents.selfie) {
      throw new Error('Selfie photo is required')
    }
    if (!documents.address_proof) {
      throw new Error('Address proof document is required')
    }

    // National ID and Driving License require back photo
    if ((documentType === 'national_id' || documentType === 'driving_license') && !documents.id_back) {
      throw new Error('ID back photo is required for this document type')
    }

    // Passport doesn't require back photo
    if (documentType === 'passport' && documents.id_back) {
      console.log('Note: Back photo not required for passport, but will be stored if provided')
    }
  }

  /**
   * Upload KYC documents to storage
   */
  private static async uploadKYCDocuments(
    userId: string,
    documents: KYCDocuments
  ): Promise<Record<string, string>> {
    try {
      const uploadPromises: Promise<{ type: string; url: string }>[] = []

      // Upload front document
      uploadPromises.push(
        KYCStorageService.uploadKYCDocument(documents.id_front, userId, 'id_front')
          .then(url => ({ type: 'id_front', url }))
      )

      // Upload back document if provided
      if (documents.id_back) {
        uploadPromises.push(
          KYCStorageService.uploadKYCDocument(documents.id_back, userId, 'id_back')
            .then(url => ({ type: 'id_back', url }))
        )
      }

      // Upload selfie
      uploadPromises.push(
        KYCStorageService.uploadKYCDocument(documents.selfie, userId, 'selfie')
          .then(url => ({ type: 'selfie', url }))
      )

      // Upload address proof
      uploadPromises.push(
        KYCStorageService.uploadKYCDocument(documents.address_proof, userId, 'address_proof')
          .then(url => ({ type: 'address_proof', url }))
      )

      const uploadResults = await Promise.all(uploadPromises)

      // Convert to object
      const documentUrls: Record<string, string> = {}
      uploadResults.forEach(result => {
        documentUrls[result.type] = result.url
      })

      return documentUrls
    } catch (error) {
      console.error('Error uploading KYC documents:', error)
      throw new Error('Failed to upload documents. Please try again.')
    }
  }

  /**
   * Get KYC submission history for user
   */
  static async getKYCStatusHistory(userId: string) {
    try {
      const { data, error } = await supabase
        .from(TABLES.KYC_STATUS_HISTORY)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        throw new Error(`Failed to fetch KYC status history: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error getting KYC status history:', error)
      return []
    }
  }

  /**
   * Check if user is KYC verified
   */
  static async isUserKYCVerified(userId: string): Promise<boolean> {
    try {
      const status = await this.getUserKYCStatus(userId)
      return status?.kyc_status === 'approved'
    } catch (error) {
      console.error('Error checking KYC verification status:', error)
      return false
    }
  }
}
