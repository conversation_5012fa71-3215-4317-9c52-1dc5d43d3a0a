"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/shop/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/shop/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/shop/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MyShopPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Camera,CheckCircle,Clock,Edit,Eye,Image,Package,Plus,Star,Store,Trash2,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/vendorShops */ \"(app-pages-browser)/./src/lib/services/vendorShops.ts\");\n/* harmony import */ var _lib_services_shopProducts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/shopProducts */ \"(app-pages-browser)/./src/lib/services/shopProducts.ts\");\n/* harmony import */ var _lib_services_orders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/orders */ \"(app-pages-browser)/./src/lib/services/orders.ts\");\n/* harmony import */ var _lib_services_storage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/services/storage */ \"(app-pages-browser)/./src/lib/services/storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx\");\n/* harmony import */ var _components_orders_TrackingModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/orders/TrackingModal */ \"(app-pages-browser)/./src/components/orders/TrackingModal.tsx\");\n/* harmony import */ var _components_dashboard_MerchantWallet__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/MerchantWallet */ \"(app-pages-browser)/./src/components/dashboard/MerchantWallet.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction MyShopPage() {\n    var _selectedShop_rating, _selectedOrder_buyer, _selectedOrder_order_items;\n    _s();\n    const { user, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [shops, setShops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedShop, setSelectedShop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [shopProducts, setShopProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [shopOrders, setShopOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [shopStats, setShopStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalProducts: 0,\n        totalViews: 0,\n        totalFollowers: 0,\n        totalSales: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrderDetails, setShowOrderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTrackingModal, setShowTrackingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderToShip, setOrderToShip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [trackingLoading, setTrackingLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingBanner, setUploadingBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadingLogo, setUploadingLogo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchUserShops = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id)) {\n            setError(\"User not authenticated\");\n            setLoading(false);\n            return;\n        }\n        try {\n            setLoading(true);\n            const userShops = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_5__.VendorShopService.getUserShops(user.id);\n            setShops(userShops);\n            if (userShops.length > 0) {\n                setSelectedShop(userShops[0]);\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load shops\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchShopProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id)) {\n            setShopProducts([]);\n            return;\n        }\n        try {\n            const { products } = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_5__.VendorShopService.getShopProducts(selectedShop.id, 1, 20);\n            setShopProducts(products);\n        } catch (err) {\n            console.error(\"Error fetching shop products:\", err);\n            // Don't throw error, just log it and continue with empty products\n            setShopProducts([]);\n        }\n    }, [\n        selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id\n    ]);\n    const fetchShopOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id) || !(user === null || user === void 0 ? void 0 : user.id)) {\n            setShopOrders([]);\n            return;\n        }\n        try {\n            const { orders } = await _lib_services_orders__WEBPACK_IMPORTED_MODULE_7__.OrderService.getSellerOrders(user.id, selectedShop.id, 1, 20);\n            setShopOrders(orders);\n        } catch (err) {\n            console.error(\"Error fetching shop orders:\", err);\n            // Don't throw error, just log it and continue with empty orders\n            setShopOrders([]);\n        }\n    }, [\n        selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    const fetchShopStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id)) {\n            setShopStats({\n                totalProducts: 0,\n                totalViews: 0,\n                totalFollowers: 0,\n                totalSales: 0\n            });\n            return;\n        }\n        try {\n            // Refresh shop statistics from database\n            const refreshedShop = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_5__.VendorShopService.refreshShopStatistics(selectedShop.id);\n            // Get followers count with error handling\n            let followersCount = 0;\n            try {\n                followersCount = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_5__.VendorShopService.getShopFollowersCount(selectedShop.id);\n            } catch (followersError) {\n                console.warn(\"Failed to get followers count, using 0:\", followersError);\n            }\n            // Update only the statistics state\n            setShopStats({\n                totalProducts: refreshedShop.total_products || 0,\n                totalViews: refreshedShop.total_views || 0,\n                totalFollowers: followersCount,\n                totalSales: refreshedShop.total_sales || 0\n            });\n        } catch (err) {\n            console.error(\"Error fetching shop stats:\", err);\n            // Fallback to existing data if refresh fails\n            let followersCount = 0;\n            try {\n                followersCount = await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_5__.VendorShopService.getShopFollowersCount(selectedShop.id);\n            } catch (followersError) {\n                console.warn(\"Failed to get followers count in fallback, using 0:\", followersError);\n            }\n            setShopStats({\n                totalProducts: selectedShop.total_products || 0,\n                totalViews: selectedShop.total_views || 0,\n                totalFollowers: followersCount,\n                totalSales: selectedShop.total_sales || 0\n            });\n        }\n    }, [\n        selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id\n    ]) // Only depend on shop ID, not the entire shop object\n    ;\n    // useEffect hooks - placed after all function declarations to avoid hoisting issues\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchUserShops();\n        }\n    }, [\n        user\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedShop) {\n            fetchShopProducts();\n            fetchShopOrders();\n            fetchShopStats();\n        }\n    }, [\n        selectedShop === null || selectedShop === void 0 ? void 0 : selectedShop.id,\n        fetchShopProducts,\n        fetchShopOrders,\n        fetchShopStats\n    ]) // Only trigger when shop ID changes\n    ;\n    const handleViewProduct = (productId)=>{\n        window.open(\"/product/\".concat(productId), \"_blank\");\n    };\n    const handleEditProduct = (productId)=>{\n        // TODO: Create edit product page\n        window.open(\"/dashboard/shop/products/edit/\".concat(productId), \"_blank\");\n    };\n    const handleDeleteProduct = async (productId, productTitle)=>{\n        const confirmed = await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showConfirmation)({\n            title: \"Delete Product\",\n            message: 'Are you sure you want to delete \"'.concat(productTitle, '\"? This action cannot be undone.'),\n            variant: \"danger\",\n            confirmText: \"Delete\"\n        });\n        if (confirmed) {\n            try {\n                await _lib_services_shopProducts__WEBPACK_IMPORTED_MODULE_6__.ShopProductService.deleteProduct(productId);\n                await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                    title: \"Success\",\n                    message: \"Product deleted successfully!\",\n                    variant: \"success\"\n                });\n                // Refresh the products list\n                fetchShopProducts();\n            } catch (error) {\n                await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                    title: \"Error\",\n                    message: error instanceof Error ? error.message : \"Failed to delete product\",\n                    variant: \"danger\"\n                });\n            }\n        }\n    };\n    const handleViewOrder = (order)=>{\n        setSelectedOrder(order);\n        setShowOrderDetails(true);\n    };\n    const handleUpdateOrderStatus = async (orderId, newStatus)=>{\n        // If marking as shipped, show tracking modal\n        if (newStatus === \"shipped\") {\n            const order = shopOrders.find((o)=>o.id === orderId);\n            if (order) {\n                setOrderToShip(order);\n                setShowTrackingModal(true);\n                return;\n            }\n        }\n        // Special handling for cancellation\n        if (newStatus === \"cancelled\") {\n            const confirmed = await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showConfirmation)({\n                title: \"Cancel Order\",\n                message: \"Are you sure you want to cancel this order? The payment will be refunded to the buyer's wallet.\",\n                confirmText: \"Cancel Order\",\n                cancelText: \"Keep Order\",\n                variant: \"danger\"\n            });\n            if (!confirmed) return;\n            try {\n                await _lib_services_orders__WEBPACK_IMPORTED_MODULE_7__.OrderService.cancelOrderWithRefund(orderId, (user === null || user === void 0 ? void 0 : user.id) || \"\");\n                await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                    title: \"Success\",\n                    message: \"Order cancelled and refund processed successfully!\",\n                    variant: \"success\"\n                });\n                fetchShopOrders();\n            } catch (error) {\n                await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                    title: \"Error\",\n                    message: error instanceof Error ? error.message : \"Failed to cancel order\",\n                    variant: \"danger\"\n                });\n            }\n            return;\n        }\n        const confirmed = await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showConfirmation)({\n            title: \"Update Order Status\",\n            message: 'Are you sure you want to update the order status to \"'.concat(newStatus.replace(\"_\", \" \"), '\"?'),\n            confirmText: \"Update\",\n            cancelText: \"Cancel\",\n            variant: \"warning\"\n        });\n        if (!confirmed) return;\n        try {\n            await _lib_services_orders__WEBPACK_IMPORTED_MODULE_7__.OrderService.updateOrderStatus(orderId, newStatus, undefined, undefined, undefined, user === null || user === void 0 ? void 0 : user.id);\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Success\",\n                message: \"Order status updated successfully!\",\n                variant: \"success\"\n            });\n            fetchShopOrders();\n        } catch (error) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Error\",\n                message: error instanceof Error ? error.message : \"Failed to update order status\",\n                variant: \"danger\"\n            });\n        }\n    };\n    const handleAddTracking = async (trackingData)=>{\n        if (!orderToShip) return;\n        try {\n            setTrackingLoading(true);\n            await _lib_services_orders__WEBPACK_IMPORTED_MODULE_7__.OrderService.updateOrderStatus(orderToShip.id, \"shipped\", trackingData.trackingNumber, trackingData.trackingUrl, \"Shipped via \".concat(trackingData.courierService), user === null || user === void 0 ? void 0 : user.id, trackingData.courierService);\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Success\",\n                message: \"Order marked as shipped with tracking information!\",\n                variant: \"success\"\n            });\n            setShowTrackingModal(false);\n            setOrderToShip(null);\n            fetchShopOrders();\n        } catch (error) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Error\",\n                message: error instanceof Error ? error.message : \"Failed to update order status\",\n                variant: \"danger\"\n            });\n        } finally{\n            setTrackingLoading(false);\n        }\n    };\n    const getOrderStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"pending_shipment\":\n                return \"text-orange-600 bg-orange-100\";\n            case \"confirmed\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"processing\":\n                return \"text-purple-600 bg-purple-100\";\n            case \"shipped\":\n                return \"text-indigo-600 bg-indigo-100\";\n            case \"delivered\":\n                return \"text-green-600 bg-green-100\";\n            case \"cancelled\":\n                return \"text-red-600 bg-red-100\";\n            case \"refunded\":\n                return \"text-gray-600 bg-gray-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"approved\":\n                return \"text-green-600 bg-green-100\";\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"rejected\":\n                return \"text-red-600 bg-red-100\";\n            case \"suspended\":\n                return \"text-gray-600 bg-gray-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 16\n                }, this);\n            case \"suspended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const handleBannerUpload = async (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file || !selectedShop || !(user === null || user === void 0 ? void 0 : user.id)) return;\n        // Validate file type and size\n        if (!file.type.startsWith(\"image/\")) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Invalid File\",\n                message: \"Please select an image file.\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        if (file.size > 5 * 1024 * 1024) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"File Too Large\",\n                message: \"Image size must be less than 5MB.\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        try {\n            setUploadingBanner(true);\n            const bannerUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_8__.StorageService.uploadImage(file, user.id);\n            await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_5__.VendorShopService.updateShop(selectedShop.id, {\n                banner_url: bannerUrl\n            });\n            // Update the selected shop with new banner\n            setSelectedShop((prev)=>prev ? {\n                    ...prev,\n                    banner_url: bannerUrl\n                } : null);\n            // Update shops list\n            setShops((prev)=>prev.map((shop)=>shop.id === selectedShop.id ? {\n                        ...shop,\n                        banner_url: bannerUrl\n                    } : shop));\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Success\",\n                message: \"Shop banner updated successfully!\",\n                variant: \"success\"\n            });\n        } catch (error) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Error\",\n                message: error instanceof Error ? error.message : \"Failed to upload banner\",\n                variant: \"danger\"\n            });\n        } finally{\n            setUploadingBanner(false);\n        }\n    };\n    const handleLogoUpload = async (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file || !selectedShop || !(user === null || user === void 0 ? void 0 : user.id)) return;\n        // Validate file type and size\n        if (!file.type.startsWith(\"image/\")) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Invalid File\",\n                message: \"Please select an image file.\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        if (file.size > 2 * 1024 * 1024) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"File Too Large\",\n                message: \"Logo size must be less than 2MB.\",\n                variant: \"danger\"\n            });\n            return;\n        }\n        try {\n            setUploadingLogo(true);\n            const logoUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_8__.StorageService.uploadImage(file, user.id);\n            await _lib_services_vendorShops__WEBPACK_IMPORTED_MODULE_5__.VendorShopService.updateShop(selectedShop.id, {\n                logo_url: logoUrl\n            });\n            // Update the selected shop with new logo\n            setSelectedShop((prev)=>prev ? {\n                    ...prev,\n                    logo_url: logoUrl\n                } : null);\n            // Update shops list\n            setShops((prev)=>prev.map((shop)=>shop.id === selectedShop.id ? {\n                        ...shop,\n                        logo_url: logoUrl\n                    } : shop));\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Success\",\n                message: \"Shop logo updated successfully!\",\n                variant: \"success\"\n            });\n        } catch (error) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_10__.showAlert)({\n                title: \"Error\",\n                message: error instanceof Error ? error.message : \"Failed to upload logo\",\n                variant: \"danger\"\n            });\n        } finally{\n            setUploadingLogo(false);\n        }\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 501,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n            lineNumber: 500,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-600 mb-4\",\n                children: \"Please sign in to access your shop\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 509,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n            lineNumber: 508,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600 mb-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 517,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchUserShops,\n                    className: \"px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n            lineNumber: 516,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"My Shop\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage your vendor shop and products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedShop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-primary-blue\",\n                                                children: selectedShop.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(selectedShop.status)),\n                                                children: [\n                                                    getStatusIcon(selectedShop.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 capitalize\",\n                                                        children: selectedShop.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, this),\n                            shops.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/create-shop\",\n                                    className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Create Shop\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, this),\n                    shops.length === 0 ? /* No Shops State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"No shops yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Create your first shop to start selling products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/create-shop\",\n                                className: \"inline-flex items-center px-6 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create Your First Shop\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: selectedShop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex space-x-8 px-6\",\n                                            \"aria-label\": \"Tabs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(\"overview\"),\n                                                    className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"overview\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: \"Overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(\"products\"),\n                                                    className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"products\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: [\n                                                        \"Products (\",\n                                                        shopProducts.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(\"orders\"),\n                                                    className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"orders\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: [\n                                                        \"Orders (\",\n                                                        shopOrders.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(\"wallet\"),\n                                                    className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"wallet\" ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: \"Merchant Wallet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 17\n                                }, this),\n                                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 bg-gradient-to-r from-primary-blue to-secondary-blue\",\n                                                children: [\n                                                    selectedShop.banner_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: selectedShop.banner_url,\n                                                        alt: \"\".concat(selectedShop.name, \" banner\"),\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-75\",\n                                                                    children: \"No banner image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 644,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"cursor-pointer\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: handleBannerUpload,\n                                                                    className: \"hidden\",\n                                                                    disabled: uploadingBanner\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center px-3 py-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors\",\n                                                                    children: [\n                                                                        uploadingBanner ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 663,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: uploadingBanner ? \"Uploading...\" : \"Change Banner\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 665,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-20 h-20 rounded-lg overflow-hidden bg-gray-200 border-4 border-white shadow-lg\",\n                                                                                children: selectedShop.logo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: selectedShop.logo_url,\n                                                                                    alt: \"\".concat(selectedShop.name, \" logo\"),\n                                                                                    className: \"w-full h-full object-cover\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 680,\n                                                                                    columnNumber: 31\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-full h-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"h-8 w-8 text-gray-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                        lineNumber: 687,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 686,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                lineNumber: 678,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"absolute -bottom-2 -right-2 cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"file\",\n                                                                                        accept: \"image/*\",\n                                                                                        onChange: handleLogoUpload,\n                                                                                        className: \"hidden\",\n                                                                                        disabled: uploadingLogo\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                        lineNumber: 694,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-8 h-8 bg-primary-blue text-white rounded-full flex items-center justify-center hover:bg-primary-blue/90 transition-colors shadow-lg\",\n                                                                                        children: uploadingLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-3 w-3 border border-white border-t-transparent\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 703,\n                                                                                            columnNumber: 33\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-3 w-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 705,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                        lineNumber: 701,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                lineNumber: 693,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 677,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                                children: selectedShop.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                lineNumber: 712,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: selectedShop.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                lineNumber: 713,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Created\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: new Date(selectedShop.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 729,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-blue-700\",\n                                                                                    children: \"Products\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 731,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-2xl font-bold text-blue-900\",\n                                                                                    children: shopStats.totalProducts\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 732,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 738,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-green-700\",\n                                                                                    children: \"Views\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 740,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-2xl font-bold text-green-900\",\n                                                                                    children: shopStats.totalViews\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 741,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-purple-700\",\n                                                                                    children: \"Sales\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 749,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-2xl font-bold text-purple-900\",\n                                                                                    children: shopStats.totalSales\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 750,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-yellow-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-yellow-700\",\n                                                                                    children: \"Rating\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 758,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-2xl font-bold text-yellow-900\",\n                                                                                    children: ((_selectedShop_rating = selectedShop.rating) === null || _selectedShop_rating === void 0 ? void 0 : _selectedShop_rating.toFixed(1)) || \"0.0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 759,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 757,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false),\n                                activeTab === \"products\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Products\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 mt-1\",\n                                                                    children: \"Manage your shop products\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 778,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/dashboard/shop/products/add\",\n                                                            className: \"flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Add Product\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 19\n                                            }, this),\n                                            shopProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                        children: \"No products yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6\",\n                                                        children: \"Start adding products to your shop to begin selling\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/dashboard/shop/products/add\",\n                                                        className: \"inline-flex items-center px-6 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Add Your First Product\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"divide-y divide-gray-200\",\n                                                children: shopProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0\",\n                                                                    children: product.images && product.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: product.images[0].image_url,\n                                                                        alt: product.title,\n                                                                        className: \"w-full h-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 811,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full h-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-8 w-8 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 818,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 817,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 809,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                                            children: product.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 827,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-gray-600 text-sm line-clamp-2 mb-2\",\n                                                                                            children: product.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 830,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: [\n                                                                                                        \"SKU: \",\n                                                                                                        product.sku || \"N/A\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 834,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"•\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 835,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"capitalize\",\n                                                                                                    children: product.condition\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 836,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"•\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 837,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: [\n                                                                                                        \"Stock: \",\n                                                                                                        product.stock_quantity\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 838,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"•\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 839,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: [\n                                                                                                        \"Views: \",\n                                                                                                        product.views || 0\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                                    lineNumber: 840,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 833,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 826,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-right ml-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-2xl font-bold text-primary-blue mb-1\",\n                                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(product.price || 0)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 844,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(product.status === \"active\" ? \"bg-green-100 text-green-800\" : product.status === \"inactive\" ? \"bg-gray-100 text-gray-800\" : product.status === \"out_of_stock\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                                            children: product.status.replace(\"_\", \" \")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 847,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 843,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 825,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleViewProduct(product.id),\n                                                                                    className: \"flex items-center px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                                                    title: \"View Product\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 868,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"View\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 863,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleEditProduct(product.id),\n                                                                                    className: \"flex items-center px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors\",\n                                                                                    title: \"Edit Product\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 876,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"Edit\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 871,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDeleteProduct(product.id, product.title),\n                                                                                    className: \"flex items-center px-3 py-1.5 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors\",\n                                                                                    title: \"Delete Product\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 884,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"Delete\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 879,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 862,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, product.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false),\n                                activeTab === \"orders\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900\",\n                                                            children: \"Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mt-1\",\n                                                            children: \"Manage your shop orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                lineNumber: 902,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 21\n                                        }, this),\n                                        shopOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                    children: \"No orders yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Orders will appear here when customers purchase your products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"divide-y divide-gray-200\",\n                                            children: shopOrders.map((order)=>{\n                                                var _order_buyer, _order_order_items;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                \"Order #\",\n                                                                                order.order_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                (_order_buyer = order.buyer) === null || _order_buyer === void 0 ? void 0 : _order_buyer.full_name,\n                                                                                \" • \",\n                                                                                new Date(order.created_at).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 925,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 921,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-lg font-bold text-gray-900\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(order.total_amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 930,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium \".concat(getOrderStatusColor(order.status)),\n                                                                            children: order.status.replace(\"_\", \" \").charAt(0).toUpperCase() + order.status.replace(\"_\", \" \").slice(1)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 933,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 929,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 920,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                ((_order_order_items = order.order_items) === null || _order_order_items === void 0 ? void 0 : _order_order_items.length) || 0,\n                                                                                \" item(s)\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 941,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: order.payment_method.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 943,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        order.tracking_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 946,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Tracking: \",\n                                                                                        order.tracking_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 947,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleViewOrder(order),\n                                                                            className: \"flex items-center px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 956,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"View\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 952,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        order.status !== \"delivered\" && order.status !== \"cancelled\" && order.status !== \"refunded\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: order.status,\n                                                                            onChange: (e)=>handleUpdateOrderStatus(order.id, e.target.value),\n                                                                            className: \"text-sm border border-gray-300 rounded-lg px-2 py-1.5 focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                                            children: [\n                                                                                order.status === \"pending_shipment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"pending_shipment\",\n                                                                                            children: \"Pending Shipment\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 967,\n                                                                                            columnNumber: 41\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"shipped\",\n                                                                                            children: \"Ship Order\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 968,\n                                                                                            columnNumber: 41\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"cancelled\",\n                                                                                            children: \"Cancel Order\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                            lineNumber: 969,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true),\n                                                                                order.status === \"shipped\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"shipped\",\n                                                                                    children: \"Shipped\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 973,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 960,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 951,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, order.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 900,\n                                    columnNumber: 19\n                                }, this),\n                                activeTab === \"wallet\" && selectedShop && (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MerchantWallet__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    shopId: selectedShop.id,\n                                    userId: user.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 988,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 530,\n                columnNumber: 7\n            }, this),\n            showOrderDetails && selectedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: [\n                                                \"Order #\",\n                                                selectedOrder.order_number\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1005,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                (_selectedOrder_buyer = selectedOrder.buyer) === null || _selectedOrder_buyer === void 0 ? void 0 : _selectedOrder_buyer.full_name,\n                                                \" • \",\n                                                new Date(selectedOrder.created_at).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowOrderDetails(false),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Camera_CheckCircle_Clock_Edit_Eye_Image_Package_Plus_Star_Store_Trash2_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                        lineNumber: 1016,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1012,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                            lineNumber: 1003,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 overflow-y-auto max-h-[calc(90vh-120px)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Order Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1027,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getOrderStatusColor(selectedOrder.status)),\n                                                                    children: selectedOrder.status.replace(\"_\", \" \").charAt(0).toUpperCase() + selectedOrder.status.replace(\"_\", \" \").slice(1)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1028,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Payment:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1033,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: selectedOrder.payment_method.replace(\"_\", \" \")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1034,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1032,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Total:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1037,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(selectedOrder.total_amount)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1038,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1036,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Customer Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1048,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: selectedOrder.shipping_address.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1049,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1047,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1052,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: selectedOrder.shipping_address.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1053,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Address:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1056,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2 mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: selectedOrder.shipping_address.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 1058,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                selectedOrder.shipping_address.city,\n                                                                                \", \",\n                                                                                selectedOrder.shipping_address.district\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 1059,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedOrder.shipping_address.postal_code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: selectedOrder.shipping_address.postal_code\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 1061,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1057,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1055,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1046,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Order Items\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1071,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: (_selectedOrder_order_items = selectedOrder.order_items) === null || _selectedOrder_order_items === void 0 ? void 0 : _selectedOrder_order_items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 p-3 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1075,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: item.product_title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1077,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"Qty: \",\n                                                                        item.quantity,\n                                                                        \" \\xd7 \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(item.unit_price)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 1078,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1076,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(item.total_price)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 1082,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 1074,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1072,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 15\n                                }, this),\n                                selectedOrder.buyer_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Customer Notes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1095,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 bg-gray-50 rounded-lg p-3\",\n                                            children: selectedOrder.buyer_notes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                            lineNumber: 1096,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                                    lineNumber: 1094,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                    lineNumber: 1002,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 1001,\n                columnNumber: 9\n            }, this),\n            showTrackingModal && orderToShip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_orders_TrackingModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showTrackingModal,\n                onClose: ()=>{\n                    setShowTrackingModal(false);\n                    setOrderToShip(null);\n                },\n                onSubmit: handleAddTracking,\n                loading: trackingLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n                lineNumber: 1108,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\shop\\\\page.tsx\",\n        lineNumber: 529,\n        columnNumber: 5\n    }, this);\n}\n_s(MyShopPage, \"1jl2SbiOmvASHeHW+jd+Bu+9eMY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = MyShopPage;\nvar _c;\n$RefreshReg$(_c, \"MyShopPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/shop/page.tsx\n"));

/***/ })

});