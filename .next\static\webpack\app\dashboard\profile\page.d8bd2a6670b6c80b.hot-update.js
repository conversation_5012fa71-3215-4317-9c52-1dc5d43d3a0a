"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx":
/*!****************************************************!*\
  !*** ./src/components/kyc/KYCVerificationForm.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KYCVerificationForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/kyc */ \"(app-pages-browser)/./src/lib/services/kyc.ts\");\n/* harmony import */ var _KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./KYCStatusBadge */ \"(app-pages-browser)/./src/components/kyc/KYCStatusBadge.tsx\");\n/* harmony import */ var _KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./KYCDocumentUpload */ \"(app-pages-browser)/./src/components/kyc/KYCDocumentUpload.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_premium__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/premium */ \"(app-pages-browser)/./src/components/ui/premium/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCVerificationForm(param) {\n    let { onSubmissionComplete } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // KYC Status\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [kycSubmission, setKycSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Form Data\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id_document_type: \"national_id\",\n        id_document_number: \"\",\n        full_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        submission_notes: \"\"\n    });\n    // Document Files\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [documentErrors, setDocumentErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Document Types\n    const [documentTypes, setDocumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchKYCData();\n            fetchDocumentTypes();\n        }\n    }, [\n        user\n    ]);\n    const fetchKYCData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [status, submission] = await Promise.all([\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCStatus(user.id),\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCSubmission(user.id)\n            ]);\n            setKycStatus(status);\n            setKycSubmission(submission);\n            // Pre-fill form with existing submission data\n            if (submission) {\n                setFormData({\n                    id_document_type: submission.id_document_type,\n                    id_document_number: submission.id_document_number || \"\",\n                    full_name: submission.full_name,\n                    date_of_birth: submission.date_of_birth || \"\",\n                    address: submission.address,\n                    submission_notes: submission.submission_notes || \"\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching KYC data:\", error);\n            setError(\"Failed to load KYC information\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        user\n    ]);\n    const fetchDocumentTypes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const types = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getKYCDocumentTypes();\n            setDocumentTypes(types);\n        } catch (error) {\n            console.error(\"Error fetching document types:\", error);\n        }\n    }, []);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setError(\"\");\n    }, []);\n    const handleDocumentSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((documentType, file)=>{\n        if (file) {\n            setDocuments((prev)=>({\n                    ...prev,\n                    [documentType]: file\n                }));\n            setDocumentErrors((prev)=>({\n                    ...prev,\n                    [documentType]: \"\"\n                }));\n        } else {\n            setDocuments((prev)=>{\n                const newDocs = {\n                    ...prev\n                };\n                delete newDocs[documentType];\n                return newDocs;\n            });\n        }\n    }, []);\n    const validateForm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const errors = {};\n        let isValid = true;\n        // Validate required fields\n        if (!formData.full_name.trim()) {\n            setError(\"Full name is required\");\n            isValid = false;\n        }\n        if (!formData.address.trim()) {\n            setError(\"Address is required\");\n            isValid = false;\n        }\n        // Validate required documents\n        const requiredDocs = [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ];\n        for (const docType of requiredDocs){\n            if (!documents[docType]) {\n                errors[docType] = \"This document is required\";\n                isValid = false;\n            }\n        }\n        setDocumentErrors(errors);\n        if (!isValid && !error) {\n            setError(\"Please fill in all required fields and upload all required documents\");\n        }\n        return isValid;\n    }, [\n        formData.full_name,\n        formData.address,\n        documents,\n        error\n    ]);\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (e)=>{\n        e.preventDefault();\n        if (!user || !validateForm()) return;\n        try {\n            setSubmitting(true);\n            setError(\"\");\n            const kycDocuments = {\n                id_front: documents.id_front,\n                id_back: documents.id_back,\n                selfie: documents.selfie,\n                address_proof: documents.address_proof\n            };\n            await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.submitKYCApplication(user.id, formData, kycDocuments);\n            setSuccess(\"KYC application submitted successfully! We will review your documents and notify you of the result.\");\n            // Refresh KYC data\n            await fetchKYCData();\n            if (onSubmissionComplete) {\n                onSubmissionComplete();\n            }\n            // Clear form\n            setDocuments({});\n            setDocumentErrors({});\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to submit KYC application\");\n        } finally{\n            setSubmitting(false);\n        }\n    }, [\n        user,\n        validateForm,\n        documents,\n        formData,\n        fetchKYCData,\n        onSubmissionComplete\n    ]);\n    // Memoize computed values to prevent unnecessary re-renders\n    const canSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>!kycSubmission || kycSubmission.status === \"rejected\", [\n        kycSubmission\n    ]);\n    const isVerified = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) === \"approved\", [\n        kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCStatusCard, {\n                    status: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\",\n                    submittedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_submitted_at,\n                    approvedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_approved_at,\n                    rejectionReason: kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.rejection_reason\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                delay: 0.1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Verification Progress\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCProgress, {\n                            currentStatus: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            canSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.SlideInUp, {\n                delay: 0.2,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-primary-blue/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-primary-blue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit KYC Documents\" : \"Submit KYC Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Complete your identity verification to unlock all features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Full Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    id: \"kyc-full-name\",\n                                                    type: \"text\",\n                                                    value: formData.full_name,\n                                                    onChange: (e)=>handleInputChange(\"full_name\", e.target.value),\n                                                    placeholder: \"Enter your full name as per ID\",\n                                                    required: true,\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Date of Birth\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"date\",\n                                                    value: formData.date_of_birth,\n                                                    onChange: (e)=>handleInputChange(\"date_of_birth\", e.target.value),\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Type *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.id_document_type,\n                                                    onChange: (e)=>handleInputChange(\"id_document_type\", e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                    required: true,\n                                                    children: documentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type.id,\n                                                            children: type.name\n                                                        }, type.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    value: formData.id_document_number,\n                                                    onChange: (e)=>handleInputChange(\"id_document_number\", e.target.value),\n                                                    placeholder: \"Enter ID number\",\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Address *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.address,\n                                            onChange: (e)=>handleInputChange(\"address\", e.target.value),\n                                            placeholder: \"Enter your full address\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Required Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_front\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_front\", file),\n                                                    selectedFile: documents.id_front,\n                                                    error: documentErrors.id_front,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_back\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_back\", file),\n                                                    selectedFile: documents.id_back,\n                                                    error: documentErrors.id_back,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"selfie\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"selfie\", file),\n                                                    selectedFile: documents.selfie,\n                                                    error: documentErrors.selfie,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"address_proof\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"address_proof\", file),\n                                                    selectedFile: documents.address_proof,\n                                                    error: documentErrors.address_proof,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.submission_notes,\n                                            onChange: (e)=>handleInputChange(\"submission_notes\", e.target.value),\n                                            placeholder: \"Any additional information you'd like to provide\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                    children: [\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, this),\n                                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-green-600 bg-green-50 border border-green-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: success\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumButton, {\n                                        type: \"submit\",\n                                        disabled: submitting,\n                                        className: \"px-8 py-3\",\n                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 23\n                                                }, this),\n                                                (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit Documents\" : \"Submit for Verification\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCVerificationForm, \"NksgwO5G1kpZzqYHGDng3YTISnY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = KYCVerificationForm;\nvar _c;\n$RefreshReg$(_c, \"KYCVerificationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx\n"));

/***/ })

});