"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/kyc/page",{

/***/ "(app-pages-browser)/./src/lib/services/kyc.ts":
/*!*********************************!*\
  !*** ./src/lib/services/kyc.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCService: function() { return /* binding */ KYCService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _kycStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kycStorage */ \"(app-pages-browser)/./src/lib/services/kycStorage.ts\");\n\n\nclass KYCService {\n    /**\n   * Get user's KYC status from users table\n   */ static async getUserKYCStatus(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"kyc_status, kyc_submitted_at, kyc_approved_at\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user KYC status:\", error);\n                // Return default status if columns don't exist yet\n                if (error.message.includes(\"column\") && error.message.includes(\"does not exist\")) {\n                    return {\n                        kyc_status: \"not_submitted\"\n                    };\n                }\n                throw new Error(\"Failed to fetch KYC status: \".concat(error.message));\n            }\n            return data || {\n                kyc_status: \"not_submitted\"\n            };\n        } catch (error) {\n            console.error(\"Error getting user KYC status:\", error);\n            // Return default status for any database errors\n            return {\n                kyc_status: \"not_submitted\"\n            };\n        }\n    }\n    /**\n   * Get user's KYC submission details\n   */ static async getUserKYCSubmission(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"*\").eq(\"user_id\", userId).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    // No submission found\n                    return null;\n                }\n                // Handle table not found error\n                if (error.message.includes(\"Could not find the table\") || error.message.includes(\"schema cache\")) {\n                    console.warn(\"KYC submissions table not found - database migration may be needed\");\n                    return null;\n                }\n                console.error(\"Error fetching KYC submission:\", error);\n                throw new Error(\"Failed to fetch KYC submission: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting user KYC submission:\", error);\n            // Return null for table not found errors instead of throwing\n            if (error instanceof Error && (error.message.includes(\"Could not find the table\") || error.message.includes(\"schema cache\"))) {\n                return null;\n            }\n            throw error;\n        }\n    }\n    /**\n   * Submit KYC application with documents\n   */ static async submitKYCApplication(userId, submissionData, documents) {\n        try {\n            // Check if user already has a submission\n            const existingSubmission = await this.getUserKYCSubmission(userId);\n            if (existingSubmission && existingSubmission.status !== \"rejected\") {\n                throw new Error(\"You already have a pending or approved KYC submission\");\n            }\n            // Prepare document uploads\n            const documentUploads = [\n                {\n                    file: documents.id_front,\n                    type: \"id_front\"\n                },\n                {\n                    file: documents.id_back,\n                    type: \"id_back\"\n                },\n                {\n                    file: documents.selfie,\n                    type: \"selfie\"\n                },\n                {\n                    file: documents.address_proof,\n                    type: \"address_proof\"\n                }\n            ];\n            // Upload all documents\n            console.log(\"Uploading KYC documents...\");\n            const uploadedDocuments = await _kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.uploadKYCDocuments(documentUploads, userId);\n            // If there's an existing rejected submission, delete it first\n            if (existingSubmission) {\n                await this.deleteKYCSubmission(existingSubmission.id);\n            }\n            // Create KYC submission record\n            const submissionRecord = {\n                user_id: userId,\n                id_document_front_url: uploadedDocuments.id_front,\n                id_document_back_url: uploadedDocuments.id_back,\n                selfie_photo_url: uploadedDocuments.selfie,\n                address_proof_url: uploadedDocuments.address_proof,\n                id_document_type: submissionData.id_document_type,\n                id_document_number: submissionData.id_document_number,\n                full_name: submissionData.full_name,\n                date_of_birth: submissionData.date_of_birth,\n                address: submissionData.address,\n                submission_notes: submissionData.submission_notes,\n                status: \"pending\"\n            };\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).insert(submissionRecord).select().single();\n            if (error) {\n                console.error(\"Error creating KYC submission:\", error);\n                throw new Error(\"Failed to submit KYC application: \".concat(error.message));\n            }\n            console.log(\"KYC application submitted successfully\");\n            return data;\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all KYC submissions for admin review\n   */ static async getAllKYCSubmissions() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, status = arguments.length > 2 ? arguments[2] : void 0, searchTerm = arguments.length > 3 ? arguments[3] : void 0;\n        try {\n            // First, try the direct join approach\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"\\n          *,\\n          user:users(full_name, email)\\n        \", {\n                count: \"exact\"\n            });\n            // Apply filters\n            if (status) {\n                query = query.eq(\"status\", status);\n            }\n            if (searchTerm) {\n                query = query.or(\"full_name.ilike.%\".concat(searchTerm, \"%,id_document_number.ilike.%\").concat(searchTerm, \"%\"));\n            }\n            // Apply pagination\n            const offset = (page - 1) * limit;\n            query = query.order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            const { data, error, count } = await query;\n            if (error) {\n                console.error(\"Error fetching KYC submissions with join:\", error);\n                // Fallback: Get submissions first, then get user data separately\n                console.log(\"Trying fallback approach...\");\n                let fallbackQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).select(\"*\", {\n                    count: \"exact\"\n                });\n                if (status) {\n                    fallbackQuery = fallbackQuery.eq(\"status\", status);\n                }\n                if (searchTerm) {\n                    fallbackQuery = fallbackQuery.or(\"full_name.ilike.%\".concat(searchTerm, \"%,id_document_number.ilike.%\").concat(searchTerm, \"%\"));\n                }\n                const fallbackOffset = (page - 1) * limit;\n                fallbackQuery = fallbackQuery.order(\"created_at\", {\n                    ascending: false\n                }).range(fallbackOffset, fallbackOffset + limit - 1);\n                const { data: submissions, error: submissionsError, count: submissionsCount } = await fallbackQuery;\n                if (submissionsError) {\n                    throw new Error(\"Failed to fetch KYC submissions: \".concat(submissionsError.message));\n                }\n                // Get user data for each submission\n                const submissionsWithUsers = await Promise.all((submissions || []).map(async (submission)=>{\n                    const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"full_name, email\").eq(\"id\", submission.user_id).single();\n                    if (userError) {\n                        console.warn(\"Could not fetch user data for submission \".concat(submission.id, \":\"), userError);\n                        return {\n                            ...submission,\n                            user: {\n                                full_name: \"Unknown User\",\n                                email: \"<EMAIL>\"\n                            }\n                        };\n                    }\n                    return {\n                        ...submission,\n                        user: userData\n                    };\n                }));\n                return {\n                    submissions: submissionsWithUsers,\n                    total: submissionsCount || 0\n                };\n            }\n            return {\n                submissions: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting all KYC submissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Review KYC submission (admin only)\n   */ static async reviewKYCSubmission(submissionId, reviewData, reviewerId) {\n        try {\n            const updateData = {\n                status: reviewData.status,\n                reviewed_by: reviewerId,\n                reviewed_at: new Date().toISOString(),\n                rejection_reason: reviewData.rejection_reason,\n                admin_notes: reviewData.admin_notes\n            };\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).update(updateData).eq(\"id\", submissionId).select().single();\n            if (error) {\n                console.error(\"Error reviewing KYC submission:\", error);\n                throw new Error(\"Failed to review KYC submission: \".concat(error.message));\n            }\n            console.log(\"KYC submission \".concat(submissionId, \" reviewed as \").concat(reviewData.status));\n            return data;\n        } catch (error) {\n            console.error(\"Error reviewing KYC submission:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get KYC status history for a user\n   */ static async getKYCStatusHistory(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_STATUS_HISTORY).select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching KYC status history:\", error);\n                throw new Error(\"Failed to fetch KYC status history: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC status history:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Check if user is KYC verified\n   */ static async isUserKYCVerified(userId) {\n        try {\n            const status = await this.getUserKYCStatus(userId);\n            return (status === null || status === void 0 ? void 0 : status.kyc_status) === \"approved\";\n        } catch (error) {\n            console.error(\"Error checking KYC verification status:\", error);\n            return false;\n        }\n    }\n    /**\n   * Get KYC document signed URLs for admin review\n   */ static async getKYCDocumentUrls(submission) {\n        try {\n            const filePaths = [\n                submission.id_document_front_url,\n                submission.id_document_back_url,\n                submission.selfie_photo_url,\n                submission.address_proof_url\n            ];\n            const signedUrls = await _kycStorage__WEBPACK_IMPORTED_MODULE_1__.KYCStorageService.getKYCDocumentSignedUrls(filePaths, 3600) // 1 hour expiry\n            ;\n            return {\n                id_front_url: signedUrls[submission.id_document_front_url],\n                id_back_url: signedUrls[submission.id_document_back_url],\n                selfie_url: signedUrls[submission.selfie_photo_url],\n                address_proof_url: signedUrls[submission.address_proof_url]\n            };\n        } catch (error) {\n            console.error(\"Error getting KYC document URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC submission (for resubmission)\n   */ static async deleteKYCSubmission(submissionId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_SUBMISSIONS).delete().eq(\"id\", submissionId);\n            if (error) {\n                console.error(\"Error deleting KYC submission:\", error);\n                throw new Error(\"Failed to delete KYC submission: \".concat(error.message));\n            }\n        } catch (error) {\n            console.error(\"Error deleting KYC submission:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get KYC document types\n   */ static async getKYCDocumentTypes() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.KYC_DOCUMENT_TYPES).select(\"*\").eq(\"is_active\", true).order(\"name\");\n            if (error) {\n                console.error(\"Error fetching KYC document types:\", error);\n                throw new Error(\"Failed to fetch document types: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting KYC document types:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kyc.ts\n"));

/***/ })

});