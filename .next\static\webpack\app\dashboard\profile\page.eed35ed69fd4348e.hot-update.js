"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx":
/*!****************************************************!*\
  !*** ./src/components/kyc/KYCVerificationForm.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KYCVerificationForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,CreditCard,Loader2,Shield,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/kyc */ \"(app-pages-browser)/./src/lib/services/kyc.ts\");\n/* harmony import */ var _KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./KYCStatusBadge */ \"(app-pages-browser)/./src/components/kyc/KYCStatusBadge.tsx\");\n/* harmony import */ var _KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./KYCDocumentUpload */ \"(app-pages-browser)/./src/components/kyc/KYCDocumentUpload.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_premium__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/premium */ \"(app-pages-browser)/./src/components/ui/premium/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCVerificationForm(param) {\n    let { onSubmissionComplete } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // KYC Status\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [kycSubmission, setKycSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Form Data\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id_document_type: \"national_id\",\n        id_document_number: \"\",\n        full_name: (user === null || user === void 0 ? void 0 : user.full_name) || \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        submission_notes: \"\"\n    });\n    // Document Files\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [documentErrors, setDocumentErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Document Types\n    const [documentTypes, setDocumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchKYCData();\n            fetchDocumentTypes();\n        }\n    }, [\n        user\n    ]);\n    const fetchKYCData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [status, submission] = await Promise.all([\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCStatus(user.id),\n                _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getUserKYCSubmission(user.id)\n            ]);\n            setKycStatus(status);\n            setKycSubmission(submission);\n            // Pre-fill form with existing submission data\n            if (submission) {\n                setFormData({\n                    id_document_type: submission.id_document_type,\n                    id_document_number: submission.id_document_number || \"\",\n                    full_name: submission.full_name,\n                    date_of_birth: submission.date_of_birth || \"\",\n                    address: submission.address,\n                    submission_notes: submission.submission_notes || \"\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching KYC data:\", error);\n            setError(\"Failed to load KYC information\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        user\n    ]);\n    const fetchDocumentTypes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const types = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getKYCDocumentTypes();\n            setDocumentTypes(types);\n        } catch (error) {\n            console.error(\"Error fetching document types:\", error);\n        }\n    }, []);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setError(\"\");\n    }, []);\n    const handleDocumentSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((documentType, file)=>{\n        if (file) {\n            setDocuments((prev)=>({\n                    ...prev,\n                    [documentType]: file\n                }));\n            setDocumentErrors((prev)=>({\n                    ...prev,\n                    [documentType]: \"\"\n                }));\n        } else {\n            setDocuments((prev)=>{\n                const newDocs = {\n                    ...prev\n                };\n                delete newDocs[documentType];\n                return newDocs;\n            });\n        }\n    }, []);\n    const validateForm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const errors = {};\n        let isValid = true;\n        // Validate required fields\n        if (!formData.full_name.trim()) {\n            setError(\"Full name is required\");\n            isValid = false;\n        }\n        if (!formData.address.trim()) {\n            setError(\"Address is required\");\n            isValid = false;\n        }\n        // Validate required documents\n        const requiredDocs = [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ];\n        for (const docType of requiredDocs){\n            if (!documents[docType]) {\n                errors[docType] = \"This document is required\";\n                isValid = false;\n            }\n        }\n        setDocumentErrors(errors);\n        if (!isValid && !error) {\n            setError(\"Please fill in all required fields and upload all required documents\");\n        }\n        return isValid;\n    }, [\n        formData.full_name,\n        formData.address,\n        documents,\n        error\n    ]);\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (e)=>{\n        e.preventDefault();\n        if (!user || !validateForm()) return;\n        try {\n            setSubmitting(true);\n            setError(\"\");\n            const kycDocuments = {\n                id_front: documents.id_front,\n                id_back: documents.id_back,\n                selfie: documents.selfie,\n                address_proof: documents.address_proof\n            };\n            await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.submitKYCApplication(user.id, formData, kycDocuments);\n            setSuccess(\"KYC application submitted successfully! We will review your documents and notify you of the result.\");\n            // Refresh KYC data\n            await fetchKYCData();\n            if (onSubmissionComplete) {\n                onSubmissionComplete();\n            }\n            // Clear form\n            setDocuments({});\n            setDocumentErrors({});\n        } catch (error) {\n            console.error(\"Error submitting KYC application:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to submit KYC application\");\n        } finally{\n            setSubmitting(false);\n        }\n    }, [\n        user,\n        validateForm,\n        documents,\n        formData,\n        fetchKYCData,\n        onSubmissionComplete\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this);\n    }\n    const canSubmit = !kycSubmission || kycSubmission.status === \"rejected\";\n    const isVerified = (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) === \"approved\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCStatusCard, {\n                    status: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\",\n                    submittedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_submitted_at,\n                    approvedAt: kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_approved_at,\n                    rejectionReason: kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.rejection_reason\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                delay: 0.1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Verification Progress\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__.KYCProgress, {\n                            currentStatus: (kycStatus === null || kycStatus === void 0 ? void 0 : kycStatus.kyc_status) || \"not_submitted\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            canSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.SlideInUp, {\n                delay: 0.2,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumCard, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-primary-blue/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-primary-blue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit KYC Documents\" : \"Submit KYC Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Complete your identity verification to unlock all features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Full Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    value: formData.full_name,\n                                                    onChange: (e)=>handleInputChange(\"full_name\", e.target.value),\n                                                    placeholder: \"Enter your full name as per ID\",\n                                                    required: true,\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Date of Birth\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"date\",\n                                                    value: formData.date_of_birth,\n                                                    onChange: (e)=>handleInputChange(\"date_of_birth\", e.target.value),\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Type *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.id_document_type,\n                                                    onChange: (e)=>handleInputChange(\"id_document_type\", e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                    required: true,\n                                                    children: documentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type.id,\n                                                            children: type.name\n                                                        }, type.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"ID Document Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    value: formData.id_document_number,\n                                                    onChange: (e)=>handleInputChange(\"id_document_number\", e.target.value),\n                                                    placeholder: \"Enter ID number\",\n                                                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Address *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.address,\n                                            onChange: (e)=>handleInputChange(\"address\", e.target.value),\n                                            placeholder: \"Enter your full address\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Required Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_front\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_front\", file),\n                                                    selectedFile: documents.id_front,\n                                                    error: documentErrors.id_front,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"id_back\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"id_back\", file),\n                                                    selectedFile: documents.id_back,\n                                                    error: documentErrors.id_back,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"selfie\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"selfie\", file),\n                                                    selectedFile: documents.selfie,\n                                                    error: documentErrors.selfie,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCDocumentUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    documentType: \"address_proof\",\n                                                    onFileSelect: (file)=>handleDocumentSelect(\"address_proof\", file),\n                                                    selectedFile: documents.address_proof,\n                                                    error: documentErrors.address_proof,\n                                                    disabled: submitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.submission_notes,\n                                            onChange: (e)=>handleInputChange(\"submission_notes\", e.target.value),\n                                            placeholder: \"Any additional information you'd like to provide\",\n                                            rows: 3,\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                    children: [\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, this),\n                                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"flex items-center space-x-2 text-green-600 bg-green-50 border border-green-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: success\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_7__.PremiumButton, {\n                                        type: \"submit\",\n                                        disabled: submitting,\n                                        className: \"px-8 py-3\",\n                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_CreditCard_Loader2_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 23\n                                                }, this),\n                                                (kycSubmission === null || kycSubmission === void 0 ? void 0 : kycSubmission.status) === \"rejected\" ? \"Resubmit Documents\" : \"Submit for Verification\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\kyc\\\\KYCVerificationForm.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCVerificationForm, \"RKmZC6kZcjhgFUIk/678ljiEqiw=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = KYCVerificationForm;\nvar _c;\n$RefreshReg$(_c, \"KYCVerificationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/kyc/KYCVerificationForm.tsx\n"));

/***/ })

});