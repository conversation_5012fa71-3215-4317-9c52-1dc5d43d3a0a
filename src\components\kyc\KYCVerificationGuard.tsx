'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Shield, AlertTriangle, ExternalLink, CheckCircle } from 'lucide-react'
import Link from 'next/link'
import { useKYCVerification } from '@/hooks/useKYCVerification'

interface KYCVerificationGuardProps {
  action: 'wallet_withdrawal' | 'p2p_transfer' | 'shop_creation'
  children: React.ReactNode
  fallback?: React.ReactNode
}

const actionLabels = {
  wallet_withdrawal: 'Wallet Withdrawals',
  p2p_transfer: 'P2P Transfers',
  shop_creation: 'Shop Creation'
}

const actionDescriptions = {
  wallet_withdrawal: 'withdraw funds from your wallet',
  p2p_transfer: 'send money to other users',
  shop_creation: 'create and manage your online shop'
}

export default function KYCVerificationGuard({ 
  action, 
  children, 
  fallback 
}: KYCVerificationGuardProps) {
  const { kycStatus, checkKYCRequired } = useKYCVerification()

  // Check if KYC is required for this action
  const verification = checkKYCRequired(action)

  // If <PERSON><PERSON><PERSON> is not required or user is verified, render children
  if (verification.allowed) {
    return <>{children}</>
  }

  // If custom fallback is provided, use it
  if (fallback) {
    return <>{fallback}</>
  }

  // Default KYC verification required UI
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-6"
    >
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
            <Shield className="h-6 w-6 text-yellow-600" />
          </div>
        </div>
        
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <h3 className="text-lg font-semibold text-gray-900">
              KYC Verification Required
            </h3>
          </div>
          
          <p className="text-gray-700 mb-4">
            To access <strong>{actionLabels[action]}</strong> and {actionDescriptions[action]}, 
            you need to complete your identity verification first. This helps us ensure the 
            security of your account and comply with financial regulations.
          </p>

          <div className="bg-white rounded-lg p-4 mb-4 border border-yellow-200">
            <h4 className="font-medium text-gray-900 mb-2">Current Status:</h4>
            <div className="flex items-center gap-2">
              {kycStatus.status === 'not_submitted' && (
                <>
                  <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                  <span className="text-gray-600">Not Submitted</span>
                </>
              )}
              {kycStatus.status === 'pending' && (
                <>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                  <span className="text-yellow-600">Under Review</span>
                </>
              )}
              {kycStatus.status === 'rejected' && (
                <>
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <span className="text-red-600">Rejected - Please Resubmit</span>
                </>
              )}
              {kycStatus.status === 'approved' && (
                <>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-green-600">Verified</span>
                </>
              )}
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <Link
              href="/dashboard/profile/kyc"
              className="inline-flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors font-medium"
            >
              <Shield className="h-4 w-4 mr-2" />
              {kycStatus.status === 'not_submitted' ? 'Start Verification' : 'View Status'}
              <ExternalLink className="h-4 w-4 ml-2" />
            </Link>
            
            <Link
              href="/help/kyc"
              className="inline-flex items-center justify-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

// Higher-order component for protecting entire pages
export function withKYCVerification<P extends object>(
  Component: React.ComponentType<P>,
  action: 'wallet_withdrawal' | 'p2p_transfer' | 'shop_creation'
) {
  return function KYCProtectedComponent(props: P) {
    return (
      <KYCVerificationGuard action={action}>
        <Component {...props} />
      </KYCVerificationGuard>
    )
  }
}

// Hook for programmatic KYC checks
export function useKYCGuard() {
  const { kycStatus, checkKYCRequired } = useKYCVerification()

  const requireKYCForAction = async (
    action: 'wallet_withdrawal' | 'p2p_transfer' | 'shop_creation'
  ): Promise<{ allowed: boolean; message?: string }> => {
    const verification = checkKYCRequired(action)
    
    if (!verification.allowed) {
      return {
        allowed: false,
        message: `KYC verification is required for ${actionLabels[action]}. Please complete your identity verification first.`
      }
    }

    return { allowed: true }
  }

  const getKYCStatusMessage = (): string => {
    switch (kycStatus.status) {
      case 'not_submitted':
        return 'Identity verification not submitted. Please complete KYC to access all features.'
      case 'pending':
        return 'Your identity verification is under review. We will notify you once it\'s processed.'
      case 'rejected':
        return 'Your identity verification was rejected. Please resubmit with correct documents.'
      case 'approved':
        return 'Your identity is verified. You have access to all features.'
      default:
        return 'Unknown verification status.'
    }
  }

  return {
    kycStatus,
    requireKYCForAction,
    getKYCStatusMessage,
    isKYCVerified: kycStatus.isVerified
  }
}

// Inline KYC status indicator component
export function KYCStatusIndicator({ className = '' }: { className?: string }) {
  const { kycStatus } = useKYCVerification()

  if (kycStatus.loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse"></div>
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    )
  }

  const statusConfig = {
    not_submitted: {
      color: 'text-gray-600',
      bgColor: 'bg-gray-400',
      label: 'Not Verified'
    },
    pending: {
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-400',
      label: 'Under Review'
    },
    rejected: {
      color: 'text-red-600',
      bgColor: 'bg-red-400',
      label: 'Rejected'
    },
    approved: {
      color: 'text-green-600',
      bgColor: 'bg-green-400',
      label: 'Verified'
    }
  }

  const config = statusConfig[kycStatus.status as keyof typeof statusConfig] || statusConfig.not_submitted

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className={`w-3 h-3 ${config.bgColor} rounded-full ${kycStatus.status === 'pending' ? 'animate-pulse' : ''}`}></div>
      <span className={`text-sm ${config.color}`}>{config.label}</span>
    </div>
  )
}
